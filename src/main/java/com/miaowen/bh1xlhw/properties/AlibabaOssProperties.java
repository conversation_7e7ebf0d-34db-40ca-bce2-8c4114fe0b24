package com.miaowen.bh1xlhw.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AlibabaOssProperties :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-03
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "oss")
public class AlibabaOssProperties {
    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String basePath;
}
