package com.miaowen.bh1xlhw.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AwsClientConfigProperties :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-03
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "cloudflare.r2")
public class AwsR2ConfigProperties {
    /**
     * // 从 Cloudflare 获取
     */
    private String accessKey;
    /**
     * 从 Cloudflare 获取
     */
    private String secretKey;
    /**
     * "https://" + ACCOUNT_ID + ".r2.cloudflarestorage.com"
     */
    private String endpoint;

    private String region;
    /**
     * 储存桶
     */
    private String bucketName;

    private String ossDomain;
}
