package com.miaowen.bh1xlhw.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AdminLogin :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-03-27
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "admin-login")
public class AdminLoginProperties {
    private String authKey;
    private Integer tokenExp = 86400;
    private Integer tokenValidExp = 86400;
    private String secretKey = "NkxKmMJ9I61b2GxjQbApInL//YobRAKZ7JHL1UzBzdA=";
}
