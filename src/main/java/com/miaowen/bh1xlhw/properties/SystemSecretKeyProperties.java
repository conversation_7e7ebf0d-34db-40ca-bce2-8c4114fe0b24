package com.miaowen.bh1xlhw.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 系统密钥
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-28
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "system-secret")
public class SystemSecretKeyProperties {
    /**
     * oa请求token
     */
    private String oaSecretToken;
    /**
     * oa请求结果加密密钥
     */
    private String oaSecretKey;
    /**
     * 邮箱token对称加密
     */
    private String emailTokenKey;
}
