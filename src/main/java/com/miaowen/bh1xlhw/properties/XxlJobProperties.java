package com.miaowen.bh1xlhw.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * XxlJobProperties :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-04
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "xxl.job")
public class XxlJobProperties {
    private Admin admin = new Admin();
    private String accessToken;
    private Executor executor = new Executor();

    @Data
    public static class Admin{
        private String addresses;
    }

    @Data
    public static class Executor{
        private String appname;
        private String address;
        private String ip;
        private Integer port;
        private String logpath;
        private Integer logretentiondays;
    }
}
