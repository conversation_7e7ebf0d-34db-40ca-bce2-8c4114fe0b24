package com.miaowen.bh1xlhw.api.oa.api;

import com.alibaba.fastjson2.JSONObject;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.model.query.oa.StatisticOrderForm;
import com.miaowen.bh1xlhw.model.vo.oa.*;
import com.miaowen.bh1xlhw.properties.SystemSecretKeyProperties;
import com.miaowen.bh1xlhw.service.oa.OaStatisticDataService;
import com.miaowen.bh1xlhw.utils.AesCryptoUtil;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * Controller :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@RestController
@RequestMapping("/public/oa")
@AllArgsConstructor
public class OaStatisticDataApi {
    private final OaStatisticDataService oaStatisticDataService;
    private final SystemSecretKeyProperties systemSecretKeyProperties;

    /**
     * 平台账号信息查询
     */
    @GetMapping("/platformAccounts")
    public ResultVO<String> platformAccounts(@RequestHeader(value = "secret-token",required = false) String secretToken,
                                             @RequestParam(value = "userCode", required = false) String userCode,
                                             @RequestParam(value = "reportType", required = false) int reportType) {
        dealSecretToken(secretToken);
        PlatformAccountListVO platformAccountListVO = oaStatisticDataService.platformAccounts(userCode, reportType);
        String response = AesCryptoUtil.generateUnsubscribeToken(systemSecretKeyProperties.getOaSecretKey(), JSONObject.toJSONString(platformAccountListVO));
        return ResultVO.success(response);
    }

    /**
     * 平台账号信息查询
     */
    @GetMapping("/getAllAgentPlatform")
    public ResultVO<String> getAllAgentPlatform(@RequestHeader(value = "secret-token",required = false) String secretToken) {
        dealSecretToken(secretToken);
        AgentPlatformListVO agentPlatformListVO = oaStatisticDataService.getAllAgentPlatform();
        String response = AesCryptoUtil.generateUnsubscribeToken(systemSecretKeyProperties.getOaSecretKey(), JSONObject.toJSONString(agentPlatformListVO));
        return ResultVO.success(response);
    }



    /**
     * 代理商信息查询
     */
    @GetMapping("/getAllAgent")
    public ResultVO<String> getAllAgent(@RequestHeader(value = "secret-token",required = false) String secretToken) {
        dealSecretToken(secretToken);
        AgentListVO agentPlatformListVO = oaStatisticDataService.getAllAgent();
        String response = AesCryptoUtil.generateUnsubscribeToken(systemSecretKeyProperties.getOaSecretKey(), JSONObject.toJSONString(agentPlatformListVO));
        return ResultVO.success(response);
    }

    /**
     * 退款数据查询
     * @param time 月份 2025-02
     */
    @GetMapping("/statistic/refund")
    public ResultVO<String> statisticRefund(@RequestHeader(value = "secret-token",required = false) String secretToken,
                                            @RequestParam(value = "time") String time) {
        dealSecretToken(secretToken);
        StatisticRefundListVO statisticRefundListVO = oaStatisticDataService.statisticRefund(time);
        String response = AesCryptoUtil.generateUnsubscribeToken(systemSecretKeyProperties.getOaSecretKey(), JSONObject.toJSONString(statisticRefundListVO)
        );
        return ResultVO.success(response);
    }

    /**
     * 代理商返点数据查询
     * @param agentIds 代理商id
     */
    @GetMapping("/rebate")
    public ResultVO<String> rebate(@RequestHeader(value = "secret-token",required = false) String secretToken,
                                   @RequestParam(value = "agentIds") String agentIds) {
        dealSecretToken(secretToken);
        RebateVO rebate = oaStatisticDataService.rebate(agentIds);
        String response = AesCryptoUtil.generateUnsubscribeToken(systemSecretKeyProperties.getOaSecretKey(), JSONObject.toJSONString(rebate));
        return ResultVO.success(response);
    }

    /**
     * 查询订单统计数据
     */
    @GetMapping("/statistic/order")
    public ResultVO<String> statisticOrder(@RequestHeader(value = "secret-token",required = false) String secretToken,
                                           @ModelAttribute @Validated StatisticOrderForm statisticOrderForm) {
        dealSecretToken(secretToken);
        //productName 未兼容
        StatisticOrderListVO statisticOrderListVo = oaStatisticDataService.statisticOrder(statisticOrderForm);
        String response = AesCryptoUtil.generateUnsubscribeToken(systemSecretKeyProperties.getOaSecretKey(), JSONObject.toJSONString(statisticOrderListVo));
        return ResultVO.success(response);
    }





    public void dealSecretToken(String secretToken) {
        String oaSecretToken = systemSecretKeyProperties.getOaSecretToken();
        if (!oaSecretToken.equals(secretToken)) {
            throw new BizException(ResultEnum.API_PERMISSIONS);
        }
    }
}
