package com.miaowen.bh1xlhw.controller.article;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.article.ArticleAddForm;
import com.miaowen.bh1xlhw.model.query.article.ArticlePageForm;
import com.miaowen.bh1xlhw.model.vo.article.ArticleVo;
import com.miaowen.bh1xlhw.service.article.ArticleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文章管理
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/23 15:37
 */
@Slf4j
@RestController
@RequestMapping("/article")
public class ArticleController {

    @Resource
    private ArticleService articleService;

    /**
     * 新增或更新
     *
     * @param form
     * @return
     */
    @PreAuthorize("hasAuthority('isystem:article-manage:multilingual:add') " +
        "or hasAuthority('isystem:article-manage:multilingual:edit') " +
        "or hasAuthority('isystem:article-manage:complex:add')" +
        "or hasAuthority('isystem:article-manage:complex:edit')")
    @PostMapping("/addOrUpdate")
    ResultVO<Void> addOrUpdate(@RequestBody ArticleAddForm form) {
        articleService.saveOrUpdate(form);
        return ResultVO.success();
    }

    /**
     * 批量删除
     *
     * @param
     * @return
     */
    @PreAuthorize("hasAuthority('isystem:article-manage:multilingual:del') " +
        "or hasAuthority('isystem:article-manage:complex:del')")
    @DeleteMapping("/delete")
    ResultVO<Void> delete(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        articleService.deleteById(deleteBatchForm.getIds());
        return ResultVO.success();
    }

    /**
     * 多语言-分页查询
     *
     * @param form
     * @return
     */
    @PreAuthorize("hasAuthority('isystem:article-manage:multilingual:page')")
    @GetMapping("/multi/page")
    ResultVO<PageVO<ArticleVo>> pageMulti(@ModelAttribute ArticlePageForm form) {
        return ResultVO.success(articleService.page(form, true));
    }

    /**
     * 繁体-分页查询
     *
     * @param form
     * @return
     */
    @PreAuthorize("hasAuthority('isystem:article-manage:complex:page')")
    @GetMapping("/traditional/page")
    ResultVO<PageVO<ArticleVo>> pageTraditional(@ModelAttribute ArticlePageForm form) {
        return ResultVO.success(articleService.page(form, false));
    }

    /**
     * 启用禁用
     *
     * @param id
     * @param status
     * @return
     */
    @PreAuthorize("hasAuthority('isystem:article-manage:multilingual:edit') " +
        "or hasAuthority('isystem:article-manage:complex:edit')")
    @PostMapping("/updateStatus")
    ResultVO<Void> updateStatus(@RequestParam("id") Integer id, @RequestParam("status") Integer status) {
        articleService.updateStatus(id, status);
        return ResultVO.success();
    }


    /**
     * 通过id获取详情
     *
     * @param id
     * @return
     */
    @PreAuthorize("hasAuthority('isystem:article-manage:multilingual:detail') " +
        "or hasAuthority('isystem:article-manage:complex:detail')")
    @GetMapping("/getById")
    ResultVO<ArticleAddForm> getById(@RequestParam("id") Integer id) {
        return ResultVO.success(articleService.getById(id));
    }

}
