package com.miaowen.bh1xlhw.controller.adManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.adManagement.AdBackfillDeleteForm;
import com.miaowen.bh1xlhw.model.query.adManagement.AdBackfillForm;
import com.miaowen.bh1xlhw.model.query.adManagement.AdSendForm;
import com.miaowen.bh1xlhw.model.vo.adManagement.AdBackfillRecordVo;
import com.miaowen.bh1xlhw.service.adManagement.AdBackfillRecordFormService;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Date;

/**
 * 广告管理-广告回传记录
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/12 9:16
 */
@RestController
@RequestMapping("/adBackfill")
@AllArgsConstructor
public class AdBackfillRecordController {

    private final AdBackfillRecordFormService adBackfillRecordFormService;

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('ad-manage:ad-log:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<AdBackfillRecordVo>> page(@ModelAttribute AdBackfillForm form) {
        return ResultVO.success(adBackfillRecordFormService.page(form));
    }

    /**
     * 根据id删除
     *
     * @param adBackfillDeleteForm 删除表单
     * @return ResultVO<Void> 返回结果
     */
    
    @PreAuthorize("hasAuthority('ad-manage:ad-log:del')")
    @DeleteMapping("/delete")
    public ResultVO<Void> delete(@RequestBody AdBackfillDeleteForm adBackfillDeleteForm) {
        adBackfillRecordFormService.delete(adBackfillDeleteForm);
        return ResultVO.success();
    }

    /**
     * 发送广告
     *
     */
    @PostMapping("/send")
    public ResultVO<String> sendAd(@RequestBody AdSendForm form) {
        return ResultVO.success(adBackfillRecordFormService.sendAd(form));
    }

}
