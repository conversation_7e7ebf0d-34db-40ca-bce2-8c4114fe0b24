package com.miaowen.bh1xlhw.controller.merchant;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.merchant.MerchantForm;
import com.miaowen.bh1xlhw.model.query.merchant.MerchantPageForm;
import com.miaowen.bh1xlhw.model.query.merchant.PayConfigForm;
import com.miaowen.bh1xlhw.model.vo.merchant.MerchantPageVO;
import com.miaowen.bh1xlhw.service.merchant.MerchantService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统-商户配置
 *
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/merchant")
@AllArgsConstructor
public class MerchantController {

    private final MerchantService merchantService;

    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated MerchantForm merchantForm) {
        merchantService.saveOrUpdateMerchant(merchantForm);
        return ResultVO.success();
    }

    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        merchantService.deleteBatch(deleteBatchForm);
        return ResultVO.success();
    }

    @GetMapping("/page")
    public ResultVO<PageVO<MerchantPageVO>> page(@ModelAttribute @Validated MerchantPageForm form) {
        return ResultVO.success(merchantService.pageMerchant(form));
    }

    @GetMapping("/listAll")
    public ResultVO<?> listAll() {
        return ResultVO.success(merchantService.listAll());
    }

    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        merchantService.updateState(form);
        return ResultVO.success();
    }

    /**
     * 批量删除 回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @DeleteMapping("/recycles")
    public ResultVO<?> recycles(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        merchantService.deleteRecycles(deleteBatchForm);
        return ResultVO.success();
    }
    /**
     * 批量批量恢复回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @PutMapping("/recycles")
    public ResultVO<?> recoverRecycles(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        merchantService.recoverRecycles(deleteBatchForm);
        return ResultVO.success();
    }


    @PutMapping("/uploadPayConfig")
    public ResultVO<?> uploadPayConfig(@RequestBody @Validated PayConfigForm form) {
        merchantService.uploadPayConfig(form);
        return ResultVO.success();
    }
}
