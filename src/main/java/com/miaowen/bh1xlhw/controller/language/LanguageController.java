package com.miaowen.bh1xlhw.controller.language;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.language.LanguageForm;
import com.miaowen.bh1xlhw.model.query.language.LanguagePageForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePageVO;
import com.miaowen.bh1xlhw.service.language.LanguageService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * 系统-语言模块
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/language")
@AllArgsConstructor
public class LanguageController {

    private final LanguageService languageService;

    

    @PreAuthorize("hasAuthority('system:lang-manage:add')")
    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated LanguageForm languageForm) {
        return languageService.saveOrUpdateLanguage(languageForm);
    }

    

    @PreAuthorize("hasAuthority('system:lang-manage:del')")
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        languageService.deleteBatch(deleteBatchForm);
        return ResultVO.success();
    }

    

    @PreAuthorize("hasAuthority('system:lang-manage:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<LanguagePageVO>> page(@ModelAttribute @Validated LanguagePageForm form) {
        return ResultVO.success(languageService.pageLanguage(form));
    }

    

    @PreAuthorize("hasAuthority('system:lang-manage:listAll')")
    @GetMapping("/listAll")
    public ResultVO<?> listAll() {
        return ResultVO.success(languageService.listAll());
    }

    

    @PreAuthorize("hasAuthority('system:lang-manage:edit')")
    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        languageService.updateState(form);
        return ResultVO.success();
    }

    /**
     * 修改是否默认
     */
    
    @PreAuthorize("hasAuthority('system:lang-manage:updateDefault')")
    @PutMapping("/updateDefault")
    public ResultVO<?> updateDefault(@RequestBody @Validated UpdateIsDefaultForm form) {
        languageService.updateDefault(form);
        return ResultVO.success();
    }
}
