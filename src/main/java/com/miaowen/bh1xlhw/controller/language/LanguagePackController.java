package com.miaowen.bh1xlhw.controller.language;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePackVO;
import com.miaowen.bh1xlhw.service.language.LanguagePackService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * @Description 语言包模块
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/languagePack")
@AllArgsConstructor
public class LanguagePackController {

    private final LanguagePackService languagePackService;

    /**
     * 获取语言包
     */
    @GetMapping("/listAll")
    public ResultVO<LanguagePackVO> listAll() {
        return ResultVO.success(languagePackService.listAll());
    }

    /**
     * 设置语言包
     */
    @PostMapping("/setLanguagePack")
    public ResultVO<?> setLanguagePack(@RequestBody @Validated LanguagePackVO vo) {
        languagePackService.setLanguagePack(vo);
        return ResultVO.success();
    }
}
