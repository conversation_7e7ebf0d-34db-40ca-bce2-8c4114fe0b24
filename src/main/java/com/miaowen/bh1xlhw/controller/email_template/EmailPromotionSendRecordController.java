package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionSendRecordForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailPromotionSendRecordVo;
import com.miaowen.bh1xlhw.service.email_template.EmailPromotionSendRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * 推广邮件记录
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 19:09
 */
@Slf4j
@RestController
@RequestMapping("/emailPromotionSendRecord")
public class EmailPromotionSendRecordController {

    @Autowired
    private EmailPromotionSendRecordService emailPromotionSendRecordService;

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('email-publish:push-record:promotion:page')")
    @PostMapping("/page")
    public ResultVO<PageVO<EmailPromotionSendRecordVo>> page(@RequestBody EmailPromotionSendRecordForm form) {
        return ResultVO.success(emailPromotionSendRecordService.page(form));
    }

}
