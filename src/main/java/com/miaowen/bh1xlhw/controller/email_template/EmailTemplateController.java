package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email_template.DetailsForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateAddForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplatePageForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailTemplateVo;
import com.miaowen.bh1xlhw.service.email_template.EmailTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;

/**
 * 通知邮箱模板
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/emailTemplate")
public class EmailTemplateController {

    @Resource
    EmailTemplateService emailTemplateService;


    /**
     * 新增或更新
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('email-publish:templates:notice:edit')")
    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated EmailTemplateAddForm form) {
        if (form.getEmailTemplate().getId() == null) {
            emailTemplateService.add(form);
        } else {
            emailTemplateService.update(form);
        }
        return ResultVO.success();
    }

    /**
     * 批量删除
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('email-publish:templates:notice:del')")
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        emailTemplateService.deleteBatch(form);
        return ResultVO.success();
    }

    /**
     * 根据id获取详情
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('email-publish:templates:notice:detail')")
    @GetMapping("/details")
    public ResultVO<?> details(@ModelAttribute @Validated DetailsForm form) {
        return ResultVO.success(emailTemplateService.getDetails(form));
    }

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('email-publish:templates:notice:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<EmailTemplateVo>> page(@ModelAttribute @Validated EmailTemplatePageForm form) {
        return ResultVO.success(emailTemplateService.page(form));
    }

    /**
     * 更新状态
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('email-publish:templates:notice:updateState')")
    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        emailTemplateService.updateState(form);
        return ResultVO.success();
    }

}
