package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateContentPageForm;
import com.miaowen.bh1xlhw.service.email_template.EmailTemplateContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;

/**
 * 通知邮箱模板
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/emailTemplateContent")
public class EmailTemplateContentController {

    @Resource
    EmailTemplateContentService iEmailTemplateContentService;

//    @PostMapping("/addOrUpdate")
//    public ResultVO<?> addOrUpdate(@RequestBody @Validated EmailTemplateContentForm emailTemplateContent) {
//        if (emailTemplateContent.getId() == null) {
//            iEmailTemplateContentService.add(emailTemplateContent);
//        } else {
//            iEmailTemplateContentService.update(emailTemplateContent);
//        }
//        return ResultVO.success();
//    }

    /**
     * 批量删除
     *
     * @param form
     * @return
     */
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        iEmailTemplateContentService.deleteBatch(form);
        return ResultVO.success();
    }

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    @GetMapping("/page")
    public ResultVO<?> page(@ModelAttribute @Validated EmailTemplateContentPageForm form) {
        return iEmailTemplateContentService.page(form);
    }

}
