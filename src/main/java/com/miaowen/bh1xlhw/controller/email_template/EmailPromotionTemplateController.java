package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email_template.DetailsForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionSendForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplateAddForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplatePageForm;
import com.miaowen.bh1xlhw.service.email_template.EmailPromotionTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 推广邮箱模板
 *
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/emailPromotionTemplate")
public class EmailPromotionTemplateController {

    @Resource
    EmailPromotionTemplateService emailPromotionTemplateService;

    /**
     * 新增或修改
     *
     * @param form
     * @return
     */
    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated EmailPromotionTemplateAddForm form) {
        if (form.getEmailPromotionTemplate().getId() == null) {
            emailPromotionTemplateService.add(form);
        } else {
            emailPromotionTemplateService.update(form);
        }
        return ResultVO.success();
    }

    /**
     * 批量删除
     *
     * @param form
     * @return
     */
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        emailPromotionTemplateService.deleteBatch(form);
        return ResultVO.success();
    }

    /**
     * 根据id获取详情
     *
     * @param form
     * @return
     */
    @GetMapping("/details")
    public ResultVO<?> details(@ModelAttribute @Validated DetailsForm form) {
        return ResultVO.success(emailPromotionTemplateService.getDetails(form));
    }

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    @GetMapping("/page")
    public ResultVO<?> page(@ModelAttribute @Validated EmailPromotionTemplatePageForm form) {
        return emailPromotionTemplateService.page(form);
    }

    /**
     * 修改状态
     *
     * @param form
     * @return
     */
    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        emailPromotionTemplateService.updateState(form);
        return ResultVO.success();
    }

    /**
     * 发送邮件
     *
     * @param form
     * @return
     */
    @PostMapping("/send")
    public ResultVO<?> send(@RequestBody @Validated EmailPromotionSendForm form) {
        emailPromotionTemplateService.send(form);
        return ResultVO.success();
    }

}
