package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplateContentPageForm;
import com.miaowen.bh1xlhw.service.email_template.EmailPromotionTemplateContentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 推广邮箱模板内容
 *
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/emailPromotionTemplateContent")
public class EmailPromotionTemplateContentController {

    @Resource
    EmailPromotionTemplateContentService iEmailPromotionTemplateContentService;

//    @PostMapping("/addOrUpdate")
//    public ResultVO<?> addOrUpdate(@RequestBody @Validated EmailPromotionTemplateContentForm emailPromotionTemplateContent) {
//        if (emailPromotionTemplateContent.getId() == null) {
//            iEmailPromotionTemplateContentService.add(emailPromotionTemplateContent);
//        } else {
//            iEmailPromotionTemplateContentService.update(emailPromotionTemplateContent);
//        }
//        return ResultVO.success();
//    }

    /**
     * 批量删除
     *
     * @param form
     * @return
     */
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        iEmailPromotionTemplateContentService.deleteBatch(form);
        return ResultVO.success();
    }

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    @GetMapping("/page")
    public ResultVO<?> page(@ModelAttribute @Validated EmailPromotionTemplateContentPageForm form) {
        return iEmailPromotionTemplateContentService.page(form);
    }

}
