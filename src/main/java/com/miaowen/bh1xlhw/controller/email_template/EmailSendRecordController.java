package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionSendRecordForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailSendRecordForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailPromotionSendRecordVo;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailSendRecordVo;
import com.miaowen.bh1xlhw.service.email_template.EmailPromotionSendRecordService;
import com.miaowen.bh1xlhw.service.email_template.EmailSendRecordService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.Date;
import java.util.List;

/**
 * 通知邮件记录
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 19:09
 */
@Slf4j
@RestController
@RequestMapping("/emailSendRecord")
public class EmailSendRecordController {

    @Autowired
    private EmailSendRecordService emailSendRecordService;

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('email-publish:push-record:notice:page')")
    @PostMapping("/page")
    public ResultVO<PageVO<EmailSendRecordVo>> page(@RequestBody EmailSendRecordForm form) {
        return ResultVO.success(emailSendRecordService.page(form));
    }

    /**
     * 未支付邮件发送记录- 根据订单id查询轨迹
     *
     * @param
     * @return
     */
    
    @PreAuthorize("hasAuthority('order-manage:unpaid-order:emailSendRecord')")
    @GetMapping("/listByOrderId")
    public ResultVO<List<EmailSendRecordVo>> page(@RequestParam("orderId") Integer orderId) {
        return ResultVO.success(emailSendRecordService.listByOrderId(orderId));
    }

}
