package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.email_template.EmailOrderUnpaidForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailOrderUnpaidVo;
import com.miaowen.bh1xlhw.service.email_template.EmailOrderUnpaidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * 未支付邮箱
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 17:07
 */
@Slf4j
@RestController
@RequestMapping("/emailOrderUnpaid")
public class EmailOrderUnpaidController {

    @Autowired
    private EmailOrderUnpaidService emailOrderUnpaidService;

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    
    @PreAuthorize("hasAuthority('order-manage:unpaid-email:page')")
    @PostMapping("/page")
    public ResultVO<PageVO<EmailOrderUnpaidVo>> page(@RequestBody EmailOrderUnpaidForm form) {
        return ResultVO.success(emailOrderUnpaidService.page(form));
    }

}
