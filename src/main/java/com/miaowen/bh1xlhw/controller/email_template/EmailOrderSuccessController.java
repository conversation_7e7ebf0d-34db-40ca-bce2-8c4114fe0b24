package com.miaowen.bh1xlhw.controller.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.email_template.EmailOrderSuccessForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailOrderSuccessVo;
import com.miaowen.bh1xlhw.service.email_template.EmailOrderSuccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 已支付邮箱
 *
 * @Author：huanglong
 * @Date：2025/5/21 17:07
 */
@Slf4j
@RestController
@RequestMapping("/emailOrderSuccess")
public class EmailOrderSuccessController {

    @Autowired
    private EmailOrderSuccessService emailOrderSuccessService;

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    @PostMapping("/page")
    public ResultVO<PageVO<EmailOrderSuccessVo>> page(@RequestBody EmailOrderSuccessForm form) {
        return ResultVO.success(emailOrderSuccessService.page(form));
    }

}
