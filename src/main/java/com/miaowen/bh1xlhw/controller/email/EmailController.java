package com.miaowen.bh1xlhw.controller.email;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email.EmailForm;
import com.miaowen.bh1xlhw.model.query.email.EmailPageForm;
import com.miaowen.bh1xlhw.model.query.email.SendEmailForm;
import com.miaowen.bh1xlhw.model.query.email.UnsubscribeEmailForm;
import com.miaowen.bh1xlhw.model.vo.email.EmailVO;
import com.miaowen.bh1xlhw.model.vo.email.UnsubscribeEmailVO;
import com.miaowen.bh1xlhw.service.email.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;
/**
 * 系统-邮箱管理
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/email")
public class EmailController {

    @Resource
    EmailService emailService;

    

    @PreAuthorize("hasAuthority('system:email-manage:edit')")
    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated EmailForm email) {
        if (email.getId() == null) {
            emailService.add(email);
        } else {
            emailService.update(email);
        }
        return ResultVO.success();
    }

    

    @PreAuthorize("hasAuthority('system:email-manage:del')")
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        emailService.deleteBatch(form);
        return ResultVO.success();
    }

    

    @PreAuthorize("hasAuthority('system:email-manage:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<EmailVO>> page(@ModelAttribute @Validated EmailPageForm form) {
        return ResultVO.success(emailService.page(form));
    }

    

    @PreAuthorize("hasAuthority('system:email-manage:sendEmail')")
    @PostMapping("/sendEmail")
    public ResultVO<?> sendEmail(@RequestBody @Validated SendEmailForm form) {
        emailService.sendEmail(form);
        return ResultVO.success();
    }

    

    @PreAuthorize("hasAuthority('system:email-manage:updateState')")
    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        emailService.updateState(form);
        return ResultVO.success();
    }


    


    @PreAuthorize("hasAuthority('email-publish:unsubscribe:page')")
    @GetMapping("/unsubscribe")
    public ResultVO<PageVO<UnsubscribeEmailVO>> unsubscribeList(@ModelAttribute @Validated UnsubscribeEmailForm form) {
        return ResultVO.success(emailService.unsubscribeList(form));
    }

    

    @PreAuthorize("hasAuthority('email-publish:unsubscribe:cancel')")
    @GetMapping("/unsubscribe/recover/{id}")
    public ResultVO<Void> recoverUnsubscribe(@PathVariable Integer id) {
        emailService.recoverUnsubscribe(id);
        return ResultVO.success();
    }
}
