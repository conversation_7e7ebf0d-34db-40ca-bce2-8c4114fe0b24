package com.miaowen.bh1xlhw.controller.email;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email.EmailForm;
import com.miaowen.bh1xlhw.model.query.email.EmailPageForm;
import com.miaowen.bh1xlhw.model.query.email.SendEmailForm;
import com.miaowen.bh1xlhw.model.query.email.UnsubscribeEmailForm;
import com.miaowen.bh1xlhw.model.vo.email.EmailVO;
import com.miaowen.bh1xlhw.model.vo.email.UnsubscribeEmailVO;
import com.miaowen.bh1xlhw.service.email.EmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
/**
 * 系统-邮箱管理
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/email")
public class EmailController {

    @Resource
    EmailService emailService;

    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated EmailForm email) {
        if (email.getId() == null) {
            emailService.add(email);
        } else {
            emailService.update(email);
        }
        return ResultVO.success();
    }

    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        emailService.deleteBatch(form);
        return ResultVO.success();
    }

    @GetMapping("/page")
    public ResultVO<PageVO<EmailVO>> page(@ModelAttribute @Validated EmailPageForm form) {
        return ResultVO.success(emailService.page(form));
    }

    @PostMapping("/sendEmail")
    public ResultVO<?> sendEmail(@RequestBody @Validated SendEmailForm form) {
        emailService.sendEmail(form);
        return ResultVO.success();
    }

    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        emailService.updateState(form);
        return ResultVO.success();
    }


    @GetMapping("/unsubscribe")
    public ResultVO<PageVO<UnsubscribeEmailVO>> unsubscribeList(@ModelAttribute @Validated UnsubscribeEmailForm form) {
        return ResultVO.success(emailService.unsubscribeList(form));
    }

    @GetMapping("/unsubscribe/recover/{id}")
    public ResultVO<Void> recoverUnsubscribe(@PathVariable Integer id) {
        emailService.recoverUnsubscribe(id);
        return ResultVO.success();
    }
}