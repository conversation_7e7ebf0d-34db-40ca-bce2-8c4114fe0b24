package com.miaowen.bh1xlhw.controller.country;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.country.CountryForm;
import com.miaowen.bh1xlhw.model.query.country.CountryPageForm;
import com.miaowen.bh1xlhw.model.vo.country.CountryPageVO;
import com.miaowen.bh1xlhw.service.country.CountryService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 系统-国家/地区
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/country")
@AllArgsConstructor
public class CountryController {

    private final CountryService countryService;

    

    @PreAuthorize("hasAuthority('system:country-region:edit')")
    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated CountryForm CountryForm) {
        return  countryService.saveOrUpdateCountry(CountryForm);

    }

    

    @PreAuthorize("hasAuthority('system:country-region:del')")
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        countryService.deleteBatch(deleteBatchForm);
        return ResultVO.success();
    }

    

    @PreAuthorize("hasAuthority('system:country-region:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<CountryPageVO>> page(@ModelAttribute @Validated CountryPageForm form) {
        return ResultVO.success(countryService.pageCountry(form));
    }

    

    @PreAuthorize("hasAuthority('system:country-region:all')")
    @GetMapping("/listAll")
    public ResultVO<?> listAll() {
        return ResultVO.success(countryService.listAll());
    }

    

    @PreAuthorize("hasAuthority('system:country-region:updateState')")
    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        countryService.updateState(form);
        return ResultVO.success();
    }


    @GetMapping("/exportExcel")
    public void exportExcel(HttpServletResponse response) throws IOException {
        // 从service层获取所有国家数据
        List<CountryPageVO> countryList = countryService.listAll();

        // 通过工具类创建writer
        ExcelWriter writer = ExcelUtil.getWriter(true);

        // 合并单元格后的标题行，使用默认标题样式
        writer.merge(6, "国家信息列表");

        // 设置列宽
        writer.setColumnWidth(0, 20);
        writer.setColumnWidth(1, 20);
        writer.setColumnWidth(2, 20);
        writer.setColumnWidth(3, 20);
        writer.setColumnWidth(4, 20);
        writer.setColumnWidth(5, 20);

        // 设置标题
        writer.addHeaderAlias("name", "国家名称");
        writer.addHeaderAlias("code", "国家代码");
        writer.addHeaderAlias("continentCode", "大陆代码");
        writer.addHeaderAlias("sort", "排序");
        writer.addHeaderAlias("status", "状态");
        writer.addHeaderAlias("createTime", "创建时间");

        // 写入数据
        writer.write(countryList, true);

        // 设置浏览器响应的格式
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
        String fileName = new String(("国家信息列表").getBytes(), "iso-8859-1");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        //-out为OutputStream，需要写出到的目标流
        ServletOutputStream out = response.getOutputStream();
        writer.flush(out, true);
        // 关闭writer，释放内存
        writer.close();
        // 关闭输出流
        IoUtil.close(out);
    }
}
