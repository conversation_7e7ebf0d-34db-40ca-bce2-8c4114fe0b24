package com.miaowen.bh1xlhw.controller.file;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.file.PoSystemFilePageForm;
import com.miaowen.bh1xlhw.model.vo.file.FileVO;
import com.miaowen.bh1xlhw.service.file.PoSystemFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * 系统-文件模块
 *
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/file")
public class PoSystemFileController {

    @Resource
    private PoSystemFileService fileService;

    @PostMapping("/upload")
    public ResultVO<FileVO> uploadFile(@RequestParam("file") MultipartFile file, @RequestParam(required = false) Integer folderId) {
        try {
            return ResultVO.success(fileService.uploadFile(file, folderId));
        } catch (IOException e) {
            return ResultVO.fail(ResultEnum.FAIL, "文件上传失败");
        }
    }

    @PostMapping("/upload/local")
    public ResultVO<FileVO> uploadLocalFile(@RequestParam("file") MultipartFile file) {
        return ResultVO.success(fileService.uploadLocalFile(file));
    }

    @GetMapping("/list")
    public ResultVO<PageVO<PoSystemFilePageForm>> listFiles(@RequestParam(defaultValue = "1") Integer pageInt, @RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(required = false) String sortField,
                                                            @RequestParam(required = false, defaultValue = "asc") String sortOrder, @RequestParam(required = false) String folderId,
                                                            @RequestParam(required = false) String name) {
        return ResultVO.success(fileService.listFiles(pageInt, pageSize, sortField, sortOrder, folderId, name));
    }

    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        fileService.deleteBatch(form);
        return ResultVO.success();
    }

    @DeleteMapping("/{fileId}")
    public ResultVO<?> deleteFile(@PathVariable Integer fileId) throws IOException {
        fileService.deleteFile(fileId);
        return ResultVO.success();
    }

    @PutMapping("/{fileId}/status")
    public ResultVO<?> updateFileStatus(@PathVariable Integer fileId, @RequestParam Integer status) {
        fileService.updateStatus(fileId, status);
        return ResultVO.success();
    }

}
