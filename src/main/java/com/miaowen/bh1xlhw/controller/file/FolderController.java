package com.miaowen.bh1xlhw.controller.file;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.file.FolderForm;
import com.miaowen.bh1xlhw.model.query.file.FolderPageForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.service.file.FolderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description 文件分组模块
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/folder")
public class FolderController {

    @Resource
    private FolderService folderService;

    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated FolderForm folder) {
        if (folder.getId() == null) {
            folderService.add(folder);
        } else {
            folderService.update(folder);
        }
        return ResultVO.success();
    }

    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        folderService.deleteBatch(form);
        return ResultVO.success();
    }

    @GetMapping("/listAll")
    public ResultVO<?> listAll() {
        return ResultVO.success(folderService.listAll());
    }

    @GetMapping("/page")
    public ResultVO<?> page(@ModelAttribute @Validated FolderPageForm form) {
        return folderService.page(form);
    }

    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        folderService.updateState(form);
        return ResultVO.success();
    }

    @PutMapping("/updateDefault")
    public ResultVO<?> updateDefault(@RequestBody @Validated UpdateIsDefaultForm form) {
        folderService.updateDefault(form);
        return ResultVO.success();
    }

}
