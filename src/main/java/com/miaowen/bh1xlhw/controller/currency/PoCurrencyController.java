package com.miaowen.bh1xlhw.controller.currency;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.currency.PoCurrencyForm;
import com.miaowen.bh1xlhw.model.query.currency.PoCurrencyPageForm;
import com.miaowen.bh1xlhw.model.vo.currency.PoCurrencyPageVO;
import com.miaowen.bh1xlhw.service.currency.PoCurrencyService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统-货币模块
 *
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/currency")
@AllArgsConstructor
public class PoCurrencyController {

    private final PoCurrencyService currencyService;

    /**
     * 新增或修改
     *
     * @param currencyForm
     * @return
     */
    @PostMapping("/addOrUpdate")
    public ResultVO<?> addOrUpdate(@RequestBody @Validated PoCurrencyForm currencyForm) {
        currencyService.saveOrUpdateCurrency(currencyForm);
        return ResultVO.success();
    }

    /**
     * 批量删除
     *
     * @param deleteBatchForm
     * @return
     */
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        currencyService.deleteBatch(deleteBatchForm);
        return ResultVO.success();
    }

    /**
     * 批量删除 回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @DeleteMapping("/recycles")
    public ResultVO<?> recycles(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        currencyService.deleteRecycles(deleteBatchForm);
        return ResultVO.success();
    }
    /**
     * 批量批量恢复回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @PutMapping("/recycles")
    public ResultVO<?> recoverRecycles(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        currencyService.recoverRecycles(deleteBatchForm);
        return ResultVO.success();
    }

    /**
     * 分页查询
     *
     * @param form
     * @return
     */
    @GetMapping("/page")
    public ResultVO<PageVO<PoCurrencyPageVO>> page(@ModelAttribute @Validated PoCurrencyPageForm form) {
        PageVO<PoCurrencyPageVO> poCurrencyPage = currencyService.pageCurrency(form);
        return ResultVO.success(poCurrencyPage);
    }

    @GetMapping("/listAll")
    public ResultVO<?> listAll() {
        return ResultVO.success(currencyService.listAll());
    }

    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        currencyService.updateState(form);
        return ResultVO.success();
    }

    @GetMapping("/updateRates")
    public ResultVO<?> updateExchangeRate() {
        currencyService.updateExchangeRate();
        return ResultVO.success();
    }

}
