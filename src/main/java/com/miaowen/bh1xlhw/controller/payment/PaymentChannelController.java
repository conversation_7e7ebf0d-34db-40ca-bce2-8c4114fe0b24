package com.miaowen.bh1xlhw.controller.payment;

import com.miaowen.bh1xlhw.model.query.payment.PaymentChannelForm;
import com.miaowen.bh1xlhw.model.vo.payment.PaymentChannelVO;
import org.springframework.security.access.prepost.PreAuthorize;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.service.payment.PaymentChannelService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 支付通道
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 09:50:56
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/payment/channel")
public class PaymentChannelController {
    private final PaymentChannelService paymentChannelService;

    /**
     * 保存支付通道配置信息
     */
    @PreAuthorize("hasAuthority('payment-channel:add')")
    @PostMapping("")
    public ResultVO<Void> savePaymentPattern(@RequestBody PaymentChannelForm form) {
        paymentChannelService.savePaymentChannel(form);
        return ResultVO.success();
    }

    /**
     * 修改支付通道配置信息
     */
    @PreAuthorize("hasAuthority('payment-channel:edit')")
    @PutMapping("/{id}")
    public ResultVO<Void> updatePaymentPattern(@PathVariable Integer id, @RequestBody PaymentChannelForm form) {
        paymentChannelService.updatePaymentChannel(id, form);
        return ResultVO.success();
    }

    /**
     * 获取支付通道配置信息列表
     */
    @PreAuthorize("hasAuthority('payment-channel:page')")
    @GetMapping("")
    public ResultVO<PageVO<PaymentChannelVO>> pagePaymentPattern(@ModelAttribute PageForm pageForm, @RequestParam(value = "name",required = false) String name) {
        return ResultVO.success(paymentChannelService.pagePaymentChannel(pageForm,name));
    }

    /**
     * 获取支付通道配置信息列表全部
     */
    @GetMapping("/all")
    public ResultVO<List<PaymentChannelVO>> allPaymentPattern() {
        return ResultVO.success(paymentChannelService.allPaymentPattern());
    }

    /**
     * 删除支付通道配置信息
     */
    @PreAuthorize("hasAuthority('payment-channel:del')")
    @DeleteMapping("/{id}")
    public ResultVO<Void> deletePaymentPattern(@PathVariable Integer id) {
        paymentChannelService.deletePaymentChannel(id);
        return ResultVO.success();
    }
}
