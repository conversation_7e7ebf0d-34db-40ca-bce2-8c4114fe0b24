package com.miaowen.bh1xlhw.controller.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.SystemPayment;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.query.payment.PaymentAddOrUpdateForm;
import com.miaowen.bh1xlhw.model.query.payment.PoSystemPaymentPageForm;
import com.miaowen.bh1xlhw.model.vo.payment.PaymentVO;
import com.miaowen.bh1xlhw.service.payment.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;
/**
 * 系统-支付方式
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/14 09:05
 */
@Slf4j
@RestController
@RequestMapping("/payment")
public class PaymentController {

    @Resource
    PaymentService paymentService;

    @PreAuthorize("hasAuthority('system:pay-type:add') or hasAuthority('system:pay-type:edit')")
    @PostMapping("/addOrUpdate")
    public ResultVO<Void> addOrUpdate(@RequestBody @Validated PaymentAddOrUpdateForm form) throws JsonProcessingException {
        if (form.getId() == null) {
            paymentService.add(form);
        } else {
            paymentService.update(form);
        }
        return ResultVO.success();
    }

    @PreAuthorize("hasAuthority('system:pay-type:del')")
    @DeleteMapping("/deleteBatch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        paymentService.deleteBatch(form);
        return ResultVO.success();
    }


    @PreAuthorize("hasAuthority('system:pay-type:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<PaymentVO>> page(@ModelAttribute @Validated PoSystemPaymentPageForm form) {
        return paymentService.page(form);
    }

    @PreAuthorize("hasAuthority('system:pay-type:updateState')")
    @PutMapping("/updateState")
    public ResultVO<?> updateState(@RequestBody @Validated UpdateStateForm form) {
        paymentService.updateState(form);
        return ResultVO.success();
    }

    @PreAuthorize("hasAuthority('system:pay-type:updateDefault')")
    @PutMapping("/updateDefault")
    public ResultVO<?> updateDefault(@RequestBody @Validated UpdateIsDefaultForm form) {
        paymentService.updateDefault(form);
        return ResultVO.success();
    }


    /**
     * 批量删除 回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @PreAuthorize("hasAuthority('system:pay-type:recycle')")
    @DeleteMapping("/recycles")
    public ResultVO<?> recycles(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        paymentService.deleteRecycles(deleteBatchForm);
        return ResultVO.success();
    }
    /**
     * 批量批量恢复回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @PreAuthorize("hasAuthority('system:pay-type:recycle')")
    @PutMapping("/recycles")
    public ResultVO<?> recoverRecycles(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        paymentService.recoverRecycles(deleteBatchForm);
        return ResultVO.success();
    }
}
