package com.miaowen.bh1xlhw.controller.operation;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.AgentsPageForm;
import com.miaowen.bh1xlhw.model.query.goods.PlatformPageForm;
import com.miaowen.bh1xlhw.model.query.operation.*;
import com.miaowen.bh1xlhw.model.vo.operation.AgentsVO;
import com.miaowen.bh1xlhw.model.vo.operation.DomainVO;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;
import com.miaowen.bh1xlhw.service.operation.OperationService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;


/**
 *
 * 运营管理
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@RestController
@RequestMapping("/operation")
@AllArgsConstructor
public class OperationController {
    private final OperationService operationService;

    /**
     * 保存平台
     */
    
    @PreAuthorize("hasAuthority('operation-manage:platform:add')")
    @PostMapping("/platform")
    public ResultVO<Void> savePlatform(@RequestBody @Validated PlatformForm platformForm) {
        operationService.savePlatform(platformForm);
        return ResultVO.success();
    }

    /**
     * 修改平台
     */
    
    @PreAuthorize("hasAuthority('operation-manage:platform:edit')")
    @PutMapping("/platform/{id}")
    public ResultVO<Void> updatePlatform(@PathVariable Integer id, @RequestBody PlatformForm platformForm){
        operationService.updatePlatform(id, platformForm);
        return ResultVO.success();
    }

    /**
     * 删除平台
     */
    
    @PreAuthorize("hasAuthority('operation-manage:platform:del')")
    @DeleteMapping("/platform/{id}")
    public ResultVO<Void> deletePlatform(@PathVariable Integer id){
        operationService.removePlatform(id);
        return ResultVO.success();
    }

    /**
     * 恢复平台
     */
    @PutMapping("/platform/recover/{id}")
    public ResultVO<Void> recoverPlatform(@PathVariable Integer id){
        operationService.recoverPlatform(id);
        return ResultVO.success();
    }

    /**
     * 平台列表
     */
    
    @PreAuthorize("hasAuthority('operation-manage:platform:page')")
    @GetMapping("/platform")
    public ResultVO<PageVO<PlatformVO>> pagePlatform(@ModelAttribute PlatformPageForm pageForm){
        return ResultVO.success(operationService.pagePlatform(pageForm));
    }
    /**
     * 全部平台列表
     */
    @GetMapping("/platform/all")
    public ResultVO<List<PlatformVO>> allPlatform(){
        return ResultVO.success(operationService.allPlatform());
    }


    /**
     * 保存代理商
     */
    
    @PreAuthorize("hasAuthority('operation-manage:agent:add')")
    @PostMapping("/agents")
    public ResultVO<Void> saveAgents(@RequestBody @Validated AgentsForm agentsForm) {
        operationService.saveAgents(agentsForm);
        return ResultVO.success();
    }

    /**
     * 修改代理商
     */
    
    @PreAuthorize("hasAuthority('operation-manage:agent:edit')")
    @PutMapping("/agents/{id}")
    public ResultVO<Void> updateAgents(@PathVariable Integer id, @RequestBody AgentsForm agentsForm){
        operationService.updateAgents(id, agentsForm);
        return ResultVO.success();
    }

    /**
     * 删除代理商
     */
    
    @PreAuthorize("hasAuthority('operation-manage:agent:del')")
    @DeleteMapping("/agents/{id}")
    public ResultVO<Void> deleteAgents(@PathVariable Integer id){
        operationService.removeAgents(id);
        return ResultVO.success();
    }

    /**
     * 恢复代理商
     */
    @PutMapping("/agents/recover/{id}")
    public ResultVO<Void> recoverAgents(@PathVariable Integer id){
        operationService.recoverAgents(id);
        return ResultVO.success();
    }

    /**
     * 代理商列表
     */
    
    @PreAuthorize("hasAuthority('operation-manage:agent:page')")
    @GetMapping("/agents")
    public ResultVO<PageVO<AgentsVO>> pageAgents(@ModelAttribute AgentsPageForm pageForm){
        return ResultVO.success(operationService.pageAgents(pageForm));
    }

    /**
     * 代理商列表全部
     */
    @GetMapping("/agents/all")
    public ResultVO<List<AgentsVO>> allAgents(){
        return ResultVO.success(operationService.allAgents());
    }

    /**
     * 保存域名
     */
    
    @PreAuthorize("hasAuthority('operation-manage:domain:add')")
    @PostMapping("/domain")
    public ResultVO<Void> saveDomain(@RequestBody @Validated DomainForm domainForm) {
        operationService.saveDomain(domainForm);
        return ResultVO.success();
    }

    /**
     * 修改域名
     */
    
    @PreAuthorize("hasAuthority('operation-manage:domain:edit')")
    @PutMapping("/domain/{id}")
    public ResultVO<Void> updateDomain(@PathVariable Integer id, @RequestBody DomainForm domainForm){
        operationService.updateDomain(id, domainForm);
        return ResultVO.success();
    }

    /**
     * 删除域名
     */
    
    @PreAuthorize("hasAuthority('operation-manage:domain:del')")
    @DeleteMapping("/domain/{id}")
    public ResultVO<Void> removeDomain(@PathVariable Integer id){
        operationService.removeDomain(id);
        return ResultVO.success();
    }

    /**
     * 恢复域名
     */
    @PutMapping("/domain/recover/{id}")
    public ResultVO<Void> recoverDomain(@PathVariable Integer id){
        operationService.recoverDomain(id);
        return ResultVO.success();
    }

    /**
     * 域名列表
     */
    
    @PreAuthorize("hasAuthority('operation-manage:domain:page')")
    @GetMapping("/domain")
    public ResultVO<PageVO<DomainVO>> pageDomain(@ModelAttribute DomainPageForm pageForm){
        return ResultVO.success(operationService.pageDomain(pageForm));
    }

    /**
     * 域名列表全部
     */
    @GetMapping("/domain/all")
    public ResultVO<List<DomainVO>> allDomain(){
        return ResultVO.success(operationService.listDomain());
    }


    /**
     * 保存价格方案
     */
    
    @PreAuthorize("hasAuthority('operation-manage:price:add')")
    @PostMapping("/price")
    public ResultVO<Void> savePrice(@RequestBody @Validated PriceForm priceForm) {
        operationService.savePrice(priceForm);
        return ResultVO.success();
    }

    /**
     * 修改价格方案
     */
    
    @PreAuthorize("hasAuthority('operation-manage:price:edit')")
    @PutMapping("/price/{id}")
    public ResultVO<Void> updatePrice(@PathVariable Integer id, @RequestBody PriceForm priceForm){
        operationService.updatePrice(id, priceForm);
        return ResultVO.success();
    }

    /**
     * 删除价格方案
     */
    
    @PreAuthorize("hasAuthority('operation-manage:price:del')")
    @DeleteMapping("/price/{id}")
    public ResultVO<Void> removePrice(@PathVariable Integer id){
        operationService.removePrice(id);
        return ResultVO.success();
    }

    /**
     * 恢复价格方案
     */
    @PutMapping("/price/recover/{id}")
    public ResultVO<Void> recoverPrice(@PathVariable Integer id){
        operationService.recoverPrice(id);
        return ResultVO.success();
    }

    /**
     * 价格方案列表
     */
    
    @PreAuthorize("hasAuthority('operation-manage:price:page')")
    @GetMapping("/price")
    public ResultVO<PageVO<PriceVO>> pagePrice(@ModelAttribute PricePageForm pageForm){
        return ResultVO.success(operationService.pagePrice(pageForm));
    }

    /**
     * 价格方案列表全部
     */
    @GetMapping("/price/all")
    public ResultVO<List<PriceVO>> listPrice(){
        return ResultVO.success(operationService.listPrice());
    }

}
