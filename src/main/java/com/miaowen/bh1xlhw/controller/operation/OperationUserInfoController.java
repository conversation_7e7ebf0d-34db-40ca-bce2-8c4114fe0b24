package com.miaowen.bh1xlhw.controller.operation;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerQryForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserQryForm;
import com.miaowen.bh1xlhw.model.vo.operation.OperationManagerVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationUserVO;
import com.miaowen.bh1xlhw.service.operation.OperationUserInfoService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运营人员管理
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@RestController
@RequestMapping("/operation/user")
@AllArgsConstructor
public class OperationUserInfoController {
    private final OperationUserInfoService operationUserInfoService;

    /**
     * 保存运营人员
     */
    @PostMapping("")
    public ResultVO<Void> saveUser(@RequestBody @Validated OperationUserForm operationUserForm) {
        operationUserInfoService.saveUser(operationUserForm);
        return ResultVO.success();
    }

    /**
     * 修改运营人员
     */
    @PutMapping("/{id}")
    public ResultVO<Void> updateUser(@PathVariable Integer id, @RequestBody OperationUserForm operationUserForm){
        operationUserInfoService.updateUser(id, operationUserForm);
        return ResultVO.success();
    }

    /**
     * 删除运营人员
     */
    @DeleteMapping("/{id}")
    public ResultVO<Void> removeUser(@PathVariable Integer id){
        operationUserInfoService.removeUser(id);
        return ResultVO.success();
    }

    /**
     * 恢复运营人员
     */
    @PutMapping("/recover/{id}")
    public ResultVO<Void> recoverUser(@PathVariable Integer id){
        operationUserInfoService.recoverUser(id);
        return ResultVO.success();
    }

    /**
     * 运营人员列表
     */
    @GetMapping("")
    public ResultVO<PageVO<OperationUserVO>> pageUser(@ModelAttribute OperationUserQryForm pageForm){
        return ResultVO.success(operationUserInfoService.pageUser(pageForm));
    }

    /**
     * 运营人员列表全部
     */
    @GetMapping("/all")
    public ResultVO<List<OperationUserVO>> listUser(){
        return ResultVO.success(operationUserInfoService.listUser());
    }


    /**
     * 保存运营主管
     */
    @PostMapping("/manager")
    public ResultVO<Void> saveManager(@RequestBody @Validated OperationManagerForm operationManagerForm) {
        operationUserInfoService.saveManager(operationManagerForm);
        return ResultVO.success();
    }

    /**
     * 修改运营主管
     */
    @PutMapping("/manager/{id}")
    public ResultVO<Void> updateManager(@PathVariable Integer id, @RequestBody OperationManagerForm operationManagerForm){
        operationUserInfoService.updateManager(id, operationManagerForm);
        return ResultVO.success();
    }

    /**
     * 删除运营主管
     */
    @DeleteMapping("/manager/{id}")
    public ResultVO<Void> removeManager(@PathVariable Integer id){
        operationUserInfoService.removeManager(id);
        return ResultVO.success();
    }

    /**
     * 恢复运营主管
     */
    @PutMapping("/manager/recover/{id}")
    public ResultVO<Void> recoverManager(@PathVariable Integer id){
        operationUserInfoService.recoverManager(id);
        return ResultVO.success();
    }

    /**
     * 运营主管列表
     */
    @GetMapping("/manager")
    public ResultVO<PageVO<OperationManagerVO>> pageManager(@ModelAttribute OperationManagerQryForm pageForm){
        return ResultVO.success(operationUserInfoService.pageManager(pageForm));
    }

    /**
     * 全部运营主管列表
     */
    @GetMapping("/manager/all")
    public ResultVO<List<OperationManagerVO>> allManager(){
        return ResultVO.success(operationUserInfoService.allManager());
    }

}
