package com.miaowen.bh1xlhw.controller.goods;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.goods.ScoreManagementForm;
import com.miaowen.bh1xlhw.model.vo.goods.ScoreManagementVo;
import com.miaowen.bh1xlhw.service.good.ScoreManagementService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 分值管理
 *
 * @Author：huanglong
 * @Date：2025/6/4 16:00
 */
@RestController
@RequestMapping("/scoreManagement")
@AllArgsConstructor
public class ScoreManagementController {

    private final ScoreManagementService scoreManagementService;

    /**
     * 模板下载
     *
     * @param response
     * @return
     * @throws IOException
     */
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        // 设置响应头
        String fileName = URLEncoder.encode("分值设置模板" + ".xlsx", "UTF-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName);

        // 动态生成表头（示例：A-J列）
        // 生成10列的模板
        List<List<String>> head = generateDynamicHeaders(10);

        // 写入Excel
        try (OutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream)
                    .head(head)
                // 自动列宽
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("模板")
                    .doWrite(new ArrayList<>());
        }
    }

    /**
     * 上传
     *
     * @param file
     * @param tag
     * @param
     * @return
     * @throws IOException
     */
    @PostMapping("/upload/multi")
    public ResultVO<ScoreManagementForm> uploadMulti(@RequestParam("file") MultipartFile file)
            throws IOException {
        return ResultVO.success(scoreManagementService.upload(file, true));
    }

    /**
     * 上传
     *
     * @param file
     * @param tag
     * @param
     * @return
     * @throws IOException
     */
    @PostMapping("/upload/traditional")
    public ResultVO<ScoreManagementForm> uploadTraditional(@RequestParam("file") MultipartFile file)
            throws IOException {
        return ResultVO.success(scoreManagementService.upload(file, false));
    }

    /**
     * 获取详情 -多语言
     *
     * @param tag
     * @return
     */
    @GetMapping("/multi")
    public ResultVO<ScoreManagementVo> getScoreManagementMulti(@RequestParam("tag") String tag,
                                                               @RequestParam("version") Integer version) {
        return ResultVO.success(scoreManagementService.getScoreManagement(tag, version, true));
    }

    /**
     * 获取详情 -繁体
     *
     * @param tag
     * @return
     */
    @GetMapping("/traditional")
    public ResultVO<ScoreManagementVo> getScoreManagementTraditional(@RequestParam("tag") String tag,
                                                                     @RequestParam("version") Integer version) {
        return ResultVO.success(scoreManagementService.getScoreManagement(tag, version, false));
    }

    /**
     * 保存 -多语言
     *
     * @param
     * @return
     */
    @PostMapping("/multi")
    public ResultVO<Void> saveMulti(@RequestBody ScoreManagementForm form) {
        scoreManagementService.save(form, true);
        return ResultVO.success();
    }

    /**
     * 保存 -繁体
     *
     * @param
     * @return
     */
    @PostMapping("/traditional")
    public ResultVO<Void> getScoreManagementTraditional(@RequestBody ScoreManagementForm form) {
        scoreManagementService.save(form, false);
        return ResultVO.success();
    }

    /**
     * 导出 -多语言
     *
     * @param
     * @return
     */
    @GetMapping("/multi/export")
    public void exportMulti(@RequestParam("tag") String tag,
                            @RequestParam("version") Integer version,
                            HttpServletResponse response)
            throws IOException {
        scoreManagementService.export(response, tag, version, true);
    }

    /**
     * 导出 -繁体
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/export")
    public void exportTraditional(@RequestParam("tag") String tag,
                                  @RequestParam("version") Integer version,
                                  HttpServletResponse response)
            throws IOException {
        scoreManagementService.export(response, tag, version, false);
    }


    /**
     * 生成动态表头
     *
     * @param columnCount 列数
     * @return 表头数据
     */
    private List<List<String>> generateDynamicHeaders(int columnCount) {
        List<List<String>> head = new ArrayList<>();
        for (int i = 0; i < columnCount; i++) {
            // 从1开始
            head.add(Collections.singletonList("选项" + getColumnName(i + 1)));
        }
        return head;
    }

    /**
     * 获取Excel列名（A, B, ..., Z, AA, AB, ...）
     */
    private String getColumnName(int number) {
        StringBuilder sb = new StringBuilder();
        while (number > 0) {
            int remainder = (number - 1) % 26;
            sb.append((char) ('A' + remainder));
            number = (number - 1) / 26;
        }
        return sb.reverse().toString();
    }


}
