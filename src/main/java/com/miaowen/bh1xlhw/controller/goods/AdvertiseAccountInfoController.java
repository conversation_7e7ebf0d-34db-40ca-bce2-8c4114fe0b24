package com.miaowen.bh1xlhw.controller.goods;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.AdvertiseAccountForm;
import com.miaowen.bh1xlhw.model.query.goods.AdvertisePageForm;
import com.miaowen.bh1xlhw.model.vo.goods.AdvertiseAccountGoodLinkVO;
import com.miaowen.bh1xlhw.model.vo.goods.AdvertiseAccountVO;
import com.miaowen.bh1xlhw.service.good.AdvertiseAccountInfoService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 广告账户信息
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 16:22:34
 */

@Slf4j
@RestController
@RequestMapping("/advertise/account")
@AllArgsConstructor
public class AdvertiseAccountInfoController {
    private final AdvertiseAccountInfoService advertiseAccountInfoService;

    /**
     * 保存广告账户表信息
     */
    @PostMapping("")
    public ResultVO<Void> saveAdvertiseAccount(@RequestBody AdvertiseAccountForm advertiseAccountForm) {
        advertiseAccountInfoService.saveAdvertiseAccount(advertiseAccountForm);
        return ResultVO.success();
    }

    /**
     * 修改广告账户表信息
     */
    @PutMapping("/{id}")
    public ResultVO<Void> updateAdvertiseAccount(@PathVariable Integer id, @RequestBody AdvertiseAccountForm advertiseAccountForm) {
        advertiseAccountInfoService.updateAdvertiseAccount(id, advertiseAccountForm);
        return ResultVO.success();
    }

    /**
     * 获取广告账户表信息列表
     */
    @GetMapping("")
    public ResultVO<PageVO<AdvertiseAccountVO>> pageAdvertiseAccount(@ModelAttribute AdvertisePageForm pageForm) {
        return ResultVO.success(advertiseAccountInfoService.pageAdvertiseAccount(pageForm));
    }

    /**
     * 获取广告账户表信息列表全部
     */
    @GetMapping("/all")
    public ResultVO<List<AdvertiseAccountVO>> allAdvertiseAccount() {
        return ResultVO.success(advertiseAccountInfoService.allAdvertiseAccount());
    }


    /**
     * 获取广告账户商品推广链接
     */
    @GetMapping("/tgLink/{id}")
    public ResultVO<AdvertiseAccountGoodLinkVO> listAdvertiseAccountTgLink(@PathVariable Integer id) {
        return ResultVO.success(advertiseAccountInfoService.listAdvertiseAccountTgLink(id));
    }

    /**
     * 删除广告账户表信息
     */
    @DeleteMapping("/{id}")
    public ResultVO<Void> deleteAdvertiseAccount(@PathVariable Integer id) {
        advertiseAccountInfoService.deleteAdvertiseAccount(id);
        return ResultVO.success();
    }
}




