package com.miaowen.bh1xlhw.controller.goods;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsPromotionMultilingualVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsPromotionTraditionalVO;
import com.miaowen.bh1xlhw.service.good.GoodsPromotionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * 推广商品
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 09:46:38
 */

@Slf4j
@RequestMapping("/goods/promotion")
@RestController
@AllArgsConstructor
public class GoodsPromotionController {
    private final GoodsPromotionService goodsPromotionService;

    /**
     * 保存推广商品信息多语言
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:add')")
    @PostMapping("/multilingual")
    public ResultVO<Void> saveGoodPromotionMultilingual(@RequestBody GoodsPromotionMultilingualForm goodsPromotionMultilingualForm) {
        goodsPromotionService.saveGoodPromotionMultilingual(goodsPromotionMultilingualForm);
        return ResultVO.success();
    }

    /**
     * 修改推广商品信息多语言
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:price-setting')")
    @PutMapping("/multilingual/{id}")
    public ResultVO<Void> updateGoodPromotionMultilingual(@PathVariable Integer id, @RequestBody GoodsPromotionMultilingualForm goodsPromotionMultilingualForm) {
        goodsPromotionService.updateGoodPromotionMultilingual(id, goodsPromotionMultilingualForm);
        return ResultVO.success();
    }

    /**
     * 多语言推广商品批量删除 回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:del')")
    @DeleteMapping("/multilingual/recycles")
    public ResultVO<?> recyclesGoodsPromotionMultilingual(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsPromotionService.deleteRecyclesGoodsPromotionMultilingual(deleteBatchForm);
        return ResultVO.success();
    }

    /**
     * 多语言推广商品批量批量恢复回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:price-setting')")
    @PutMapping("/multilingual/recycles")
    public ResultVO<Void> recoverRecyclesGoodsPromotionMultilingual(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsPromotionService.recoverRecyclesGoodsPromotionMultilingual(deleteBatchForm);
        return ResultVO.success();
    }

    /**
     * 复制推广商品信息多语言
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:copy')")
    @PostMapping("/multilingual/copy/{id}")
    public ResultVO<Void> opyGoodPromotionMultilingual(@PathVariable Integer id) {
        goodsPromotionService.copyGoodPromotionMultilingual(id);
        return ResultVO.success();
    }

    /**
     * 获取推广商品信息多语言列表
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:page')")
    @GetMapping("/multilingual")
    public ResultVO<PageVO<GoodsPromotionMultilingualVO>> getGoodTypeMultilingual(@ModelAttribute GoodsPromotionPageForm pageForm) {
        return ResultVO.success(goodsPromotionService.pageGoodPromotionMultilingual(pageForm));
    }

    /**
     * 删除推广商品信息多语言
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:del')")
    @DeleteMapping("/multilingual/{id}")
    public ResultVO<Void> deleteGoodMultilingual(@PathVariable Integer id) {
        goodsPromotionService.deleteGoodPromotionMultilingual(id);
        return ResultVO.success();
    }



    /**
     * 保存推广商品信息繁体
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:complex:add')")
    @PostMapping("/traditional")
    public ResultVO<Void> saveGoodPromotionTraditional(@RequestBody GoodsPromotionTraditionalForm goodsPromotionTraditionalForm) {
        goodsPromotionService.saveGoodPromotionTraditional(goodsPromotionTraditionalForm);
        return ResultVO.success();
    }

    /**
     * 修改推广商品信息繁体
     */
    @PutMapping("/traditional/{id}")
    public ResultVO<Void> updateGoodPromotionTraditional(@PathVariable Integer id, @RequestBody GoodsPromotionTraditionalForm goodsPromotionTraditionalForm) {
        goodsPromotionService.updateGoodPromotionTraditional(id, goodsPromotionTraditionalForm);
        return ResultVO.success();
    }

    /**
     * 多语言推广商品批量删除 回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:complex:del')")
    @DeleteMapping("/traditional/recycles")
    public ResultVO<Void> recyclesGoodsPromotionTraditional(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsPromotionService.deleteRecyclesGoodsPromotionTraditional(deleteBatchForm);
        return ResultVO.success();
    }
    /**
     * 多语言推广商品批量批量恢复回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @PutMapping("/traditional/recycles")
    public ResultVO<Void> recoverRecyclesGoodsPromotionTraditional(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsPromotionService.recoverRecyclesGoodsPromotionTraditional(deleteBatchForm);
        return ResultVO.success();
    }



    /**
     * 复制推广商品信息繁体
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:complex:copy')")
    @PostMapping("/traditional/copy/{id}")
    public ResultVO<Void> opyGoodPromotionTraditional(@PathVariable Integer id) {
        goodsPromotionService.copyGoodPromotionTraditional(id);
        return ResultVO.success();
    }

    /**
     * 获取推广商品信息繁体列表
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:complex:page')")
    @GetMapping("/traditional")
    public ResultVO<PageVO<GoodsPromotionTraditionalVO>> getGoodTypeTraditional(@ModelAttribute GoodsPromotionPageForm pageForm) {
        return ResultVO.success(goodsPromotionService.pageGoodPromotionTraditional(pageForm));
    }

    /**
     * 删除推广商品信息繁体
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:complex:del')")
    @DeleteMapping("/traditional/{id}")
    public ResultVO<Void> deleteGoodTraditional(@PathVariable Integer id) {
        goodsPromotionService.deleteGoodPromotionTraditional(id);
        return ResultVO.success();
    }
}




