package com.miaowen.bh1xlhw.controller.goods;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduBaseForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduDetailForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduMapForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduMapLangForm;
import com.miaowen.bh1xlhw.service.good.ResultWeiduAnalysisServie;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * 结果设置-维度分析
 *
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 16:56
 */
@RestController
@RequestMapping("/resultSetting")
@AllArgsConstructor
public class ResultSettingWeiduController {

    private final ResultWeiduAnalysisServie resultWeiduAnalysisServie;

    /**
     * 维度分析-图表配置-图表配置列表
     *
     * @param
     * @return
     */
    @GetMapping("/weiduMap/getByWeiduId")
    public ResultVO<PageVO<ResultWeiduMapForm>> getWeiduMapByWeiduId(@Param("id") Integer id,
                                                                     @Param("pageInt") Integer pageInt,
                                                                     @Param("pageSize") Integer pageSize) {
        return ResultVO.success(resultWeiduAnalysisServie.getByWeiduId(pageInt, pageSize, id));
    }

    /**
     * 维度分析-图表配置-图表配置详情（编辑项带出）
     *
     * @param
     * @return
     */
    @GetMapping("/weiduMap/getByWeiduMapId")
    public ResultVO<List<ResultWeiduMapLangForm.WeiduMapLang>> getByWeiduMapId(@Param("weiduMapId") Integer weiduMapId) {
        return ResultVO.success(resultWeiduAnalysisServie.getByWeiduMapIdLang(weiduMapId));
    }

    /**
     * 维度分析-图表配置-删除图表配置项
     *
     * @param
     * @return
     */
    @DeleteMapping("/weiduMap/deleteByWeiduMapId")
    public ResultVO<Void> deleteWeiduMap(@RequestParam("ids") List<Integer> ids) {
        resultWeiduAnalysisServie.deletByWeiduMapId(ids);
        return ResultVO.success();
    }

    /**
     * 维度分析-图表配置-保存图表配置项
     *
     * @param form 保存图表配置项的表单
     * @return 保存成功后的结果
     */
    @PostMapping("/weiduMap/saveWeiduMap")
    public ResultVO<Void> saveWeiduMap(@RequestBody ResultWeiduMapLangForm form) {
        resultWeiduAnalysisServie.saveOrUpdate(form);
        return ResultVO.success();
    }


    /**
     * 维度分析-多语言-获取维度分析数据
     *
     * @param
     * @return
     */
    @GetMapping("/multi/weidu/getWeidu")
    public ResultVO<PageVO<ResultWeiduBaseForm>> getMultiResultWeidu(@Param("tag") String tag,
                                                                     @Param("pageInt") Integer pageInt,
                                                                     @Param("pageSize") Integer pageSize) {
        return ResultVO.success(resultWeiduAnalysisServie.getByTag(tag, pageInt, pageSize, true));
    }

    /**
     * 维度分析-繁体-获取维度分析数据
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/weidu/getWeidu")
    public ResultVO<PageVO<ResultWeiduBaseForm>> getTraditionalResultWeidu(@Param("tag") String tag,
                                                                           @Param("pageInt") Integer pageInt,
                                                                           @Param("pageSize") Integer pageSize) {
        return ResultVO.success(resultWeiduAnalysisServie.getByTag(tag, pageInt, pageSize, false));
    }

    /**
     * 维度分析-获取维度分析数据详情(编辑弹出）
     *
     * @param
     * @return
     */
    @GetMapping("/weidu/getWeiduLangDetail")
    public ResultVO<List<ResultWeiduDetailForm.WeiduLang>> getWeiduLangByWeiduId(@Param("id") Integer id) {
        return ResultVO.success(resultWeiduAnalysisServie.getByWeiduIdLang(id));
    }

    /**
     * 维度分析-删除维度分析条目
     *
     * @param
     * @return
     */
    @DeleteMapping("/weidu/deleteById")
    public ResultVO<Void> deleteById(@RequestParam("ids") List<Integer> ids) {
        resultWeiduAnalysisServie.deleteByWeiduId(ids);
        return ResultVO.success();
    }

    /**
     * 维度分析-多语言-新增或者修改维度信息数据
     *
     * @param form
     * @param
     * @return
     */
    @PostMapping("/multi/weidu/saveOrUpdate")
    public ResultVO<Void> saveOrUpdateMultiWeidu(@RequestBody ResultWeiduDetailForm form) {
        resultWeiduAnalysisServie.saveOrUpdate(form, true);
        return ResultVO.success();
    }

    /**
     * 维度分析-繁体-新增或者修改维度信息数据
     *
     * @param form
     * @param
     * @return
     */
    @PostMapping("/traditional/weidu/saveOrUpdate")
    public ResultVO<Void> saveOrUpdateTraditionalWeidu(@RequestBody ResultWeiduDetailForm form) {
        resultWeiduAnalysisServie.saveOrUpdate(form, false);
        return ResultVO.success();
    }


}
