package com.miaowen.bh1xlhw.controller.goods;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsTraditionalVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsMultilingualVO;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商品管理
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09
 */
@RestController
@RequestMapping("/goods")
@AllArgsConstructor
public class GoodsController {
    private final GoodsService goodsService;

    /**
     * 保存多语言商品信息
     */
    @PostMapping("/multilingual")
    public ResultVO<Void> saveGoodsMultilingual(@RequestBody GoodsMultilingualForm goodsMultilingualForm) {
        goodsService.saveGoodsMultilingual(goodsMultilingualForm);
        return ResultVO.success();
    }

    /**
     * 修改多语言商品信息
     */
    @PutMapping("/multilingual/{id}")
    public ResultVO<Void> updateGoodsMultilingual(@PathVariable Integer id,
                                                  @RequestBody GoodsMultilingualForm goodsMultilingualForm) {
        goodsService.updateGoodsMultilingual(id, goodsMultilingualForm);
        return ResultVO.success();
    }
    /**
     * 多语言商品批量删除 回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @DeleteMapping("/multilingual/recycles")
    public ResultVO<?> recyclesGoodsMultilingual(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsService.deleteRecyclesGoodsMultilingual(deleteBatchForm);
        return ResultVO.success();
    }
    /**
     * 多语言商品批量批量恢复回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @PutMapping("/multilingual/recycles")
    public ResultVO<?> recoverRecyclesGoodsMultilingual(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsService.recoverRecyclesGoodsMultilingual(deleteBatchForm);
        return ResultVO.success();
    }

    /**
     * 获取多语言商品信息列表
     */
    @GetMapping("/multilingual")
    public ResultVO<PageVO<GoodsMultilingualVO>> pageGoodsMultilingual(@ModelAttribute GoodsPageForm pageForm) {
        return ResultVO.success(goodsService.pageGoodsMultilingual(pageForm));
    }

    /**
     * 获取多语言商品信息列表全部
     */
    @GetMapping("/multilingual/all")
    public ResultVO<List<GoodsMultilingualVO>> pageGoodTypeMultilingual() {
        return ResultVO.success(goodsService.listGoodsMultilingual());
    }


    /**
     * 删除多语言商品信息
     */
    @DeleteMapping("/multilingual/{id}")
    public ResultVO<Void> deleteGoodsMultilingual(@PathVariable Integer id) {
        goodsService.deleteGoodsMultilingual(id);
        return ResultVO.success();
    }


    /**
     * 保存繁体商品信息
     */
    @PostMapping("/traditional")
    public ResultVO<Void> saveGoodsTraditional(@RequestBody GoodsTraditionalForm goodsTraditionalForm) {
        goodsService.saveGoodsTraditional(goodsTraditionalForm);
        return ResultVO.success();
    }

    /**
     * 修改繁体商品信息
     */
    @PutMapping("/traditional/{id}")
    public ResultVO<Void> updateGoodsTraditional(@PathVariable Integer id,
                                                 @RequestBody GoodsTraditionalForm goodsTraditionalForm) {
        goodsService.updateGoodsTraditional(id, goodsTraditionalForm);
        return ResultVO.success();
    }
    /**
     * 多语言商品批量删除 回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @DeleteMapping("/traditional/recycles")
    public ResultVO<?> recyclesGoodsTraditional(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsService.deleteRecyclesGoodsTraditional(deleteBatchForm);
        return ResultVO.success();
    }
    /**
     * 多语言商品批量批量恢复回收站
     *
     * @param deleteBatchForm deleteBatchForm
     * @return
     */
    @PutMapping("/traditional/recycles")
    public ResultVO<?> recoverRecyclesGoodsTraditional(@RequestBody @Validated DeleteBatchForm deleteBatchForm) {
        goodsService.recoverRecyclesGoodsTraditional(deleteBatchForm);
        return ResultVO.success();
    }
    /**
     * 获取繁体商品信息列表
     */
    @GetMapping("/traditional")
    public ResultVO<PageVO<GoodsTraditionalVO>> pageGoodsTraditional(@ModelAttribute GoodsPageForm pageForm) {
        return ResultVO.success(goodsService.pageGoodsTraditional(pageForm));
    }

    /**
     * 获取繁体商品信息列表全部
     */
    @GetMapping("/traditional/all")
    public ResultVO<List<GoodsTraditionalVO>> listGoodsTraditional() {
        return ResultVO.success(goodsService.listGoodsTraditional());
    }


    /**
     * 删除繁体商品信息
     */
    @DeleteMapping("/traditional/{id}")
    public ResultVO<Void> deleteGoodsTraditional(@PathVariable Integer id) {
        goodsService.deleteGoodsTraditional(id);
        return ResultVO.success();
    }

}
