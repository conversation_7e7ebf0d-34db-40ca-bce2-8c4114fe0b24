package com.miaowen.bh1xlhw.controller.goods;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.goods.*;
import com.miaowen.bh1xlhw.model.vo.goods.ResultWrongVo;
import com.miaowen.bh1xlhw.service.good.*;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * 结果设置
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 10:04
 */
@RestController
@RequestMapping("/resultSetting")
@AllArgsConstructor
public class ResultSettingController {
    private final ResultSettingService resultSettingService;
    private final ResultScoreDescService resultScoreDescService;
    private final ResultJobScoreService resultJobScoreService;
    private final ResultReadingService readingService;
    private final ResultWrongPriceService resultWrongPriceService;


    /**
     * 错题解析-多语言-获取价格设置
     *
     * @param tag
     * @return
     */
    @GetMapping("/multi/wrongAnalysisPrice")
    public ResultVO<ResultWrongAnalysisPriceForm> getMultiWrongAnalysisPrice(@Param("tag") String tag) {
        return ResultVO.success(resultWrongPriceService.getWrongAnalysisPrice(tag, true));
    }

    /**
     * 错题解析-繁体-获取价格设置
     *
     * @param tag
     * @return
     */
    @GetMapping("/traditional/wrongAnalysisPrice")
    public ResultVO<ResultWrongAnalysisPriceForm> getTraditionalWrongAnalysisPrice(@Param("tag") String tag) {
        return ResultVO.success(resultWrongPriceService.getWrongAnalysisPrice(tag, false));
    }

    /**
     * 错题解析-多语言-保存价格设置
     *
     * @param
     * @return
     */
    @PostMapping("/multi/wrongAnalysisPrice/save")
    public ResultVO<Void> addMultiWrongAnalysisPrice(@RequestBody ResultWrongAnalysisPriceForm form) {
        resultWrongPriceService.save(form, true);
        return ResultVO.success();
    }

    /**
     * 错题解析-繁体-保存价格设置
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/wrongAnalysisPrice/save")
    public ResultVO<Void> addTraditionalWrongAnalysisPrice(@RequestBody ResultWrongAnalysisPriceForm form) {
        resultWrongPriceService.save(form, false);
        return ResultVO.success();
    }


    /**
     * 错题解析-多语言-题目答案列表
     *
     * @param tag
     * @return
     */
    @GetMapping("/multi/wrongAnalysis")
    public ResultVO<List<ResultWrongVo>> getMultiResultWrongVo(@Param("tag") @NonNull String tag) {
        return ResultVO.success(resultSettingService.getResultWrongVo(tag, true));
    }

    /**
     * 错题解析-繁体-题目答案列表
     *
     * @param tag
     * @return
     */
    @GetMapping("/traditional/wrongAnalysis")
    public ResultVO<List<ResultWrongVo>> getTraResultWrongVo(@Param("tag") @NonNull String tag) {
        return ResultVO.success(resultSettingService.getResultWrongVo(tag, false));
    }

    /**
     * 错题解析-多语言-新增题号及答案
     *
     * @param
     * @return
     */
    @PostMapping("/multi/wrongAnalysis/add")
    public ResultVO<Void> addMultiResultWrongVo(@RequestBody ResultSettingWrongDetailForm form) {
        return resultSettingService.addResultWrongVo(form, true);
    }

    /**
     * 错题解析-繁体-新增题号及答案
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/wrongAnalysis/add")
    public ResultVO<Void> addTraResultWrongVo(@RequestBody ResultSettingWrongDetailForm form) {
        return resultSettingService.addResultWrongVo(form, false);
    }

    /**
     * 错题解析-多语言-修改题号及答案
     *
     * @param
     * @return
     */
    @PostMapping("/multi/wrongAnalysis/edit")
    public ResultVO<Void> editMultiResultWrongVo(@RequestBody ResultSettingWrongBaseForm form) {
        return ResultVO.success(resultSettingService.editResultWrongVo(form, true));
    }

    /**
     * 错题解析-繁体-修改题号及答案
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/wrongAnalysis/edit")
    public ResultVO<Void> editTraResultWrongVo(@RequestBody ResultSettingWrongBaseForm form) {
        return ResultVO.success(resultSettingService.editResultWrongVo(form, false));
    }

    /**
     * 错题解析-多语言-修改题号及答案解析(含答案解析）
     *
     * @param
     * @return
     */
    @PostMapping("/multi/wrongAnalysis/editDetail")
    public ResultVO<Void> editMultiWrongAnalysisDetail(@RequestBody ResultSettingWrongDetailForm form) {
        return ResultVO.success(resultSettingService.editResultWrongDetailVo(form, true));
    }

    /**
     * 错题解析-繁体-修改题号及答案解析(含答案解析）
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/wrongAnalysis/editDetail")
    public ResultVO<Void> edtTraWrongAnalysisDetail(@RequestBody ResultSettingWrongDetailForm form) {
        return ResultVO.success(resultSettingService.editResultWrongDetailVo(form, false));
    }

    /**
     * 错题解析-多语言-获取题号及答案解析(含答案解析）
     *
     * @param
     * @return
     */
    @GetMapping("/multi/wrongAnalysis/detail")
    public ResultVO<ResultSettingWrongDetailForm> getMultiWrongAnalysisDetail(@ModelAttribute ResultSettingWrongBaseForm form) {
        return ResultVO.success(resultSettingService.getResultWrongDetailVo(form, true));
    }

    /**
     * 错题解析-繁体-获取题号及答案解析(含答案解析）
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/wrongAnalysis/detail")
    public ResultVO<ResultSettingWrongDetailForm> getTraWrongAnalysisDetail(@ModelAttribute ResultSettingWrongBaseForm form) {
        return ResultVO.success(resultSettingService.getResultWrongDetailVo(form, false));
    }

    /**
     * 错题解析-多语言-删除题号及答案
     *
     * @param
     * @return
     */
    @PostMapping("/multi/wrongAnalysis/delete")
    public ResultVO<Void> deleteMultiResultWrongVo(@RequestBody ResultSettingWrongBaseForm form) {
        return ResultVO.success(resultSettingService.deleteResultWrongVo(form, true));
    }

    /**
     * 错题解析-繁体-删除题号及答案
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/wrongAnalysis/delete")
    public ResultVO<Void> deleteTraResultWrongVo(@RequestBody ResultSettingWrongBaseForm form) {
        return ResultVO.success(resultSettingService.deleteResultWrongVo(form, false));
    }


    /**
     * 分值说明-多语言-获取分值说明列表（不含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/multi/score/getScoreDesc")
    public ResultVO<PageVO<ResultScoreForm>> getMultiResultScoreDescVo(@Param("tag") @NonNull String tag,
                                                                       @Param("pageInt") Integer pageInt,
                                                                       @Param("pageSize") Integer pageSize) {
        return ResultVO.success(resultScoreDescService.getResultScoreForm(tag, pageInt, pageSize, true));
    }

    /**
     * 分值说明-繁体-获取分值说明列表（不含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/score/getScoreDesc")
    public ResultVO<PageVO<ResultScoreForm>> getTraditionalResultScoreDescVo(@Param("tag") @NonNull String tag,
                                                                             @Param("pageInt") Integer pageInt,
                                                                             @Param("pageSize") Integer pageSize) {
        return ResultVO.success(resultScoreDescService.getResultScoreForm(tag, pageInt, pageSize, false));
    }

    /**
     * 分值说明-多语言-获取分值说明列表（含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/multi/score/getScoreDescDetail")
    public ResultVO<ResultScoreDetailForm> getMultiResultScoreDescDetailVo(@Param("tag") @NonNull String tag,
                                                                           @Param("id") @NonNull Integer id) {
        return ResultVO.success(resultScoreDescService.getResultScoreDetailForm(tag, id, true));
    }

    /**
     * 分值说明-繁体-获取分值说明列表（含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/score/getScoreDescDetail")
    public ResultVO<ResultScoreDetailForm> getTraditionalResultScoreDescDetailVo(@Param("tag") @NonNull String tag,
                                                                                 @Param("id") @NonNull Integer id) {
        return ResultVO.success(resultScoreDescService.getResultScoreDetailForm(tag, id, false));
    }

    /**
     * 分值说明-多语言-删除分值说明
     *
     * @param
     * @return
     */
    @DeleteMapping("/multi/score/deleteScoreDesc")
    public ResultVO<Void> deleteMultiScoreDesc(@RequestParam("ids") @NonNull List<Integer> ids) {
        resultScoreDescService.deleteResultScoreForm(ids, true);
        return ResultVO.success();
    }

    /**
     * 分值说明-繁体-删除分值说明
     *
     * @param
     * @return
     */
    @DeleteMapping("/traditional/score/deleteScoreDesc")
    public ResultVO<Void> deleteTraditionalScoreDesc(@RequestParam("ids") @NonNull List<Integer> ids) {
        resultScoreDescService.deleteResultScoreForm(ids, false);
        return ResultVO.success();
    }

    /**
     * 分值说明-多语言-新增或修改分值说明
     *
     * @param
     * @return
     */
    @PostMapping("/multi/score/saveScore")
    public ResultVO<Void> addMultiScoreDesc(@RequestBody ResultScoreDetailForm detailForm) {
        resultScoreDescService.saveResultScoreForm(detailForm, true);
        return ResultVO.success();
    }

    /**
     * 分值说明-繁体-新增或修改分值说明
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/score/saveScore")
    public ResultVO<Void> addTraditionalScoreDesc(@RequestBody ResultScoreDetailForm detailForm) {
        resultScoreDescService.saveResultScoreForm(detailForm, false);
        return ResultVO.success();
    }


    /**
     * 职业智力分值表-多语言-获取职业智力分值说明列表（不含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/multi/jobScore/getScoreDesc")
    public ResultVO<PageVO<ResultJobForm>> getMultiResultJobScoreDescVo(@Param("tag") @NonNull String tag,
                                                                        @Param("pageInt") Integer pageInt,
                                                                        @Param("pageSize") Integer pageSize) {
        return ResultVO.success(resultJobScoreService.getResultJobForm(tag, pageInt, pageSize, true));
    }

    /**
     * 职业智力分值表-繁体-获取职业智力分值说明列表（不含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/jobScore/getScoreDesc")
    public ResultVO<PageVO<ResultJobForm>> getTraditionalResultJobScoreDescVo(@Param("tag") @NonNull String tag,
                                                                              @Param("pageInt") Integer pageInt,
                                                                              @Param("pageSize") Integer pageSize) {
        return ResultVO.success(resultJobScoreService.getResultJobForm(tag, pageInt, pageSize, false));
    }

    /**
     * 职业智力分值表-多语言-获取职业智力分值说明列表（含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/multi/jobScore/getScoreDescDetail")
    public ResultVO<ResultJobDetailForm> getMultiResultJobScoreDescDetailVo(@Param("tag") @NonNull String tag,
                                                                            @Param("id") @NonNull Integer id) {
        return ResultVO.success(resultJobScoreService.getResultJobDetailForm(tag, id, true));
    }

    /**
     * 职业智力分值表-繁体-获取职业智力分值说明列表（含详情）
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/jobScore/getScoreDescDetail")
    public ResultVO<ResultJobDetailForm> getTraditionalResultJobScoreDescDetailVo(@Param("tag") @NonNull String tag,
                                                                                  @Param("id") @NonNull Integer id) {
        return ResultVO.success(resultJobScoreService.getResultJobDetailForm(tag, id, false));
    }

    /**
     * 职业智力分值表-多语言-删除职业智力分值说明
     *
     * @param
     * @return
     */
    @DeleteMapping("/multi/jobScore/deleteScoreDesc")
    public ResultVO<Void> deleteMultiJobScoreDesc(@RequestParam("ids") @NonNull List<Integer> ids) {
        resultJobScoreService.deleteResultJobForm(ids, true);
        return ResultVO.success();
    }

    /**
     * 职业智力分值表-繁体-删除职业智力分值说明
     *
     * @param
     * @return
     */
    @DeleteMapping("/traditional/jobScore/deleteScoreDesc")
    public ResultVO<Void> deleteTraditionalJobScoreDesc(@RequestParam("ids") @NonNull List<Integer> ids) {
        resultJobScoreService.deleteResultJobForm(ids, false);
        return ResultVO.success();
    }

    /**
     * 职业智力分值表-多语言-新增或修改职业智力分值说明
     *
     * @param
     * @return
     */
    @PostMapping("/multi/jobScore/saveScore")
    public ResultVO<Void> addMultiJobScoreDesc(@RequestBody ResultJobDetailForm detailForm) {
        resultJobScoreService.saveResultJobForm(detailForm, true);
        return ResultVO.success();
    }

    /**
     * 职业智力分值表-繁体-新增或修改职业智力分值说明
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/jobScore/saveScore")
    public ResultVO<Void> addTraditionalJobScoreDesc(@RequestBody ResultJobDetailForm detailForm) {
        resultJobScoreService.saveResultJobForm(detailForm, false);
        return ResultVO.success();
    }


    /**
     * 扩展阅读-多语言-扩展阅读表数据
     *
     * @param
     * @return
     */
    @GetMapping("/multi/reading/getReading")
    public ResultVO<ResultReadingForm> getMultiReading(@Param("tag") @NonNull String tag) {
        return ResultVO.success(readingService.getByTag(tag, true));
    }

    /**
     * 扩展阅读-繁体-扩展阅读表数据
     *
     * @param
     * @return
     */
    @GetMapping("/traditional/reading/getReading")
    public ResultVO<ResultReadingForm> getTraditionalReading(@Param("tag") @NonNull String tag) {
        return ResultVO.success(readingService.getByTag(tag, false));
    }

    /**
     * 扩展阅读-多语言-新增或修改扩展阅读表数据
     *
     * @param
     * @return
     */
    @PostMapping("/multi/reading/saveReading")
    public ResultVO<Void> addMultiReading(@RequestBody ResultReadingForm readingForm) {
        readingService.save(readingForm, true);
        return ResultVO.success();
    }

    /**
     * 扩展阅读-繁体-新增或修改扩展阅读表数据
     *
     * @param
     * @return
     */
    @PostMapping("/traditional/reading/saveReading")
    public ResultVO<Void> addTraditionalReading(@RequestBody ResultReadingForm readingForm) {
        readingService.save(readingForm, false);
        return ResultVO.success();
    }


}
