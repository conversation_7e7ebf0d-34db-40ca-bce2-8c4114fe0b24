package com.miaowen.bh1xlhw.controller.goods;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsCategoryMultilingualVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsCategoryTraditionalVO;
import com.miaowen.bh1xlhw.service.good.GoodsCategoryService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * 商品类型
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08
 */
@RestController
@RequestMapping("/goods/category")
@AllArgsConstructor
public class GoodsCategoryController {

    private GoodsCategoryService goodsCategoryService;

    /**
     * 保存多语言商品类型
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:multilingual:add')")
    @PostMapping("/multilingual")
    public ResultVO<Void> saveGoodCategoryMultilingual(@RequestBody GoodsCategoryMultilingualForm goodsCategoryMultilingualForm) {
        return goodsCategoryService.saveGoodsCategoryMultilingual(goodsCategoryMultilingualForm);

    }

    /**
     * 修改多语言商品类型
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:multilingual:edit')")
    @PutMapping("/multilingual/{id}")
    public ResultVO<Void> updateGoodCategoryMultilingual(@PathVariable Integer id, @RequestBody GoodsCategoryMultilingualForm goodsCategoryMultilingualForm) {
        return goodsCategoryService.updateGoodsCategoryMultilingual(id, goodsCategoryMultilingualForm);
    }

    /**
     * 获取多语言商品类型列表
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:multilingual:page')")
    @GetMapping("/multilingual")
    public ResultVO<PageVO<GoodsCategoryMultilingualVO>> pageGoodTypeMultilingual(@ModelAttribute GoodsCategoryPageForm pageForm) {
        return ResultVO.success(goodsCategoryService.pageGoodsCategoryMultilingual(pageForm));
    }

    /**
     * 获取多语言商品类型所有数据列表
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:multilingual:all')")
    @GetMapping("/multilingual/all")
    public ResultVO<List<GoodsCategoryMultilingualVO>> listGoodTypeMultilingual() {
        return ResultVO.success(goodsCategoryService.listGoodTypeMultilingual());
    }

    /**
     * 删除多语言商品类型
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:multilingual:del')")
    @DeleteMapping("/multilingual/{id}")
    public ResultVO<Void> deleteGoodCategoryMultilingual(@PathVariable Integer id) {
        goodsCategoryService.deleteGoodTypeMultilingual(id);
        return ResultVO.success();
    }


    /**
     * 保存繁体商品类型
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:complex:add')")
    @PostMapping("/traditional")
    public ResultVO<Void> saveGoodCategoryTraditional(@RequestBody GoodsCategoryTraditionalForm goodsCategoryTraditionalForm) {
        return goodsCategoryService.saveGoodsCategoryTraditional(goodsCategoryTraditionalForm);
    }

    /**
     * 修改繁体商品类型
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:complex:edit')")
    @PutMapping("/traditional/{id}")
    public ResultVO<Void> updateGoodsCategoryTraditional(@PathVariable Integer id,
                                                         @RequestBody GoodsCategoryTraditionalForm goodsCategoryTraditionalForm) {
        return goodsCategoryService.updateGoodsCategoryTraditional(id, goodsCategoryTraditionalForm);
    }

    /**
     * 获取繁体商品类型列表
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:complex:page')")
    @GetMapping("/traditional")
    public ResultVO<PageVO<GoodsCategoryTraditionalVO>> pageGoodsCategoryTraditional(@ModelAttribute GoodsCategoryPageForm pageForm) {
        return ResultVO.success(goodsCategoryService.pageGoodsCategoryTraditional(pageForm));
    }

    /**
     * 获取繁体商品类型所有数据列表
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:complex:all')")
    @GetMapping("/traditional/all")
    public ResultVO<List<GoodsCategoryTraditionalVO>> listGoodsCategoryTraditional() {
        return ResultVO.success(goodsCategoryService.listGoodsCategoryTraditional());
    }

    /**
     * 删除繁体商品类型
     */
    
    @PreAuthorize("hasAuthority('goods-manage:goods-type:complex:del')")
    @DeleteMapping("/traditional/{id}")
    public ResultVO<Void> deleteGoodsCategoryTraditional(@PathVariable Integer id) {
        goodsCategoryService.deleteGoodsCategoryTraditional(id);
        return ResultVO.success();
    }
}
