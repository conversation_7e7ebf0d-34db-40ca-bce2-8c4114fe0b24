package com.miaowen.bh1xlhw.controller.goods;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.goods.ResultMBTIPriceForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultMBTIWeiduForm;
import com.miaowen.bh1xlhw.service.good.ResultSettingMBTIService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * mbti维度结果设置
 *
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/6/5 15:43
 */
@RestController
@RequestMapping("/resultSetting")
@AllArgsConstructor
public class ResultSettingMBTIController {

    private final ResultSettingMBTIService resultSettingMBTIService;

    /**
     * 保存或更新
     *
     * @param form
     * @return
     */
    @PostMapping("/mbti/saveOrUpdate")
    public ResultVO<Void> saveOrUpdate(@RequestBody ResultMBTIWeiduForm form) {
        resultSettingMBTIService.saveOrUpdate(form);
        return ResultVO.success();
    }

    /**
     * 获取数据
     *
     * @return
     */
    @GetMapping("/mbti")
    public ResultVO<List<ResultMBTIWeiduForm>> list(@RequestParam("version") Integer version) {
        return ResultVO.success(resultSettingMBTIService.listAll(version));
    }


    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    @DeleteMapping("/mbti/{id}")
    public ResultVO<Void> delete(@PathVariable Integer id) {
        resultSettingMBTIService.delete(id);
        return ResultVO.success();
    }

    /**
     * 保存或更新价格
     *
     * @param form
     * @return
     */
    @PostMapping("/mbti/saveOrUpdatePrice")
    public ResultVO<Void> saveOrUpdate(@RequestBody ResultMBTIPriceForm form) {
        resultSettingMBTIService.saveOrUpdatePrice(form);
        return ResultVO.success();
    }

    /**
     * 获取价格
     *
     * @return
     */
    @GetMapping("/mbti/price")
    public ResultVO<ResultMBTIPriceForm> getPrice() {
        return ResultVO.success(resultSettingMBTIService.listAllPrice());
    }

}
