package com.miaowen.bh1xlhw.controller.order;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.constant.RedisConstant;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;
import com.miaowen.bh1xlhw.model.query.order.OrderMbtiSubCreateForm;
import com.miaowen.bh1xlhw.model.query.order.RefundForm;
import com.miaowen.bh1xlhw.model.query.order.SendForm;
import com.miaowen.bh1xlhw.model.vo.order.OrderDetailVO;
import com.miaowen.bh1xlhw.model.vo.order.OrderListVO;
import com.miaowen.bh1xlhw.model.vo.order.OrderMbtiSubVO;
import com.miaowen.bh1xlhw.service.CommonService;
import com.miaowen.bh1xlhw.service.order.OrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * 订单管理
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 14:20:44
 */
@Slf4j
@RestController
@AllArgsConstructor

@PreAuthorize("hasAuthority('order-manage:unpaid-order:detail')")
@RequestMapping("/order")
public class OrderController {
    private final OrderService orderService;
    private final CommonService commonService;


    /**
     * 获取交易订单成功表信息列表
     */
    
    @PreAuthorize("hasAuthority('order-manage:paid-order:refund')")
    @GetMapping("/paid")
    public ResultVO<PageVO<OrderListVO>> pageSuccessOrder(@ModelAttribute @Validated OrderForm orderForm) {
        return ResultVO.success(orderService.pageSuccessOrder(orderForm));
    }


    /**
     * 获取交易订单未支付表信息列表
     */
    
    @PreAuthorize("hasAuthority('order-manage:unpaid-order:detail')")
    @GetMapping("")
    public ResultVO<PageVO<OrderListVO>> pageOrder(@ModelAttribute @Validated OrderForm orderForm) {
        return ResultVO.success(orderService.pageOrder(orderForm));
    }

    /**
     * 获取交易订单详情
     */
    
    @PreAuthorize("hasAuthority('order-manage:unpaid-order:detail')")
    @GetMapping("/{id}")
    public ResultVO<OrderDetailVO> orderDetail(@PathVariable Long id) {
        return ResultVO.success(orderService.orderDetail(id));
    }

    /**
     * 查询主订单的子顶动感
     */
    @GetMapping("/{id}/sub")
    public ResultVO<List<OrderMbtiSubVO>> orderSubList(@PathVariable Long id) {
        return ResultVO.success(orderService.orderSubList(id));
    }

    /**
     * 退款
     */
    
    @PreAuthorize("hasAuthority('order-manage:paid-order:refund')")
    @PostMapping("/refund")
    public ResultVO<Void> refund(@RequestBody RefundForm refundForm) {
        commonService.limit();
        orderService.refund(refundForm);
        return ResultVO.success();
    }


    /**
     * 未支付订单-修改为已支付
     */
    @PostMapping("/changeStatus")
    public ResultVO<Void> changeStatus(@RequestBody SendForm form) {
        orderService.changeStatus(form.getId());
        return ResultVO.success();
    }

    /**
     * 修改结果查看状态
     */
    @PostMapping("/changeReadStatus")
    public ResultVO<Void> changeReadStatus(@RequestBody SendForm form) {
        orderService.changeReadStatus(form.getId());
        return ResultVO.success();
    }

    /**
     * 发送邮件报告
     */
    @PostMapping("/sendEmail")
    public ResultVO<Void> sendEmail(@RequestBody SendForm form) {
        orderService.sendEmail(form);
        return ResultVO.success();
    }


    /**
     * mbti子订单
     */
    
    @PreAuthorize("hasAuthority('order-manage:mbti-order:page')")
    @GetMapping("/mbti/sub")
    public ResultVO<PageVO<OrderMbtiSubVO>> orderMbtiSubPage(@ModelAttribute @Validated OrderForm orderForm) {
        return ResultVO.success(orderService.orderMbtiSubPage(orderForm));
    }


    /**
     * 后台创建mbti子订单
     */
    @PostMapping("/mbti/sub")
    public ResultVO<Void> orderMbtiSubCreate(@RequestBody OrderMbtiSubCreateForm orderMbtiSubCreateForm) {
        String key = String.format(RedisConstant.LOCK_MBTI, orderMbtiSubCreateForm.getMainOutTradeNo());
        commonService.limit(key);
        orderService.orderMbtiSubCreate(orderMbtiSubCreateForm);
        return ResultVO.success();
    }


    /**
     * 退款
     */
    @PostMapping("/mbti/sub/refund")
    public ResultVO<Void> mbtiSubRefund(@RequestBody RefundForm refundForm) {
        commonService.limit();
        orderService.mbtiSubRefund(refundForm);
        return ResultVO.success();
    }

}
