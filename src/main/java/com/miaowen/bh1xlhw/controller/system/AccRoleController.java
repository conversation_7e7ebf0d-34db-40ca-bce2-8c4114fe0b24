package com.miaowen.bh1xlhw.controller.system;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.auth.AccRoleForm;
import com.miaowen.bh1xlhw.model.query.auth.AccRolePageForm;
import com.miaowen.bh1xlhw.model.query.auth.AuthorizeForm;
import com.miaowen.bh1xlhw.model.vo.system.RoleVO;
import com.miaowen.bh1xlhw.service.system.SystemService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 权限-角色管理
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/accRole")
@AllArgsConstructor
public class AccRoleController {

    private final SystemService systemService;

    /**
     * 分页接口
     */
    @GetMapping("/list")
    public ResultVO<List<RoleVO>> list() {
        return ResultVO.success(systemService.list());
    }

    /**
     * 分页接口
     */
    @GetMapping("/page")
    public ResultVO<PageVO<RoleVO>> page(@ModelAttribute @Validated AccRolePageForm form) {
        return ResultVO.success(systemService.pageRole(form));
    }

    /**
     * 新增或删除
     */
    @PostMapping("/save")
    public ResultVO<Void> save(@RequestBody @Validated AccRoleForm accRoleForm) {
        systemService.saveOrUpdateRole(accRoleForm);
        return ResultVO.success();
    }

    /**
     * 设置权限
     */
    @PutMapping("/authorize")
    public ResultVO<?> authorize(@RequestBody @Validated AuthorizeForm form) {
        systemService.authorize(form);
        systemService.clear();
        return ResultVO.success();
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    public ResultVO<Void> delete(@PathVariable Integer id) {
        systemService.removeRole(id);
        systemService.clear();
        return ResultVO.success();
    }

}
