package com.miaowen.bh1xlhw.controller.system;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.auth.AccPermissionPageForm;
import com.miaowen.bh1xlhw.model.query.auth.PermissionForm;
import com.miaowen.bh1xlhw.model.vo.system.PermissionVO;
import com.miaowen.bh1xlhw.service.system.SystemService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *
 * @Description 菜单按钮权限接口
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/accPermission")
@AllArgsConstructor
public class AccPermissionController {

    private final SystemService systemService;

    /**
     * 权限菜单列表
     */
    @GetMapping("/list")
    public ResultVO<List<PermissionVO>> list() {
        return ResultVO.success(systemService.listPermission());
    }

    /**
     * 分页接口
     */
    @GetMapping("/page")
    public ResultVO<?> page(@ModelAttribute @Validated AccPermissionPageForm form) {
        return ResultVO.success(systemService.pagePermission(form));
    }

    /**
     * 保存权限菜单
     */
    @PostMapping("/saveOrUpdate")
    public ResultVO<Void> saveOrUpdate(@RequestBody @Validated PermissionForm permissionForm) {
        systemService.saveOrUpdatePermission(permissionForm);
        systemService.clear();
        return ResultVO.success();
    }

    /**
     * 删除权限菜单以及子菜单
     */
    @DeleteMapping("/{id}")
    public ResultVO<Void> delete(@PathVariable Integer id) {
        systemService.removePermission(id);
        systemService.clear();
        return ResultVO.success();
    }

    /**
     * 菜单权限清除
     */
    @GetMapping("/clear")
    public ResultVO<?> clear() {
        return systemService.clear();
    }



}
