package com.miaowen.bh1xlhw.controller.system;


import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.auth.AccUserAddOrUpdateForm;
import com.miaowen.bh1xlhw.model.query.auth.AccUserPageForm;
import com.miaowen.bh1xlhw.model.query.auth.UpdatePasswordForm;
import com.miaowen.bh1xlhw.model.vo.system.AccMyInfoVo;
import com.miaowen.bh1xlhw.model.vo.system.AccUserDetailsVO;
import com.miaowen.bh1xlhw.model.vo.system.AccUserPageVO;
import com.miaowen.bh1xlhw.model.vo.system.UserPermissionVO;
import com.miaowen.bh1xlhw.service.system.SystemService;
import com.miaowen.bh1xlhw.utils.SecurityUtil;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 *
 * @Description 管理员模块
 * <AUTHOR>
 * @Date 2025/5/14 09:05
 */
@RestController
@RequestMapping("/accUser")
@AllArgsConstructor
public class AccUserController {

    private final SystemService systemService;

    /**
     * 获取我的菜单权限，按钮菜单分开
     */
    @GetMapping("/getMyMenu")
    public ResultVO<UserPermissionVO> getMyMenu() {
        Integer myUserId = SecurityUtil.currentUser().getUserId();
        return ResultVO.success(systemService.getMyMenu(myUserId));
    }

    /**
     * 个人信息
     */
    @GetMapping("/getMyInfo")
    public ResultVO<AccMyInfoVo> getMyInfo() {
        Integer myUserId = SecurityUtil.currentUser().getUserId();
        AccMyInfoVo accMyInfoVo = systemService.getMyInfo(myUserId);
        return ResultVO.success(accMyInfoVo);
    }

    /**
     * 修改密码
     */
    @PutMapping("/updatePassword")
    public ResultVO<Void> updatePassword(@RequestBody @Validated UpdatePasswordForm form) {
        systemService.updatePassword(form);
        return ResultVO.success();
    }

    /**
     * 用户分页
     */
    @GetMapping("/page")
    public ResultVO<PageVO<AccUserPageVO>> page(@ModelAttribute @Validated AccUserPageForm form) {
        return systemService.page(form);
    }

    /**
     * 用户详情
     */
    @GetMapping("/details")
    public ResultVO<AccUserDetailsVO> details(Integer id) {
        return ResultVO.success(systemService.getDetail(id));
    }

    /**
     * 用户新增或修改
     */
    @PostMapping("/addOrUpdate")
    public ResultVO<Void> addOrUpdate(@RequestBody @Validated AccUserAddOrUpdateForm form) {
        if (form.getId() == null) {
            systemService.add(form);
        } else {
            systemService.update(form);
        }
        return ResultVO.success();
    }

    /**
     * 用户删除
     */
    @DeleteMapping("/{id}")
    public ResultVO<Void> delete(@PathVariable Integer id) {
        systemService.removeUser(id);
        return ResultVO.success();
    }

}
