package com.miaowen.bh1xlhw.controller.system;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryTypeForm;
import com.miaowen.bh1xlhw.model.query.system.UpdateStateForm;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryTypeVO;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryVO;
import com.miaowen.bh1xlhw.service.system.DictionaryConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * 系统-字典配置
 * DictionaryController :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08
 */
@Slf4j
@RestController
@RequestMapping("/dictionary")
@AllArgsConstructor
public class DictionaryConfigController {
    private final DictionaryConfigService dictionaryConfigService;

    /**
     * 保存字典
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:config:edit')")
    @PostMapping("/save")
    public ResultVO<Void> saveDictionary(@RequestBody @Validated DictionaryForm dictionaryForm) {
        if (dictionaryForm.getId() == null) {
            dictionaryConfigService.addDictionary(dictionaryForm);
        } else {
            dictionaryConfigService.updateDictionary(dictionaryForm);
        }
        return ResultVO.success();
    }

    /**
     * 批量删除字典
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:config:del')")
    @DeleteMapping("/batch")
    public ResultVO<Void> deleteBatchDictionary(@RequestBody @Validated DeleteBatchForm form) {
        dictionaryConfigService.deleteBatchDictionary(form);
        return ResultVO.success();
    }

    /**
     * 分页查询字典
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:config:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<DictionaryVO>> pageDictionary(@ModelAttribute @Validated DictionaryPageForm form) {
        return ResultVO.success(dictionaryConfigService.pageDictionary(form));
    }

    /**
     * 修改字典状态
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:config:state')")
    @PutMapping("/state")
    public ResultVO<Void> updateDictionaryState(@RequestBody @Validated UpdateStateForm form) {
        dictionaryConfigService.updateStateDictionary(form);
        return ResultVO.success();
    }

    /**
     * 保存字典类型
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:edit')")
    @PostMapping("/type/save")
    public ResultVO<Void> saveDictionaryType(@RequestBody @Validated DictionaryTypeForm dictionaryTypeForm) {
        if (dictionaryTypeForm.getId() == null) {
            dictionaryConfigService.addDictionaryType(dictionaryTypeForm);
        } else {
            dictionaryConfigService.updateDictionaryType(dictionaryTypeForm);
        }
        return ResultVO.success();
    }

    /**
     * 批量删除字典类型
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:del')")
    @DeleteMapping("/type/batch")
    public ResultVO<Void> deleteBatchDictionaryType(@RequestBody @Validated DeleteBatchForm form) {
        dictionaryConfigService.deleteBatchDictionaryType(form);
        return ResultVO.success();
    }

    /**
     * 分页查询字典类型
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:page')")
    @GetMapping("/type/page")
    public ResultVO<PageVO<DictionaryTypeVO>> pageDictionaryType(@ModelAttribute @Validated DictionaryPageForm form) {
        return ResultVO.success(dictionaryConfigService.pageDictionaryType(form));
    }

    /**
     * 根据字典类型查询所有字典
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:type:list')")
    @GetMapping("/type/list")
    public ResultVO<List<DictionaryVO>> listDictionaryType(@RequestParam("code") String code) {
        return ResultVO.success(dictionaryConfigService.listDictionaryByCode(code));
    }

    /**
     * 修改字典类型状态
     */
    
    @PreAuthorize("hasAuthority('system:dict-manage:status')")
    @PutMapping("/type/status")
    public ResultVO<Void> updateDictionaryTypeState(@RequestBody @Validated UpdateStateForm form) {
        dictionaryConfigService.updateStateDictionaryType(form);
        return ResultVO.success();
    }
}
