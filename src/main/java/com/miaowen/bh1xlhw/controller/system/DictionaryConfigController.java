package com.miaowen.bh1xlhw.controller.system;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryTypeForm;
import com.miaowen.bh1xlhw.model.query.system.UpdateStateForm;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryTypeVO;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryVO;
import com.miaowen.bh1xlhw.service.system.DictionaryConfigService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 系统-字典配置
 * DictionaryController :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08
 */
@Slf4j
@RestController
@RequestMapping("/dictionary")
@AllArgsConstructor
public class DictionaryConfigController {
    private final DictionaryConfigService dictionaryConfigService;

    /**
     * 保存字典
     */
    @PostMapping("/save")
    public ResultVO<Void> saveDictionary(@RequestBody @Validated DictionaryForm dictionaryForm) {
        if (dictionaryForm.getId() == null) {
            dictionaryConfigService.addDictionary(dictionaryForm);
        } else {
            dictionaryConfigService.updateDictionary(dictionaryForm);
        }
        return ResultVO.success();
    }

    /**
     * 批量删除字典
     */
    @DeleteMapping("/batch")
    public ResultVO<Void> deleteBatchDictionary(@RequestBody @Validated DeleteBatchForm form) {
        dictionaryConfigService.deleteBatchDictionary(form);
        return ResultVO.success();
    }

    /**
     * 分页查询字典
     */
    @GetMapping("/page")
    public ResultVO<PageVO<DictionaryVO>> pageDictionary(@ModelAttribute @Validated DictionaryPageForm form) {
        return ResultVO.success(dictionaryConfigService.pageDictionary(form));
    }

    /**
     * 修改字典状态
     */
    @PutMapping("/state")
    public ResultVO<Void> updateDictionaryState(@RequestBody @Validated UpdateStateForm form) {
        dictionaryConfigService.updateStateDictionary(form);
        return ResultVO.success();
    }

    /**
     * 保存字典类型
     */
    @PostMapping("/type/save")
    public ResultVO<Void> saveDictionaryType(@RequestBody @Validated DictionaryTypeForm dictionaryTypeForm) {
        if (dictionaryTypeForm.getId() == null) {
            dictionaryConfigService.addDictionaryType(dictionaryTypeForm);
        } else {
            dictionaryConfigService.updateDictionaryType(dictionaryTypeForm);
        }
        return ResultVO.success();
    }

    /**
     * 批量删除字典类型
     */
    @DeleteMapping("/type/batch")
    public ResultVO<Void> deleteBatchDictionaryType(@RequestBody @Validated DeleteBatchForm form) {
        dictionaryConfigService.deleteBatchDictionaryType(form);
        return ResultVO.success();
    }

    /**
     * 分页查询字典类型
     */
    @GetMapping("/type/page")
    public ResultVO<PageVO<DictionaryTypeVO>> pageDictionaryType(@ModelAttribute @Validated DictionaryPageForm form) {
        return ResultVO.success(dictionaryConfigService.pageDictionaryType(form));
    }

    /**
     * 根据字典类型查询所有字典
     */
    @GetMapping("/type/list")
    public ResultVO<List<DictionaryVO>> listDictionaryType(@RequestParam("code") String code) {
        return ResultVO.success(dictionaryConfigService.listDictionaryByCode(code));
    }

    /**
     * 修改字典类型状态
     */
    @PutMapping("/type/status")
    public ResultVO<Void> updateDictionaryTypeState(@RequestBody @Validated UpdateStateForm form) {
        dictionaryConfigService.updateStateDictionaryType(form);
        return ResultVO.success();
    }
}
