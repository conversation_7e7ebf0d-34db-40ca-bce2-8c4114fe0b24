package com.miaowen.bh1xlhw.controller;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.security.login.UsernameAuthenticationToken;
import com.miaowen.bh1xlhw.model.query.login.LoginForm;
import com.miaowen.bh1xlhw.model.vo.login.TokenVO;
import com.miaowen.bh1xlhw.service.auth.LoginService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 登录
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2024/3/19 14:54
 */
@Slf4j
@RestController
@AllArgsConstructor
public class LoginController {

    @Resource
    private final LoginService loginService;

    /**
     * 验证码
     */
    @GetMapping("/verify")
    public ResultVO<Map<String, Object>> verify() {
        return ResultVO.success(loginService.verify());
    }

    /**
     * 推出登录
     */
    @GetMapping("/oauth/logout")
    public ResultVO<Void> logout() {
        loginService.logout();
        return ResultVO.success();
    }

    /**
     * 登录
     */
    @PostMapping("/adminLogin")
    public ResultVO<TokenVO> adminLogin(@RequestBody LoginForm form)  {
        UsernameAuthenticationToken token = new UsernameAuthenticationToken(form.getUsername(), form.getPassword(),
            form.getVerificationCode(),
            form.getVerificationCodeUuid());
        return ResultVO.success(loginService.createAccessToken(token));
    }

    /**
     * 刷新token
     */
    @PostMapping("/adminRefreshToken")
    public ResultVO<TokenVO> adminRefreshToken(){
        return ResultVO.success(loginService.refreshToken());
    }

}
