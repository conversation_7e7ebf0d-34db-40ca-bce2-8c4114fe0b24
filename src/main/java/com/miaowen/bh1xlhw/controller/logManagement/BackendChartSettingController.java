package com.miaowen.bh1xlhw.controller.logManagement;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.service.logManagement.BackendChartSettingService;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  后台图表配置
 * @Author：huanglong
 * @Date：2025/5/9 14:12
 */
@RestController
@RequestMapping("/chartSetting")
@AllArgsConstructor
public class BackendChartSettingController {
    private final BackendChartSettingService backendChartSettingService;

    /**
     * 保存后台图表配置
     * @param chartCode
     * @return
     */
    @GetMapping("/save")
    public ResultVO<Void> chartSetting(@Param("chartCode") String chartCode){
        return backendChartSettingService.chartSetting(chartCode);
    }

    /**
     * 获取后台图表配置
     * @return
     */
    @GetMapping("/get")
    public ResultVO<String> getChartSetting(){
        return ResultVO.success(backendChartSettingService.getChartSetting());
    }
}
