package com.miaowen.bh1xlhw.controller.logManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.logManagement.EventSettingForm;
import com.miaowen.bh1xlhw.model.query.logManagement.EventSettingPageForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SystemEventVo;
import com.miaowen.bh1xlhw.service.logManagement.EventSettingService;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.util.AntPathMatcher;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 事件配置
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/7 10:45R
 */
@RestController
@RequestMapping("/eventSetting")
@AllArgsConstructor
public class EventSettingController {

    private final EventSettingService eventSettingService;

    /**
     * 分页查询
     *
     * @return 查询结果
     */
    
    @PreAuthorize("hasAuthority('log-manage:event-config:page')")
    @GetMapping("/page")
    public ResultVO<PageVO<SystemEventVo>> page(@ModelAttribute @Validated EventSettingPageForm pageForm) {
        return ResultVO.success(eventSettingService.page(pageForm));
    }

    /**
     * 删除事件配置
     *
     * @param ids id集合
     * @return 删除结果
     */
    
    @PreAuthorize("hasAuthority('log-manage:event-config:del')")
    @DeleteMapping("/delete")
    public ResultVO<Void> delete(@RequestBody List<Long> ids) {
        eventSettingService.delete(ids);
        return ResultVO.success();
    }

    /**
     * 添加事件配置
     *
     * @param settingForm 添加类
     * @return 添加结果
     */
    
    @PreAuthorize("hasAuthority('log-manage:event-config:add')")
    @PostMapping("/add")
    public ResultVO<Void> add(@RequestBody EventSettingForm settingForm) {
        return eventSettingService.add(settingForm);
    }

    /**
     * 修改事件配置
     */
    
    @PreAuthorize("hasAuthority('log-manage:event-config:edit')")
    @PostMapping("/update")
    public ResultVO<Void> update(HttpServletRequest request, @RequestBody EventSettingForm settingForm) {
        return eventSettingService.update(settingForm);
    }


}
