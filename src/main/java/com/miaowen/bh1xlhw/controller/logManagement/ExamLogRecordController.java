package com.miaowen.bh1xlhw.controller.logManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.logManagement.ExamLogForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.ExamLogVO;
import com.miaowen.bh1xlhw.service.logManagement.ExamLogRecordService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 做题日志
 * @Author：huanglong
 * @Date：2025/5/8 10:28
 */
@RestController
@RequestMapping("/examLog")
@AllArgsConstructor
public class ExamLogRecordController {

    private final ExamLogRecordService examLogRecordService;

    /**
     * 分页查询做题日志
     * @param pageForm
     * @return
     */
    @GetMapping("/page")
    public ResultVO<PageVO<ExamLogVO>> page(@ModelAttribute @Validated ExamLogForm pageForm) {
        return examLogRecordService.page(pageForm);
    }

}
