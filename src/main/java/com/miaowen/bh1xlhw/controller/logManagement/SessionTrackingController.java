package com.miaowen.bh1xlhw.controller.logManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.logManagement.SessionTrackingForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SessionTrackingVO;
import com.miaowen.bh1xlhw.service.logManagement.SessionTrackingService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会话跟踪
 * @Author：huanglong
 * @Date：2025/5/8 11:36
 */
@RestController
@RequestMapping("/sessionTracking")
@AllArgsConstructor
public class SessionTrackingController {
    private final SessionTrackingService sessionTrackingService;

    /**
     * 分页查询追踪日志
     * @param pageForm
     * @return
     */
    @GetMapping("/page")
    public ResultVO<PageVO<SessionTrackingVO>> page(@ModelAttribute @Validated SessionTrackingForm pageForm) {
        return ResultVO.success(sessionTrackingService.page(pageForm));
    }
}
