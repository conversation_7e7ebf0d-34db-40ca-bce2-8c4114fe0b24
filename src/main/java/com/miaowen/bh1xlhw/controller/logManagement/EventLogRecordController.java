package com.miaowen.bh1xlhw.controller.logManagement;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.model.query.logManagement.EventLogForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventBrowseCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventLogRecordVO;
import com.miaowen.bh1xlhw.service.logManagement.EventLogRecordService;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;
import org.springframework.security.access.prepost.PreAuthorize;

import java.util.List;

/**
 * 事件日志
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/8 16:01
 */
@RestController
@RequestMapping("/eventLogRecord")
@AllArgsConstructor
public class EventLogRecordController {
    private final EventLogRecordService eventLogRecordService;

    /**
     * 访问轨迹-获取事件日志集合
     *
     * @param
     * @return
     */
    
    @PreAuthorize("hasAuthority('order-manage:unpaid-order:eventLogRecord')")
    @GetMapping("/list")
    public ResultVO<List<EventLogRecordVO>> list(@RequestParam(value = "orderId") Integer orderId) {
        return ResultVO.success(eventLogRecordService.list(orderId));
    }

    /**
     * 访问轨迹-根据logId获取事件日志集合
     *
     * @param
     * @return
     */
    @GetMapping("/listByLogId")
    public ResultVO<List<EventLogRecordVO>> listByLogId(@RequestParam(value = "logId") String logId) {
        return ResultVO.success(eventLogRecordService.listByLogId(logId));
    }

    /**
     * 浏览事件统计
     *
     * @return
     */
    
    @PreAuthorize("hasAuthority('data-statistics:count')")
    @GetMapping("/count")
    public ResultVO<List<EventCountVO>> count(@Param("platformCode") String platformCode) {
        return ResultVO.success(eventLogRecordService.count(platformCode));
    }

    /**
     * 推广浏览统计
     *
     * @return
     */
    @GetMapping("/countByTgid")
    public ResultVO<List<EventCountVO>> countByTgid(@Param("tgid") String tgid, @Param("platformCode") String platformCode) {
        return ResultVO.success(eventLogRecordService.countByTgid(tgid, platformCode));
    }

    /**
     * 浏览统计
     *
     * @return
     */
    
    @PreAuthorize("hasAuthority('goods-manage:promotion-goods:multilingual:data-statics')")
    @GetMapping("/browseCount")
    public ResultVO<List<EventBrowseCountVO>> browseCount(@ModelAttribute BrowseCountForm form) {
        return ResultVO.success(eventLogRecordService.browseCount(form));
    }

}
