package com.miaowen.bh1xlhw.controller.log;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.log.LogPageForm;
import com.miaowen.bh1xlhw.model.vo.log.SystemLogsVO;
import com.miaowen.bh1xlhw.service.log.LogsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 系统日志
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@RestController
@RequestMapping("/logs")
@AllArgsConstructor
public class LogsController {
    private final LogsService logsService;

    /**
     * 批量删除记录
     */
    @DeleteMapping("/batch")
    public ResultVO<?> deleteBatch(@RequestBody @Validated DeleteBatchForm form) {
        logsService.logicBatchDelete(form);
        return ResultVO.success();
    }

    /**
     * 分页查询
     */
    @GetMapping("/page")
    public ResultVO<PageVO<SystemLogsVO>> page(@ModelAttribute @Validated LogPageForm form) {
        return ResultVO.success(logsService.page(form));
    }

}
