package com.miaowen.bh1xlhw.utils;

import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.function.Supplier;

/**
 * 分表工具类
 * 提供便捷的分表操作方法
 * 
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
public class ShardingUtils {
    
    private static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyyMM");
    
    /**
     * 执行带分表上下文的操作
     * 
     * @param dateSuffix 日期后缀
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithSharding(String dateSuffix, Supplier<T> operation) {
        try {
            MonthShardingTableNameHandler.setContext(dateSuffix);
            return operation.get();
        } finally {
            MonthShardingTableNameHandler.clearContext();
        }
    }
    
    /**
     * 执行带分表上下文的操作（无返回值）
     * 
     * @param dateSuffix 日期后缀
     * @param operation 要执行的操作
     */
    public static void executeWithSharding(String dateSuffix, Runnable operation) {
        try {
            MonthShardingTableNameHandler.setContext(dateSuffix);
            operation.run();
        } finally {
            MonthShardingTableNameHandler.clearContext();
        }
    }
    
    /**
     * 根据LocalDate执行分表操作
     * 
     * @param date 日期
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     */
    public static <T> T executeWithSharding(LocalDate date, Supplier<T> operation) {
        String suffix = date.format(MONTH_FORMAT);
        return executeWithSharding(suffix, operation);
    }
    
    /**
     * 根据LocalDate执行分表操作（无返回值）
     * 
     * @param date 日期
     * @param operation 要执行的操作
     */
    public static void executeWithSharding(LocalDate date, Runnable operation) {
        String suffix = date.format(MONTH_FORMAT);
        executeWithSharding(suffix, operation);
    }

    

}
