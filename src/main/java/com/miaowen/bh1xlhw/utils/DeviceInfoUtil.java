package com.miaowen.bh1xlhw.utils;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
public class DeviceInfoUtil {
    
    /**
     * 解析手机型号
     */
    public static String parsePhoneModel(String userAgent) {
        // iPhone
        if (userAgent.contains("iPhone")) {
            return "iPhone";
        }
        // Android设备
        else if (userAgent.contains("Android")) {
            // 尝试从User-Agent中提取具体型号
            Pattern pattern = Pattern.compile(";\\s(.*?)\\sBuild/");
            Matcher matcher = pattern.matcher(userAgent);
            if (matcher.find()) {
                return matcher.group(1);
            }
            return "Android Device";
        }
        return "Unknown Device";
    }
    
    /**
     * 解析屏幕尺寸
     */
    public static String parseScreenResolution(HttpServletRequest request) {
        // 从请求头获取屏幕尺寸
        String screenWidth = request.getHeader("Screen-Width");
        String screenHeight = request.getHeader("Screen-Height");
        
        if (StringUtils.isNotBlank(screenWidth) && StringUtils.isNotBlank(screenHeight)) {
            return screenWidth + "x" + screenHeight;
        }
        
        // 从User-Agent中获取设备信息
        String userAgent = request.getHeader("User-Agent");
        if (userAgent.contains("iPhone")) {
            // iPhone屏幕尺寸
            if (userAgent.contains("iPhone 12") || userAgent.contains("iPhone 13")) {
                return "1170x2532";
            } else if (userAgent.contains("iPhone 11")) {
                return "828x1792";
            }
            // 其他iPhone型号
            return "Unknown iPhone Resolution";
        }
        
        return "Unknown Resolution";
    }
    
    /**
     * 解析网络类型
     */
    public static String parseNetworkType(HttpServletRequest request) {
        // 从请求头获取网络类型
        String networkType = request.getHeader("Network-Type");
        if (StringUtils.isNotBlank(networkType)) {
            return networkType;
        }
        
        // 从User-Agent中获取网络信息
        String userAgent = request.getHeader("User-Agent");
        if (userAgent.contains("5G")) {
            return "5G";
        } else if (userAgent.contains("4G")) {
            return "4G";
        } else if (userAgent.contains("3G")) {
            return "3G";
        } else if (userAgent.contains("WiFi")) {
            return "WiFi";
        }
        
        return "Unknown Network";
    }

    /**
     * 解析浏览器类型和版本
     */
    public static String parseBrowserTypeAndVersion(String userAgent) {
        // Chrome浏览器
        if (userAgent.contains("Chrome")) {
            Pattern pattern = Pattern.compile("Chrome/([\\d.]+)");
            Matcher matcher = pattern.matcher(userAgent);
            if (matcher.find()) {
                return "Chrome " + matcher.group(1);
            }
        }
        // Firefox浏览器
        else if (userAgent.contains("Firefox")) {
            Pattern pattern = Pattern.compile("Firefox/([\\d.]+)");
            Matcher matcher = pattern.matcher(userAgent);
            if (matcher.find()) {
                return "Firefox " + matcher.group(1);
            }
        }
        // Safari浏览器
        else if (userAgent.contains("Safari")) {
            Pattern pattern = Pattern.compile("Version/([\\d.]+)");
            Matcher matcher = pattern.matcher(userAgent);
            if (matcher.find()) {
                return "Safari " + matcher.group(1);
            }
        }
        // Edge浏览器
        else if (userAgent.contains("Edg")) {
            Pattern pattern = Pattern.compile("Edg/([\\d.]+)");
            Matcher matcher = pattern.matcher(userAgent);
            if (matcher.find()) {
                return "Edg " + matcher.group(1);
            }
        }
        // IE浏览器
        else if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            Pattern pattern = Pattern.compile("MSIE\\s([\\d.]+)");
            Matcher matcher = pattern.matcher(userAgent);
            if (matcher.find()) {
                return "IE " + matcher.group(1);
            } else {
                pattern = Pattern.compile("rv:([\\d.]+)");
                matcher = pattern.matcher(userAgent);
                if (matcher.find()) {
                    return "IE " + matcher.group(1);
                }
            }
        }

        // 其他浏览器
        return "Unknown";
    }

}
