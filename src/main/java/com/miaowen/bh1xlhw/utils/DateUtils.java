/*******************    💫 Codegeex Inline Diff    *******************/
package com.miaowen.bh1xlhw.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/8 14:11
 */
public class DateUtils {
    public static final DateTimeFormatter DEFAULT_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter MONTH_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM");
    public static final DateTimeFormatter NUM_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter MONTH_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMM");


    /**
     * 判断两个日期是否在同一个月
     *
     * @param startDate 第一个日期
     * @param endDate   第二个日期
     * @return 如果在同一个月返回true，否则返回false
     */
    public static boolean isSameMonth(Date startDate, Date endDate) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();

        cal1.setTime(startDate);
        cal2.setTime(endDate);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);

        int month1 = cal1.get(Calendar.MONTH);
        int month2 = cal2.get(Calendar.MONTH);

        return year1 == year2 && month1 == month2;
    }

    // 获取本月第一天
    public static Date getFirstDayOfMonth() {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        // 设置为本月第一天
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        // 设置小时为0
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        // 设置分钟为0
        calendar.set(Calendar.MINUTE, 0);
        // 设置秒数为0
        calendar.set(Calendar.SECOND, 0);
        // 设置毫秒数为0
        calendar.set(Calendar.MILLISECOND, 0);
        // 返回本月第一天的时间
        return calendar.getTime();
    }

    /**
     * 根据时区和时间返回东八区 LocalDateTime
     *
     * @param timeZone 原始时区（如 "America/New_York"）
     * @param time     时间字符串（格式需与 formatter 匹配）
     * @return 东八区 LocalDateTime
     */
    public static LocalDateTime getEast8LocalDateTime(String timeZone, String time) {
        // 解析时间字符串为 LocalDateTime
        LocalDateTime localDateTime = LocalDateTime.parse(time, DEFAULT_FORMAT);
        // 原始时区 -> 转换为 ZonedDateTime
        ZonedDateTime sourceZoned = localDateTime.atZone(ZoneId.of(timeZone));
        // 转换为东八区时间
        ZonedDateTime east8Zoned = sourceZoned.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
        return east8Zoned.toLocalDateTime();
    }

    /**
     * 根据时区和时间返回东八区 LocalDateTime
     *
     * @param timeZone 原始时区（如 "America/New_York"）
     * @param time     时间字符串（格式需与 formatter 匹配）
     * @return 东八区 LocalDateTime
     */
    public static Date getEast8Date(String timeZone, String time) {
        return Date.from(getEast8LocalDateTime(timeZone, time).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 秒级时间戳转日期
     * @param second 秒级时间戳
     * @return 日期
     */
    public static LocalDateTime secondToLocalDateTime(Integer second) {
        return LocalDateTime.ofEpochSecond(second, 0, ZoneOffset.ofHours(8));
    }


    /**
     * 根据时区和时间返回东八区 LocalDateTime
     *
     * @param timeZone 原始时区（如 "America/New_York"）
     * @return 东八区 LocalDateTime
     */
    public static Date getEast8Date(String timeZone, Date date) {
        String time = getDateString(date);
        return Date.from(getEast8LocalDateTime(timeZone, time).atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDateTime 转 Date（系统默认时区）
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date getDateByLogId(String logId) {
        if (logId.length() != 20) {
            return null;
        }
        String substring = logId.substring(7);
        return new Date(Long.parseLong(substring));
    }

    /**
     * LocalDateTime 转 Date（系统默认时区）
     */
    public static String getDateString(LocalDateTime localDateTime) {
        return localDateTime.format(MONTH_DATE_FORMAT);
    }


    public static String getDateString(Date date) {
        DateTimeFormatter formatter = DateTimeFormatter
                .ofPattern("yyyy-MM-dd HH:mm:ss")
                .withZone(ZoneId.systemDefault());
        return formatter.format(date.toInstant());
    }

    public static Date getData(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Date date = null; // String → Date
        try {
            date = sdf.parse(dateStr);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return date;
    }

    public static String changeDate(Date date) {

        LocalDate localDate = date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        int year = localDate.getYear();   // 年
        int month = localDate.getMonthValue(); // 月（1~12）
        return String.format("%04d%02d", year, month);
    }

    /**
     * LocalDateTime 转 Date（指定时区）
     */
    public static Date toDate(LocalDateTime localDateTime, ZoneId zoneId) {
        return Date.from(localDateTime.atZone(zoneId).toInstant());
    }

    // 获取指定日期所在月份的最后一天的23:59:59秒的日期
    public static Date getLastDayOfMonth(Date date) {
        // 获取当前时间
        Calendar calendar = Calendar.getInstance();
        // 使用传入的日期设置Calendar的时间
        calendar.setTime(date);
        // 设置为本月最后一天
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        // 设置小时为23
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        // 设置分钟为59
        calendar.set(Calendar.MINUTE, 59);
        // 设置秒数为59
        calendar.set(Calendar.SECOND, 59);
        // 设置毫秒数为999
        calendar.set(Calendar.MILLISECOND, 999);
        // 返回本月最后一天的时间
        return calendar.getTime();
    }

}

