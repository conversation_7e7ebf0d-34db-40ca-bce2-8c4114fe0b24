package com.miaowen.bh1xlhw.utils;

import com.google.gson.*;
import com.google.gson.reflect.TypeToken;
import com.google.gson.stream.JsonReader;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public class GsonUtil {
    // 多例容器（线程安全）
    private static final Map<String, Gson> GSON_INSTANCES = new ConcurrentHashMap<>();

    // 实例类型枚举
    public enum GsonType {
        /**
         * 默认配置
         */
        DEFAULT,
        /**
         *  下划线转驼峰
         */
        LOWER_CASE,
        /**
         * 带日期格式
         */
        DATE_FORMATTED
    }

    // 私有构造
    private GsonUtil() {
        throw new AssertionError("不允许实例化工具类");
    }

    /**
     * 获取指定类型的 Gson 实例
     */
    public static Gson getGson(GsonType type) {
        return GSON_INSTANCES.computeIfAbsent(type.name(), k -> createGson(type));
    }

    public static Gson getGson() {
        return GSON_INSTANCES.computeIfAbsent(GsonType.DATE_FORMATTED.name(), k -> createGson(GsonType.DATE_FORMATTED));
    }

    /**
     * 创建指定配置的 Gson 实例
     */
    private static Gson createGson(GsonType type) {
        GsonBuilder builder = new GsonBuilder();

        switch (type) {
            case LOWER_CASE:
                builder.setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES);
                break;

            case DATE_FORMATTED:
                builder.setDateFormat("yyyy-MM-dd HH:mm:ss")
                    .setObjectToNumberStrategy(JsonReader::nextInt)
                    .setNumberToNumberStrategy(JsonReader::nextInt);
                break;

            case DEFAULT:
            default:
                break;
        }

        return builder.create();
    }

    // ------------------- 快捷访问方法 -------------------
    public static Gson getDefault() {
        return getGson(GsonType.DEFAULT);
    }

    public static Gson getLowerCase() {
        return getGson(GsonType.LOWER_CASE);
    }

    public static Gson getDateFormatted() {
        return getGson(GsonType.DATE_FORMATTED);
    }
    /**
     * 将对象转成json格式
     *
     * @param object
     * @return String
     */
    public static String gsonString(Object object) {
        Gson gson = getGson();
        String gsonString = null;
        if (gson != null) {
            gsonString = gson.toJson(object);
        }
        return gsonString;
    }

    /**
     * 将json转成特定的cls的对象
     *
     * @param gsonString
     * @param cls
     * @return
     */
    public static <T> T gsonToBean(String gsonString, Class<T> cls) {
        Gson gson = getGson();
        return gsonToBean(gson, gsonString, cls);
    }

    public static <T> T gsonToBean(Gson gson,String gsonString, Class<T> cls) {
        T t = null;
        if (gson != null) {
            //传入json对象和对象类型,将json转成对象
            t = gson.fromJson(gsonString, cls);
        }
        return t;
    }

    /**
     * json字符串转成list
     *
     * @param gsonString
     * @param cls
     * @return
     */
//    public static <T> List<T> gsonToList(String gsonString, Class<T> cls) {
//        Gson gson = new GsonBuilder().setObjectToNumberStrategy(in -> {
//            if (Integer.class.equals(cls)){
//                return in.nextInt();
//            } else if (Long.class.equals(cls)){
//                return in.nextDouble();
//            } else {
//                return in.nextLong();
//            }
//        }).setNumberToNumberStrategy(JsonReader::nextInt).setDateFormat(
//            "yyyy-MM-dd HH:mm:ss").create();
//        List<T> list = null;
//        //根据泛型返回解析指定的类型,TypeToken<List<T>>{}.getType()获取返回类型
//        list = gson.fromJson(gsonString, new TypeToken<List<T>>() {
//        }.getType());
//        if (CollectionUtils.isEmpty(list)){
//            return new ArrayList<>();
//        }
//        return list;
//    }

    /**
     * json字符串转成list
     *
     * @param json :
     * @param cls:
     * @return List<T>
     */
    public static <T> List<T> gsonToList(String json, Class<T> cls) {
        Gson gson = getGson();
        return gsonToList(gson, json, cls);
    }

    public static <T> List<T> gsonToList(Gson gson,String json, Class<T> cls) {
        ArrayList<T> mList = new ArrayList<T>();

        JsonArray array = JsonParser.parseString(json).getAsJsonArray();
        for (final JsonElement elem : array) {
            mList.add(gson.fromJson(elem, cls));
        }
        return mList;
    }

    /**
     * json字符串转成list中有map的
     *
     * @param gsonString
     * @return
     */
    public static <T> List<Map<String, T>> gsonToListMaps(String gsonString) {
        Gson gson = getGson();
        List<Map<String, T>> list = null;
        if (gson != null) {
            list = gson.fromJson(gsonString,
                new TypeToken<List<Map<String, T>>>() {
                }.getType());
        }
        return list;
    }

    /**
     * json字符串转成map的
     *
     * @param gsonString
     * @return
     */
    public static <T> Map<String, T> gsonToMaps(String gsonString) {
        Gson gson = getGson();
        Map<String, T> map = null;
        if (gson != null) {
            map = gson.fromJson(gsonString, new TypeToken<Map<String, T>>() {
            }.getType());
        }
        return map;
    }
}  
