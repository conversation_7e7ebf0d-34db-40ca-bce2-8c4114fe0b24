package com.miaowen.bh1xlhw.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public class AesCryptoUtil {


    /**
     * 生成加密的退订令牌
     * @param text 用户邮箱
     * @return 加密后的Base64URL安全字符串
     */
    public static String generateUnsubscribeToken(String secretKey, String text)  {
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, secretKey.getBytes(StandardCharsets.UTF_8));
        return aes.encryptHex(text);
    }

    /**
     * 解密退订令牌
     * @param token 加密后的令牌
     * @return 原始邮箱（解密失败返回null）
     */
    public static String decryptUnsubscribeToken(String secretKey, String token) {
        SymmetricCrypto aes = new SymmetricCrypto(SymmetricAlgorithm.AES, secretKey.getBytes(StandardCharsets.UTF_8));
        return aes.decryptStr(token, CharsetUtil.CHARSET_UTF_8);
    }
}
