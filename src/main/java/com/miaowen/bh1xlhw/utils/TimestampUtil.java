package com.miaowen.bh1xlhw.utils;

import lombok.Data;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.Objects;

/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class TimestampUtil {

    // 时间单位常量（秒）
    private static final Integer SECOND = 1;
    private static final Integer MINUTE = 60 * SECOND;
    private static final Integer HOUR = 60 * MINUTE;
    private static final Integer DAY = 24 * HOUR;
    private static final Integer WEEK = 7 * DAY;
    private static final Integer MONTH = 30 * DAY;
    private static final Integer YEAR = 365 * DAY;

    public static class Pattern {
        public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    }

    public static int currentTimestamp() {
        return (int) (System.currentTimeMillis() / 1000);
    }

    public static String currentLocalDateTimeString() {
        return DateTimeFormatter.ofPattern(Pattern.YYYY_MM_DD_HH_MM_SS).format(LocalDateTime.now());
    }

    public static LocalDateTime timestampToLocalDateTime(Integer timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
    }

    public static int localDateTimeToTimestamp(LocalDateTime dateTime) {
        return (int) dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    public static String timestampToLocalDateTimeStringWithHeaderZone(Integer timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochSecond(timestamp),
                getZoneId()
        );
        return DateTimeFormatter.ofPattern(Pattern.YYYY_MM_DD_HH_MM_SS).format(dateTime);
    }

    public static int localDateTimeStringToTimestampWithHeaderZone(String localDateTimeString) {
        // 解析时间字符串
        LocalDateTime dateTime = LocalDateTime.parse(localDateTimeString, DateTimeFormatter.ofPattern(Pattern.YYYY_MM_DD_HH_MM_SS));
        // 转换为时间戳（秒）
        return (int) dateTime.atZone(getZoneId()).toEpochSecond();
    }

    private static ZoneId getZoneId() {
        // 获取当前请求
        try {
            HttpServletRequest request = ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest();
            String zone = request.getParameter("timezone");
            return ZoneId.of(zone);
        } catch (Exception ignored) {

        }
        return ZoneId.systemDefault();
    }

    /**
     * 支持多语言的相对时间描述
     * @param map 映射关系
     * @param seconds 秒数
     * @return 相对时间描述
     */
    public static String getRelativeTime(Map<String, String> map, Integer seconds) {

        Integer relativeSeconds = currentTimestamp() - seconds;

        if (relativeSeconds < WEEK) {
            return timestampToLocalDateTimeStringWithHeaderZone(seconds);
        }

        if (relativeSeconds < MONTH) {
            long weeks = relativeSeconds / WEEK;
            return String.format(map.get("%d周前"), weeks);
        }

        if (relativeSeconds < YEAR) {
            long months = relativeSeconds / MONTH;
            return String.format(map.get("%d月前"), months);
        }

        Integer years = relativeSeconds / YEAR;
        return String.format(map.get("%d年前"), years);
    }

}
