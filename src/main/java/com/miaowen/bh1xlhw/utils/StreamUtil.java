package com.miaowen.bh1xlhw.utils;


import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 对stream api的封装
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-5-13
 */
public class StreamUtil {

    private static <T, K, V, C extends Collection<V>> Map<K, C> groupingBy(
        List<T> dataList,
        Function<? super T, ? extends K> keyMapper,
        Function<? super T, ? extends V> valueMapper,
        Supplier<C> supplier) {

        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyMap();
        }

        if (Objects.isNull(valueMapper)) {
            valueMapper = t -> (V) t;
        }

        return dataList.stream().collect(Collectors.groupingBy(keyMapper, Collectors.mapping(valueMapper, Collectors.toCollection(supplier))));
    }

    /**
     * 按指定key分组为list
     *
     * @param dataList  待分组列表
     * @param keyMapper key映射
     * @param <T>       列表泛型
     * @param <K>       key泛型
     * @return Map<K, List < T>>
     */
    public static <T, K> Map<K, List<T>> groupingToList(List<T> dataList, Function<? super T, ? extends K> keyMapper) {
        return groupingBy(dataList, keyMapper, null, ArrayList::new);
    }


    /**
     * 按指定key分组为list
     *
     * @param dataList    待分组列表
     * @param keyMapper   key映射
     * @param valueMapper value映射
     * @param <T>         列表泛型
     * @param <K>         key泛型
     * @param <V>         分组后的value泛型
     * @return Map<K, List < T>>
     */
    public static <T, K, V> Map<K, List<V>> groupingToList(List<T> dataList, Function<? super T, ? extends K> keyMapper
        , Function<? super T, ? extends V> valueMapper) {
        return groupingBy(dataList, keyMapper, valueMapper, ArrayList::new);
    }

    /**
     * 按指定key分组为set
     */
    public static <T, K, V> Map<K, Set<V>> groupingToSet(List<T> dataList, Function<? super T, ? extends K> keyMapper
        , Function<? super T, ? extends V> valueMapper) {
        return groupingBy(dataList, keyMapper, valueMapper, HashSet::new);
    }

    public static <T, K> Map<K, Set<T>> groupingToSet(List<T> dataList, Function<? super T, ? extends K> keyMapper) {
        return groupingBy(dataList, keyMapper, null, HashSet::new);
    }

    private static <T, R, C extends Collection<T>, Result extends Collection<R>> Result
    fetchCollection(C data, Function<? super T, ? extends R> fieldMapper, Supplier<Result> supplier) {
        if (CollectionUtils.isEmpty(data)) {
            return supplier.get();
        }
        return data.stream().map(fieldMapper).collect(Collectors.toCollection(supplier));
    }


    /**
     * 将dataList映射为自定义的list
     */
    public static <T, R, C extends Collection<T>> List<R> fetchList(C data, Function<? super T, ? extends R> fieldMapper) {
        return fetchCollection(data, fieldMapper, ArrayList::new);
    }

    public static <T, R, C extends Collection<T>> Set<R> fetchSet(C data, Function<? super T, ? extends R> fieldMapper) {
        return fetchCollection(data, fieldMapper, HashSet::new);
    }

    /**
     * list to map
     */
    public static <T, K, V, C extends Collection<T>> Map<K, V> map(C data, Function<? super T, ? extends K> keyMapper
        , Function<? super T, ? extends V> valueMapper) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyMap();
        }

        if (Objects.isNull(valueMapper)) {
            valueMapper = t -> (V) t;
        }

        return data.stream().collect(Collectors.toMap(keyMapper, valueMapper, (oldData, newData) -> newData));
    }


    public static <T, K, C extends Collection<T>> Map<K, T> map(C data, Function<? super T, ? extends K> keyMapper) {
        return map(data, keyMapper, null);
    }


    /**
     * 将数据列表按照两个键进行分组，返回 Map<K, Map<K, V>> 结构
     *
     * @param dataList    数据列表
     * @param keyMapper1  第一个键的映射函数
     * @param keyMapper2  第二个键的映射函数
     * @param valueMapper 值的映射函数
     * @param <T>         数据类型
     * @param <K1>        第一个键的类型
     * @param <K2>        第二个键的类型
     * @param <V>         值的类型
     * @return 分组后的 Map<K1, Map<K2, V>>
     */
    public static <T, K1, K2, V> Map<K1, Map<K2, V>> groupingToNestedMap(
        List<T> dataList,
        Function<? super T, ? extends K1> keyMapper1,
        Function<? super T, ? extends K2> keyMapper2,
        Function<? super T, ? extends V> valueMapper) {

        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyMap();
        }

        return dataList.stream()
            .collect(Collectors.groupingBy(
                // 第一层分组
                keyMapper1,
                Collectors.groupingBy(
                    // 第二层分组
                    keyMapper2,
                    // 取第一个值
                    Collectors.mapping(valueMapper, Collectors.collectingAndThen(Collectors.toList(), list -> list.get(0)))
                )));
    }

    /**
     * 示例：将数据列表按照两个键分组，返回 Map<K1, Map<K2, T>>
     *
     * @param dataList   数据列表
     * @param keyMapper1 第一个键的映射函数
     * @param keyMapper2 第二个键的映射函数
     * @param <T>        数据类型
     * @param <K1>       第一个键的类型
     * @param <K2>       第二个键的类型
     * @return 分组后的 Map<K1, Map<K2, T>>
     */
    public static <T, K1, K2> Map<K1, Map<K2, T>> groupingToNestedMap(
        List<T> dataList,
        Function<? super T, ? extends K1> keyMapper1,
        Function<? super T, ? extends K2> keyMapper2) {

        return groupingToNestedMap(dataList, keyMapper1, keyMapper2, Function.identity());
    }

    /**
     * 示例：将数据根据条件过滤获取第一个值
     *
     * @param dataList   数据列表
     * @param fieldMapper 过滤条件
     * @param <T>        数据类型
     * @return 过滤后的第一个值
     */
    public static <T, R extends Collection<T>> T flitterFirst(R dataList, Predicate<? super T> fieldMapper) {
        if (CollectionUtils.isEmpty(dataList)){
            return null;
        }
       return dataList.stream().filter(fieldMapper).findFirst().orElse(null);
    }
}
