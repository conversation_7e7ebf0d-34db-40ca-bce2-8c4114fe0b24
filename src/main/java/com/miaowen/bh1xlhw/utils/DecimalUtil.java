package com.miaowen.bh1xlhw.utils;

import com.miaowen.bh1xlhw.config.exception.BizException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * DecimalUtil :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
public class DecimalUtil {
    /**
     * 默认保留小数
     */
    private static final Integer DEFAULT_SCALE = 2;
    private static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);

    /**
     * 将扩大100倍的数字转换成原始金额
     * @param scaledValue 扩大后的值
     * @return 保留两位小数的BigDecimal
     */
    public static BigDecimal fromStoredValue(Integer scaledValue){
        return BigDecimal.valueOf(scaledValue).divide(ONE_HUNDRED, DEFAULT_SCALE, RoundingMode.HALF_UP);
    }

    /**
     * 将金额转换为扩大100倍的整数值
     * @param amount 金额
     * @return 扩大100倍后的值
     */
    public static int toStoredValue(BigDecimal amount) {
        return amount.multiply(ONE_HUNDRED)
            .setScale(0, RoundingMode.HALF_UP)
            .intValueExact();
    }

    /**
     * 重载方法：字符串输入转换
     */
    public static int toStoredValue(String amountStr) {
        BigDecimal amount = new BigDecimal(amountStr);
        return toStoredValue(amount);
    }


    /**
     * 计算付费率
     */
    public static BigDecimal getRate(Integer num, Integer sum) {
        if (Objects.isNull(sum) || Objects.isNull(num) || sum <= 0 || num <= 0) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(num).divide(new BigDecimal(sum), DEFAULT_SCALE, RoundingMode.HALF_UP);

    }

    /**
     * 检查金额是否符合精度要求，不符合则抛出异常
     *
     * @param amount          要检查的金额（BigDecimal）
     * @param degreeAccuracy  精度要求：-2（0.01精度）、0（整数精度）、2（100精度）
     * @throws IllegalArgumentException 如果金额不符合精度要求
     */
    public static void validateAmountPrecision(BigDecimal amount, int degreeAccuracy) {
        if (amount == null) {
            throw new BizException("金额不能为null");
        }

        if (degreeAccuracy < 0) {
            // 检查小数精度
            int requiredScale = -degreeAccuracy;
            if (amount.scale() > requiredScale) {
                BigDecimal rounded = amount.setScale(requiredScale, RoundingMode.DOWN);
                if (rounded.compareTo(amount) != 0) {
                    throw new BizException(String.format("金额 %s 超过%d位小数精度", amount, requiredScale));
                }
            }
        } else if (degreeAccuracy > 0) {
            // 检查整数倍精度
            BigDecimal divisor = BigDecimal.TEN.pow(degreeAccuracy);
            if (amount.remainder(divisor).compareTo(BigDecimal.ZERO) != 0) {
                throw new BizException(String.format("金额 %s 超过%d位小数精度", amount, divisor.intValue()));
            }
        } else {
            // 检查是否为整数
            if (amount.scale() > 0 && amount.stripTrailingZeros().scale() > 0) {
                throw new BizException(String.format("金额 %s 不是整数", amount));
            }
        }
    }

}
