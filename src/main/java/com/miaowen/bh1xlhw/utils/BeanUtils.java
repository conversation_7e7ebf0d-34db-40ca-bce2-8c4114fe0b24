package com.miaowen.bh1xlhw.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.miaowen.bh1xlhw.config.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * BeanUtils :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-10
 */
@Slf4j
public class BeanUtils {
    public final static CopyOptions COPY_OPTIONS = CopyOptions.create()
        .setIgnoreError(true)
        .setIgnoreNullValue(true);

    public static <T> T copy(Object source, Class<T> target) {
        try {
            T t = target.newInstance();
            if (Objects.isNull(source)) {
                return t;
            }

            BeanUtil.copyProperties(source, t, COPY_OPTIONS);
            return t;
        } catch (Exception e) {
            throw new BizException("copy properties error");
        }
    }

    public static <T> List<T> copyList(List<?> list, Class<T> aClass) {
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(item -> BeanUtil.copyProperties(item, aClass)).collect(Collectors.toList());
    }

}
