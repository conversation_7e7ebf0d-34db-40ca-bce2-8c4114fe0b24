package com.miaowen.bh1xlhw.utils;

import lombok.Data;

/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class PasswordUtil {

    public static int passwordToRank(String password) {
        int score = 0;
        if (password.length() <= 6) {
            return score;
        }
        if (password.length() > 12) {
            score += 2;
        } else {
            score += 1;
        }
        if (password.matches(".*[0-9]+.*")) {
            score += 1;
        }
        if (password.matches(".*[a-z]+.*")) {
            score += 1;
        }
        if (password.matches(".*[A-Z]+.*")) {
            score += 1;
        }
        if (password.matches(".*[A-Z].*") && password.matches(".*[a-z].*")) {
            score += 1;
        }
        if (password.matches(".*\\W+.*")) {
            score += 1;
        }
        if (password.matches(".*[^a-zA-Z0-9].*")) {
            score += 2;
        }
        return score;
    }

    public static String rankToString(Integer passwordRank) {
        if (passwordRank == 0) {
            return "极弱";
        } else if (passwordRank >= 1 && passwordRank <= 3) {
            return "弱";
        } else if (passwordRank >= 4 && passwordRank <= 6) {
            return "中";
        } else if (passwordRank >= 7 && passwordRank <= 10) {
            return "强";
        } else {
            return "极强";
        }
    }

    public static int calculatePasswordStrength(String password) {
        int strength = 0;
        if (password.matches(".*[A-Z].*")) strength++;
        if (password.matches(".*[a-z].*")) strength++;
        if (password.matches(".*\\d.*")) strength++;
        if (password.matches(".*[!@#$%^&*()].*")) strength++;
        return strength;
    }
}
