package com.miaowen.bh1xlhw.utils;

import cn.hutool.core.lang.Snowflake;

/**
 * @Description TODO
 * @company 武汉秒闻网络科技有限公司
 * <AUTHOR>
 */
public class IdUtil {


    private static final Snowflake SNOWFLAKE = cn.hutool.core.util.IdUtil.getSnowflake(1, 1);

    public static String generateOutTradeNo() {
        return "E" + SNOWFLAKE.nextId();
    }

    public static String generateMbtiOutTradeNo() {
        return "M" + SNOWFLAKE.nextId();
    }


    public static String generateOutTradePayNo(String outTradeNo) {
        return outTradeNo + generateRandomString(5);
    }

    /**
     * 生成自定义格式的LogId
     * 格式：L6位随机数时间戳毫秒
     * 例如：L1932611749612189366
     *
     * @return 自定义格式的LogId
     */
    public static String generateCustomLogId() {
        // 生成6位随机数（100000-999999）
        int randomNum = (int) (Math.random() * 900000) + 100000;
        // 获取当前时间戳毫秒
        long timestamp = System.currentTimeMillis();
        return "L" + randomNum + timestamp ;
    }

    /**
     * 生成随机字符串（大小写字母+数字）
     *
     * @param length 字符串长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        java.util.Random random = new java.util.Random();

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }

        return sb.toString();
    }

    /**
     * 生成4-8位随机字符串（大小写字母+数字）
     *
     * @return 4-8位随机字符串
     */
    public static String generateRandomString() {
        // 随机生成4-8位长度
        // 4-8位
        int length = (int) (Math.random() * 5) + 4;
        return generateRandomString(length);
    }
    public static String generateRefundTradeNo() {
        return "RE" + String.valueOf(SNOWFLAKE.nextId());
    }


    public static String generateMtgId() {
        return "ml" + cn.hutool.core.util.IdUtil.fastUUID().substring(0, 4);
    }


    public static String generateTtgId() {
        return "tc" + cn.hutool.core.util.IdUtil.fastUUID().substring(0, 4);
    }

}
