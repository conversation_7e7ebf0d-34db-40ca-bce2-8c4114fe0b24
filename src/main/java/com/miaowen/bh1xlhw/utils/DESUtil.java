package com.miaowen.bh1xlhw.utils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.util.Base64;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public class DESUtil {
    
    private static final String DES_ALGORITHM = "DES";
    private static final String CHARSET = "UTF-8";

    /**
     * DES加密
     * @param plaintext 明文
     * @param secretKey 密钥（必须8位）
     * @return Base64编码的密文
     */
    public static String encrypt(String plaintext, String secretKey) throws Exception {
        
        DESKeySpec desKeySpec = new DESKeySpec(secretKey.getBytes(CHARSET));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES_ALGORITHM);
        SecretKey key = keyFactory.generateSecret(desKeySpec);
        
        Cipher cipher = Cipher.getInstance(DES_ALGORITHM);
        cipher.init(Cipher.ENCRYPT_MODE, key);
        
        byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(CHARSET));
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    /**
     * DES解密
     * @param ciphertext Base64编码的密文
     * @param secretKey 密钥（必须8位）
     * @return 明文
     */
    public static String decrypt(String ciphertext, String secretKey) throws Exception {
        
        DESKeySpec desKeySpec = new DESKeySpec(secretKey.getBytes(CHARSET));
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(DES_ALGORITHM);
        SecretKey key = keyFactory.generateSecret(desKeySpec);
        
        Cipher cipher = Cipher.getInstance(DES_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, key);
        
        byte[] decodedBytes = Base64.getDecoder().decode(ciphertext);
        byte[] decryptedBytes = cipher.doFinal(decodedBytes);
        return new String(decryptedBytes, CHARSET);
    }

    // 测试示例
    public static void main(String[] args) throws Exception {
        // 8位密钥
        String key = "12345678";
        String text = "Hello, DES加密测试!";

        String encrypted = encrypt(text, key);
        System.out.println("加密后: " + encrypted);
//
        String decrypted = decrypt("65i94k3o2odSbA2glv4SWgeJjd+tYSoY3LomNidhPAHiOGwI9IP1Dlspwa9MjwMM", key);
        System.out.println("解密后: " + decrypted);
    }
}
