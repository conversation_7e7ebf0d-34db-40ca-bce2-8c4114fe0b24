package com.miaowen.bh1xlhw.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName JsonUtil
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/4/2 9:35
 */
@Slf4j
public class JsonUtil {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    public static Map<String, Object> getMap(String json) {
        if (!StringUtils.hasText(json)) {
            return new HashMap<>();
        }
        Map<String, Object> res = new HashMap<String, Object>();
        try {
            res = OBJECT_MAPPER.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (Exception e) {
            doLog("json解析失败"+json);
        }
        return res;
    }

    public static List<Map<String, Object>> getListMap(String json) {
        if (!StringUtils.hasText(json)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> res = new ArrayList<>();
        try {
            res = OBJECT_MAPPER.readValue(json, OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, Map.class));
        } catch (Exception e) {
            doLog("json解析失败"+json);
        }

        return res;
    }

    public static List<Integer> getListInteger(String json) {
        if (!StringUtils.hasText(json)) {
            return new ArrayList<>();
        }
        List<Integer> res = new ArrayList<>();
        try {
            res = OBJECT_MAPPER.readValue(json, OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, Integer.class));
        } catch (Exception e) {
            doLog("json解析失败"+json);
        }

        return res;
    }

    public static <T> List<T> getList(String json, Class<T> clazz) {
        if (!StringUtils.hasText(json)) {
            return new ArrayList<>();
        }
        List<T> res = new ArrayList<>();
        try {
            res = OBJECT_MAPPER.readValue(json, OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (Exception e) {
            doLog("json解析失败"+json);
        }

        return res;
    }

    public static String getString(Object o) {
        String res = "";
        try {
            res = OBJECT_MAPPER.writeValueAsString(o);
        } catch (Exception e) {
            doLog("json解析失败"+o);
        }
        return res;
    }

    public static <T> T getBean(String json, Class<T> clazz) {
        T res = null;
        try {
            res = OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception e) {
            log.warn("json反序列化失败,json:{},clazz:{}",json,clazz.getName());
        }
        return res;
    }



    private static void doLog(String msg) {
        log.warn(msg);
    }

}
