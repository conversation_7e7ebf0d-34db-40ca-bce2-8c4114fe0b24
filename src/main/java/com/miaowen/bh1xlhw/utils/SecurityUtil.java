package com.miaowen.bh1xlhw.utils;

import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.function.Supplier;

/**
 * 
 * AdminUtil :
 * 
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-03-21
 */
public class SecurityUtil {

    /**
     * 获取当前认证用户
     * @return OAuthUser 当前用户对象
     * @throws IllegalStateException 如果用户未认证或类型不匹配
     */
    public static OAuthUser currentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        // 检查认证是否存在
        if (authentication == null) {
            throw new IllegalStateException("无法获取当前用户：SecurityContext中不存在Authentication");
        }

        // 检查Details是否为OAuthUser类型
        Object details = authentication.getDetails();
        if (details instanceof OAuthUser) {
            return (OAuthUser) details;
        } else {
            OAuthUser oAuthUser = new OAuthUser();
            oAuthUser.setUserId(0);
            oAuthUser.setUsername("未登录用户");
            return oAuthUser;
        }
    }

    /**
     * 清理当前用户上下文（线程安全）
     */
    public static void clearCurrentUser() {
        SecurityContextHolder.clearContext();
    }

    /**
     * 在指定代码块内临时切换用户上下文
     * @param user 临时用户
     * @param action 要执行的代码
     */
    public static <T> T runAs(OAuthUser user, Supplier<T> action) {
        Authentication originalAuth = SecurityContextHolder.getContext().getAuthentication();
        try {
            // 创建临时认证
            UsernamePasswordAuthenticationToken tempAuth = new UsernamePasswordAuthenticationToken(user, null, user.getAuthorities());
            tempAuth.setDetails(user);

            // 设置临时上下文
            SecurityContextHolder.getContext().setAuthentication(tempAuth);

            // 执行代码
            return action.get();
        } finally {
            // 恢复原始上下文
            SecurityContextHolder.getContext().setAuthentication(originalAuth);
        }
    }

}
