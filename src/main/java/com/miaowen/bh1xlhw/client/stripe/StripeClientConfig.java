package com.miaowen.bh1xlhw.client.stripe;



import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.model.bo.payment.StripeConfigBO;
import com.miaowen.bh1xlhw.repository.IMerchantService;
import com.stripe.net.BearerTokenAuthenticator;
import com.stripe.net.RequestOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * StripeClientConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-29
 */

@Slf4j
@Component
public class StripeClientConfig {
    private final IMerchantService merchantService;

    public StripeClientConfig(IMerchantService merchantService) {
        this.merchantService = merchantService;
    }

    /**
     * 获取Stripe请求配置
     */
    public RequestOptions getRequestOptions(Integer clientId) {
        try {
            StripeConfigBO newConfig = merchantService.getStripeConfigById(clientId);
            return createRequestOptions(newConfig);
        } catch (Exception e) {
            log.error("获取Stripe配置异常, clientId: {}", clientId, e);
            throw new BizException("can not get request options");
        }
    }


    /**
     * 创建新的请求配置
     */
    private RequestOptions createRequestOptions(StripeConfigBO config) {
        return RequestOptions.builder()
            .setAuthenticator(new BearerTokenAuthenticator(config.getPrivateKey()))
            .build();
    }

}

