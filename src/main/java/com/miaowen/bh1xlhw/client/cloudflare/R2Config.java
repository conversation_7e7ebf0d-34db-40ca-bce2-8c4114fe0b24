package com.miaowen.bh1xlhw.client.cloudflare;


import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.miaowen.bh1xlhw.properties.AwsR2ConfigProperties;
import com.miaowen.bh1xlhw.service.oss.impl.R2Storage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@Configuration
@AllArgsConstructor
@EnableConfigurationProperties(AwsR2ConfigProperties.class)
public class R2Config {
    private final AwsR2ConfigProperties awsR2ConfigProperties;

    @Bean
    @ConditionalOnProperty("cloudflare.r2.access-key")
    public AmazonS3 amazonS3() {
        log.info("初始化 amazonS3 bean");
        AwsClientBuilder.EndpointConfiguration endpointConfig = new AwsClientBuilder.EndpointConfiguration(awsR2ConfigProperties.getEndpoint(), awsR2ConfigProperties.getRegion());
        AWSStaticCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(new BasicAWSCredentials(awsR2ConfigProperties.getAccessKey(), awsR2ConfigProperties.getSecretKey()));
        // R2 必须启用路径风格
        return AmazonS3ClientBuilder
            .standard()
            .withEndpointConfiguration(endpointConfig)
            .withCredentials(credentialsProvider)
            .withPathStyleAccessEnabled(true)
            .build();
    }

    @Bean
    @ConditionalOnBean(AmazonS3.class)
    public R2Storage r2Storage() {
       return new R2Storage(amazonS3(), awsR2ConfigProperties);
    }
}
