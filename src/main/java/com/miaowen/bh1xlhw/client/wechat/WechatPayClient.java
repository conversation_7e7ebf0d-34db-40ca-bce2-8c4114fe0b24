package com.miaowen.bh1xlhw.client.wechat;

import com.miaowen.bh1xlhw.model.bo.payment.WechatConfigBO;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import lombok.Data;

/**
 * WechatPayClient :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-19
 */
@Data
public class WechatPayClient {
    private RSAAutoCertificateConfig rsaAutoCertificateConfig;
    private WechatConfigBO wechatConfig;

    public WechatPayClient(WechatConfigBO wechatConfig) {
        this.wechatConfig = wechatConfig;
        this.rsaAutoCertificateConfig = new RSAAutoCertificateConfig.Builder()
            .merchantId(wechatConfig.getWechatMchId())
            .privateKeyFromPath(wechatConfig.getWechatApiClientKey())
            .merchantSerialNumber(wechatConfig.getWechatCertSn())
            .apiV3Key(wechatConfig.getWechatV3MchKey())
            .build();
    }
}
