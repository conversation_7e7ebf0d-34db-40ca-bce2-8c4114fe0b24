package com.miaowen.bh1xlhw.client.wechat;


import com.miaowen.bh1xlhw.model.bo.payment.WechatConfigBO;
import com.miaowen.bh1xlhw.repository.IMerchantService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WechatPayClientConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-19
 */

@Slf4j
@Component
@AllArgsConstructor
public class WechatPayClientConfig {
    private final Map<Integer, WechatPayClient> clientMap = new ConcurrentHashMap<>();
    // 配置缓存
    private final IMerchantService merchantService;

    /**
     * 获取或创建PayPal客户端
     */
    public WechatPayClient getClient(Integer clientId) {
        return clientMap.compute(clientId, (k, existingClient) -> {
            // 一次性获取配置，避免多次查询
            WechatConfigBO newConfig = merchantService.getWechatPayConfig(clientId);
            if (Objects.isNull(newConfig)) {
                log.error("未找到wechatPay配置，RSAAutoCertificateConfig: {}", clientId);
                clientMap.remove(clientId);
                return null;
            }
            WechatPayClient wechatPayClient = clientMap.get(clientId);


            // 检查是否需要重用现有客户端
            if (existingClient != null && isClientValid(wechatPayClient, newConfig)) {
                return existingClient;
            }

            // 创建新客户端
            return createRSAAutoCertificateConfig(newConfig);
        });

    }


    /**
     * 检查现有客户端是否有效
     */
    private boolean isClientValid(WechatPayClient client, WechatConfigBO config) {
        if (Objects.isNull(client)){
            return false;
        }
        return isCredentialsMatch(client.getWechatConfig(), config);
    }


    /**
     * 数据库更新需要
     * 检查凭证是否匹配
     */
    private boolean isCredentialsMatch(WechatConfigBO oldConfig, WechatConfigBO newConfig) {
        return oldConfig.getWechatAppid().equals(newConfig.getWechatAppid())
            && oldConfig.getWechatAppSecret().equals(newConfig.getWechatAppSecret())
            && oldConfig.getWechatMchId().equals(newConfig.getWechatMchId())
            && oldConfig.getWechatMchKey().equals(newConfig.getWechatMchKey())
            && oldConfig.getWechatV3MchKey().equals(newConfig.getWechatV3MchKey())
            && oldConfig.getWechatCertSn().equals(newConfig.getWechatCertSn())
            && oldConfig.getWechatApiClientCert().equals(newConfig.getWechatApiClientCert())
            && oldConfig.getWechatApiClientKey().equals(newConfig.getWechatApiClientKey())
            && oldConfig.getWechatPlatformKey().equals(newConfig.getWechatPlatformKey());
    }

    /**
     * 创建PayPal客户端
     */
    private WechatPayClient createRSAAutoCertificateConfig(WechatConfigBO wechatPayConfig) {
        return new WechatPayClient(wechatPayConfig);
    }

}
