package com.miaowen.bh1xlhw.client.paypal;

import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.bo.payment.PaypalConfigBO;
import com.miaowen.bh1xlhw.repository.IMerchantService;
import com.paypal.sdk.ClientCredentialsAuth;
import com.paypal.sdk.Environment;
import com.paypal.sdk.PaypalServerSdkClient;
import com.paypal.sdk.authentication.ClientCredentialsAuthModel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PaypalClientConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-29
 */
@Slf4j
@Component
@AllArgsConstructor
public class PaypalClientConfig {
    private final Map<Integer, PaypalServerSdkClient> clientMap = new ConcurrentHashMap<>();
    private final IMerchantService merchantService;

    /**
     * 获取或创建PayPal客户端
     */
    public PaypalServerSdkClient getClient(Integer clientId) {
        return clientMap.compute(clientId, (k, existingClient) -> {
            // 一次性获取配置，避免多次查询
            PaypalConfigBO config = merchantService.getPaypalConfigById(clientId);
            if (Objects.isNull(config)) {
                log.error("未找到PayPal配置，clientId: {}", clientId);
                clientMap.remove(clientId);
                return null;
            }

            // 检查是否需要重用现有客户端
            if (existingClient != null && isClientValid(existingClient, config)) {
                return existingClient;
            }

            // 创建新客户端
            return createPaypalClient(config);
        });
    }

    /**
     * 检查现有客户端是否有效
     */
    private boolean isClientValid(PaypalServerSdkClient client, PaypalConfigBO config) {
        return isEnvironmentMatch(client, config) && isCredentialsMatch(client, config);
    }

    /**
     * 数据库更新需要
     * 检查环境模式是否匹配
     */
    private boolean isEnvironmentMatch(PaypalServerSdkClient client, PaypalConfigBO config) {
        Environment expectedEnv = BooleanEnum.FALSE.getValue().equals(config.getSandboxMode())
            ? Environment.PRODUCTION
            : Environment.SANDBOX;
        return expectedEnv.equals(client.getEnvironment());
    }

    /**
     * 数据库更新需要
     * 检查凭证是否匹配
     */
    private boolean isCredentialsMatch(PaypalServerSdkClient client, PaypalConfigBO config) {
        ClientCredentialsAuth auth = client.getClientCredentialsAuth();
        return auth.getOAuthClientId().equals(config.getClientId())
            && auth.getOAuthClientSecret().equals(config.getPrivateKey());
    }

    /**
     * 创建PayPal客户端
     */
    private PaypalServerSdkClient createPaypalClient(PaypalConfigBO config) {
        Environment environment = BooleanEnum.TRUE.getValue().equals(config.getSandboxMode())
            ? Environment.SANDBOX
            : Environment.PRODUCTION;

        return new PaypalServerSdkClient.Builder()
            .httpClientConfig(configBuilder -> configBuilder.timeout(0))
            .clientCredentialsAuth(new ClientCredentialsAuthModel.Builder(
                config.getClientId(),
                config.getPrivateKey()
            ).build())
            .environment(environment)
            .build();
    }

}
