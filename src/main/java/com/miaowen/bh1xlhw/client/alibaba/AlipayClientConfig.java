package com.miaowen.bh1xlhw.client.alibaba;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayConfig;
import com.alipay.api.DefaultAlipayClient;

import com.alipay.api.internal.util.AlipaySignature;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.model.bo.payment.AlipayConfigBO;
import com.miaowen.bh1xlhw.repository.IMerchantService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * AlipayClientConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-03
 */
@Slf4j
@Component
@AllArgsConstructor
public class AlipayClientConfig {
    private final Map<Integer, AlipayClient> clientMap = new ConcurrentHashMap<>();
    // 配置缓存
    private final Map<Integer, AlipayConfigBO> configCache = new ConcurrentHashMap<>();

    //缓存公钥避免多次读取文件
    private final Map<Integer, String> publicKeyCache = new ConcurrentHashMap<>();

    private final IMerchantService merchantService;

    /**
     * 获取或创建PayPal客户端
     */
    public AlipayClient getClient(Integer clientId) {
        return clientMap.compute(clientId, (k, existingClient) -> {
            // 一次性获取配置，避免多次查询
            AlipayConfigBO newConfig = merchantService.getAliPayConfig(clientId);
            if (Objects.isNull(newConfig)) {
                log.error("未找到alipay配置，clientId: {}", clientId);
                clientMap.remove(clientId);
                publicKeyCache.remove(clientId);
                configCache.remove(clientId);
                return null;
            }
            AlipayConfigBO oldConfig = configCache.put(clientId, newConfig);
            try {
                publicKeyCache.put(clientId, AlipaySignature.getAlipayPublicKey(newConfig.getAlipayPublicCertPath()));
            } catch (AlipayApiException e) {
                log.error("alipay初始化配置读取公钥失败,clientId:{}",clientId);
                throw new BizException("can not create alipay client");
            }
            // 检查是否需要重用现有客户端
            if (existingClient != null && isClientValid(oldConfig, newConfig)) {
                return existingClient;
            }

            // 创建新客户端
            try {
                return createPaypalClient(newConfig);
            } catch (AlipayApiException e) {
                log.error("获取alipay配置异常, clientId: {}", clientId, e);
                throw new BizException("can not create alipay client");
            }
        });

    }

    public String getPublicKey(Integer clientId){
        return publicKeyCache.get(clientId);
    }

    /**
     * 检查现有客户端是否有效
     */
    private boolean isClientValid(AlipayConfigBO client, AlipayConfigBO config) {
        return isCredentialsMatch(client, config);
    }


    /**
     * 数据库更新需要
     * 检查凭证是否匹配
     */
    private boolean isCredentialsMatch(AlipayConfigBO oldConfig, AlipayConfigBO newConfig) {
        return oldConfig.getAppId().equals(newConfig.getAppId())
            && oldConfig.getAppPrivatePath().equals(newConfig.getAppPrivatePath())
            && oldConfig.getAppPublicCertPath().equals(newConfig.getAppPublicCertPath())
            && oldConfig.getAlipayPublicCertPath().equals(newConfig.getAlipayPublicCertPath())
            && oldConfig.getRootCertPath().equals(newConfig.getRootCertPath())
            && oldConfig.getAliAesKey().equals(newConfig.getAliAesKey());
    }

    /**
     * 创建PayPal客户端
     */
    private AlipayClient createPaypalClient(AlipayConfigBO config) throws AlipayApiException {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setAppId(config.getAppId());
        try {
            String privateKey = new String(Files.readAllBytes(Paths.get(config.getAppPrivatePath())));
            alipayConfig.setPrivateKey(privateKey);
        } catch (IOException e) {
            log.error("alipay读取私钥失败", e);
        }
        alipayConfig.setAppCertPath(config.getAppPublicCertPath());
        alipayConfig.setAlipayPublicCertPath(config.getAlipayPublicCertPath());
        alipayConfig.setRootCertPath(config.getRootCertPath());
        alipayConfig.setMaxIdleConnections(5);
        return new DefaultAlipayClient(alipayConfig);
    }
}
