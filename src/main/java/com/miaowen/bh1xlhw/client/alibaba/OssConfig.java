package com.miaowen.bh1xlhw.client.alibaba;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.miaowen.bh1xlhw.properties.AlibabaOssProperties;
import com.miaowen.bh1xlhw.service.oss.impl.OssFileStorage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@Configuration
@AllArgsConstructor
public class OssConfig {
    private final AlibabaOssProperties alibabaOssProperties;

    @Bean
    @ConditionalOnProperty("oss.access-key-id")
    public OSS ossClient() {
        log.info("初始化 oss bean");
        return new OSSClientBuilder().build(alibabaOssProperties.getEndpoint(), alibabaOssProperties.getAccessKeyId(), alibabaOssProperties.getAccessKeySecret());
    }

    @Bean
    @ConditionalOnBean(OSS.class)
    public OssFileStorage ossFileStorage() {
       return new OssFileStorage(alibabaOssProperties, ossClient());
    }


}
