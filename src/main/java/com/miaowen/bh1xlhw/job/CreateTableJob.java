package com.miaowen.bh1xlhw.job;

import com.baomidou.dynamic.datasource.DynamicRoutingDataSource;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.config.mybatisPlus.ShardingTableContent;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.sql.DataSource;
import java.time.YearMonth;

import static com.miaowen.bh1xlhw.utils.DateUtils.MONTH_DATE_FORMAT;
import static com.miaowen.bh1xlhw.utils.DateUtils.MONTH_FORMAT;


/**
 * CreateTableJob : 分表任务
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-11
 */
@Slf4j
@Component
@AllArgsConstructor
public class CreateTableJob {
    private final DynamicRoutingDataSource dynamicRoutingDataSource;

    /**
     * 每月1号创建下个月的表
     */
    @XxlJob("createTableJob")
    public void createTableJob() {
        YearMonth yearMonth = YearMonth.now().plusMonths(1);
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.hasText(jobParam)){
            try {
                yearMonth = YearMonth.parse(jobParam, MONTH_FORMAT);
            } catch (Exception e) {
                log.error("xxlJob 参数解析失败 task:workMonthStatistics,jobParam:{}",jobParam,e);
            }
        }
        YearMonth finalYearMonth = yearMonth;

        log.info("开始创建分表，目标月份: {}", finalYearMonth.format(MONTH_DATE_FORMAT));

        ShardingTableContent.TABLE_CREATE.forEach((tableName, tableCreator) -> {
            // 获取当前月份后缀
            String actualTableName = tableName + "_" + finalYearMonth.format(MONTH_DATE_FORMAT);
            try {
                createNewTable(actualTableName, tableName);
                XxlJobHelper.log("成功创建表: " + actualTableName);
            } catch (Exception e) {
                XxlJobHelper.log("创建表失败: " + actualTableName + ", 错误: " + e.getMessage());
                log.error("创建表失败: {}", actualTableName, e);
            }
        });

        log.info("分表创建任务完成");
    }

    /**
     * 创建新表
     * 使用 log 数据源创建表
     */
    public void createNewTable(String actualTableName, String tableName) {
        try {
            log.info("开始创建表: {} (基于模板: {})", actualTableName, tableName);

            // 获取 log 数据源
            DataSource logDataSource = dynamicRoutingDataSource.getDataSource("log");
            if (logDataSource == null) {
                throw new BizException("无法获取 log 数据源");
            }

            // 使用 log 数据源创建 JdbcTemplate
            JdbcTemplate logJdbcTemplate = new JdbcTemplate(logDataSource);

            ShardingTableContent.TableCreator tableCreator = ShardingTableContent.TABLE_CREATE.get(tableName);
            String tableCreateSql = tableCreator.createTable(actualTableName);

            if (!StringUtils.hasText(tableCreateSql)) {
                // 使用LIKE复制表结构
                tableCreateSql = String.format("CREATE TABLE IF NOT EXISTS `%s` LIKE `%s`", actualTableName, tableName);
                log.info("使用 LIKE 语句创建表: {}", tableCreateSql);
            } else {
                log.info("使用自定义 SQL 创建表: {}", actualTableName);
            }

            // 使用 log 数据源的 JdbcTemplate 执行 SQL
            logJdbcTemplate.execute(tableCreateSql);
            log.info("表创建成功: {} (在 log 数据源中)", actualTableName);

        } catch (Exception e) {
            String errorMsg = String.format("创建分表失败 tableName: %s, actualTableName: %s", tableName, actualTableName);
            log.error(errorMsg, e);
            XxlJobHelper.log(errorMsg, e);
            throw new BizException("创建分表失败: " + actualTableName);
        }
    }


}
