package com.miaowen.bh1xlhw.job;

import com.miaowen.bh1xlhw.service.currency.PoCurrencyService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/23 8:51
 */
@Component
public class ExchangeRateJobController {

    @Resource
    PoCurrencyService poCurrencyService;

    @XxlJob("exchangeRateJob")
    public void exchangeRateJob(){
        poCurrencyService.updateExchangeRate();
        System.out.println("定时任务执行");
    }
}
