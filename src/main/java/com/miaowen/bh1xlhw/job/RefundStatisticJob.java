package com.miaowen.bh1xlhw.job;

import com.miaowen.bh1xlhw.service.order.RefundService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDate;

/**
 * RefundStatisticJob :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-28
 */
@Slf4j
@Component
@AllArgsConstructor
public class RefundStatisticJob {
    public final RefundService refundStatistic;

    @XxlJob("refundStatisticJob")
    public void refundStatisticJob() {
        //每天凌晨算出前一天的退款总数
        LocalDate lastMonthDay = LocalDate.now().minusMonths(1);
        String jobParam = XxlJobHelper.getJobParam();
        if (StringUtils.hasText(jobParam)){
            try {
                lastMonthDay = LocalDate.parse(jobParam, DateUtils.DATE_FORMAT);
            } catch (Exception e) {
                log.error("xxlJob 参数解析失败 task:workMonthStatistics,jobParam:{}",jobParam,e);
            }
        }

        try {
            refundStatistic.refundStatistic(lastMonthDay);
        } catch (Exception e) {
            log.error("统计退款总金额报错 error", e);
        }

    }

}
