package com.miaowen.bh1xlhw.service.email.impl;

import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import com.miaowen.bh1xlhw.repository.IEmailTemplateContentService;
import com.miaowen.bh1xlhw.service.email.EmailSendStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description 账单报告邮件发送策略
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/18 17:49
 */
@Component
public class GoogleEmailStrategy implements EmailSendStrategy {

    @Resource
    IEmailTemplateContentService iEmailTemplateContentService;
    
    @Override
    public void send(MailAccount account, String toEmail) {

//        EmailTemplateContent emailTemplateContent = iEmailTemplateContentService.getById(40);
//        MailUtil.send(account, toEmail, emailTemplateContent.getTitle(), emailTemplateContent.getContent(), true);
        MailUtil.send(account, toEmail, "未支付账单", "未支付账单", true);

    }

    @Override
    public Integer getType() {
        return 0;
    }
} 
