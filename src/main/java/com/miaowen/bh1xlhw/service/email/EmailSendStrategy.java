package com.miaowen.bh1xlhw.service.email;

import cn.hutool.extra.mail.MailAccount;

/**
 * @Description 邮件发送策略接口
 * <AUTHOR>
 * @Date 2025/3/18 17:49
 */
public interface EmailSendStrategy {
    /**
     * 发送邮件
     * @param account 邮件账户
     * @param toEmail 收件人邮箱
     */
    void send(MailAccount account, String toEmail);
    
    /**
     * 获取策略类型
     * @return 策略类型
     */
    Integer getType();
} 