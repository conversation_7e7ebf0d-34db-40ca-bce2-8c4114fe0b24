package com.miaowen.bh1xlhw.service.email.impl;

import cn.hutool.extra.mail.MailAccount;
import cn.hutool.extra.mail.MailUtil;
import com.miaowen.bh1xlhw.model.entity.Email;
import com.miaowen.bh1xlhw.service.email.EmailSendService;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import java.io.File;
import java.util.List;
import java.util.Properties;

@Service
public class GoogleEmailService implements EmailSendService {

    @Override
    public void send(MailAccount account, String toEmail, String content, File attachment) {
        MailUtil.send(account, toEmail, "业务通知邮件",  // 主题可配置化
                content, true,  // 是否为HTML
                attachment // 添加附件
        );
    }

    //--todo带实现
    @Override
    public void send(MailAccount account, String toEmail, String content, List<File> attachments) {

    }

    @Override
    public Integer getType() {
        return 0;  // 策略类型标识
    }

    @Override
    public boolean send(Email account, String toEmail, String subject, String content) {
       return send(account, toEmail, subject, content, null);
    }

    @Override
    public boolean send(Email account, String toEmail, String subject, String content, String unsubscribeUrl) {
        try {
            JavaMailSenderImpl mailSender = getMailAccount(account);
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            // 基础信息设置
            // 显示发件人名称
            helper.setFrom(new InternetAddress(account.getEmail(), account.getSendName()));
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(content, true);
            // 添加必须的邮件头（RFC 8058规范）
            if (!StringUtils.isEmpty(unsubscribeUrl)){
                message.addHeader("List-Unsubscribe", "<" + account.getEmail() + ">, <" + unsubscribeUrl + ">");
            }
            message.addHeader("List-Unsubscribe-Post", "List-Unsubscribe=One-Click");
            // 标识批量邮件
            message.addHeader("Precedence", "bulk");
            // 抑制自动回复
            message.addHeader("X-Auto-Response-Suppress", "All");
            mailSender.send(message);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private static JavaMailSenderImpl getMailAccount(Email fromEmail) {
        JavaMailSenderImpl sender = new JavaMailSenderImpl();
        sender.setHost(fromEmail.getSmtp());
        sender.setPort(fromEmail.getPort());
        sender.setUsername(fromEmail.getEmail());
        sender.setPassword(fromEmail.getPassword());
        sender.setProtocol("smtp");
        Properties properties = new Properties();
        properties.setProperty("mail.smtp.auth", "true");
//        properties.setProperty("mail.smtp.ssl.enable", "true");
//        properties.setProperty("mail.smtp.starttls.enable", "true");
        // 根据端口动态设置加密方式

        if (fromEmail.getPort() == 465) {
            properties.setProperty("mail.smtp.ssl.enable", "true");
            properties.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            properties.setProperty("mail.smtp.socketFactory.port", "465");
        } else if (fromEmail.getPort() == 587) {
            properties.setProperty("mail.smtp.starttls.enable", "true");
        }

        // Gmail特殊配置
        if (fromEmail.getSmtp().contains("gmail.com")) {
            properties.setProperty("mail.smtp.ssl.trust", "smtp.gmail.com");
            // 解决Gmail的TLS问题
            properties.setProperty("mail.smtp.ssl.protocols", "TLSv1.2");
        }

        sender.setJavaMailProperties(properties);
        return sender;
    }


}