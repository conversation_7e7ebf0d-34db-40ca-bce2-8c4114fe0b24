package com.miaowen.bh1xlhw.service.email.impl;

import cn.hutool.extra.mail.MailAccount;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.constant.enums.EmailTempletTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.EmailTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.bo.EmailParamBO;
import com.miaowen.bh1xlhw.model.bo.email.SendEmailBO;
import com.miaowen.bh1xlhw.model.bo.email.UnsubscribeEmailBO;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsInfoBo;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.entity.order.OrderEnvironmental;
import com.miaowen.bh1xlhw.model.entity.order.OrderRequestParam;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email.EmailForm;
import com.miaowen.bh1xlhw.model.query.email.EmailPageForm;
import com.miaowen.bh1xlhw.model.query.email.SendEmailForm;
import com.miaowen.bh1xlhw.model.query.email.UnsubscribeEmailForm;
import com.miaowen.bh1xlhw.model.vo.email.EmailVO;
import com.miaowen.bh1xlhw.model.vo.email.UnsubscribeEmailVO;
import com.miaowen.bh1xlhw.properties.SystemSecretKeyProperties;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email.EmailSendService;
import com.miaowen.bh1xlhw.service.email.EmailService;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.utils.AesCryptoUtil;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.ShardingUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.junit.platform.commons.util.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;
import static com.miaowen.bh1xlhw.constant.CommonConstant.ID;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Slf4j
@Service
@AllArgsConstructor
public class EmailServiceImpl implements EmailService {

    private final IEmailService emailService;
    private final EmailSendService emailSendService;
    private final DomainService domainService;
    private final GoogleEmailService googleEmailService;
    private final IEmailSendRecordService emailSendRecordService;
    private final GoodsService goodsService;
    private final IOrderService orderService;
    private final IEmailTemplateService emailTemplateService;
    private final IEmailTemplateContentService emailTemplateContentService;
    private final IOrderRequestParamService orderRequestParamService;
    private final IEmailOrderSuccessService emailOrderSuccessService;
    private final SystemSecretKeyProperties systemSecretKeyProperties;
    private final IEmailUnsubscribeService emailUnsubscribeService;

    @Override
    public void add(EmailForm email) {
        Email entity = BeanUtils.copy(email, Email.class);
        entity.setDeleteTime(0);
        emailService.getBaseMapper().insert(entity);
    }

    @Override
    public void update(EmailForm email) {
        emailService.getBaseMapper().updateById(BeanUtils.copy(email, Email.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<Email> wrapper = new UpdateWrapper<Email>().in(ID, deleteBatchForm.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        emailService.update(wrapper);
    }

    @Override
    public PageVO<EmailVO> page(EmailPageForm form) {
        QueryWrapper<Email> qw = new QueryWrapper<>();

        // 基本删除条件
        qw.eq(DELETE_TIME, 0);

        // 邮箱名称
        if (StringUtils.isNotBlank(form.getName())) {
            qw.like("name", form.getName());
        }

        // 发件人名称
        if (StringUtils.isNotBlank(form.getSendName())) {
            qw.like("send_name", form.getSendName());
        }

        // 公司
        if (StringUtils.isNotBlank(form.getCompany())) {
            qw.like("company", form.getCompany());
        }

        // 邮箱类型
        if (form.getType() != null) {
            qw.eq("type", form.getType());
        }

        // SMTP
        if (StringUtils.isNotBlank(form.getSmtp())) {
            qw.like("smtp", form.getSmtp());
        }

        // 端口
        if (form.getPort() != null) {
            qw.eq("port", form.getPort());
        }

        // 邮箱
        if (StringUtils.isNotBlank(form.getEmail())) {
            qw.like("email", form.getEmail());
        }

        // 密码
        if (StringUtils.isNotBlank(form.getPassword())) {
            qw.like("password", form.getPassword());
        }

        // 状态
        if (form.getStatus() != null) {
            qw.eq("status", form.getStatus());
        }

        // 发送次数
        if (form.getSendNumbers() != null) {
            qw.eq("send_numbers", form.getSendNumbers());
        }
        qw.orderByDesc("create_time");

        // 执行分页查询
        Page<Email> pageInfo = emailService.page(form.qry(), qw);
        return new PageVO<>(pageInfo, EmailVO.class);
    }

    @Override
    public void sendEmail(SendEmailForm form) {
        String toEmail = form.getToEmail();
        Email fromEmail = emailService.getBaseMapper().selectById(form.getFromEmailId());
        if (fromEmail == null) {
            throw new BizException(ResultEnum.ResourceNotFound.getCode(), "fromEmailId不存在");
        }
        if (fromEmail.getStatus() == 0) {
            throw new BizException(ResultEnum.AuthFail.getCode(), "邮箱禁用");
        }
        if (googleEmailService.send(fromEmail, toEmail, fromEmail.getName(), "content")) {
            //将发送次数+1
            emailService.incrementSendNumbersById(fromEmail.getId());
        }
    }

    @Override
    public void updateState(UpdateStateForm form) {
        Email updateParam = Email.builder().status(form.getStatus()).build();
        updateParam.setId(form.getId());
        emailService.getBaseMapper().updateById(updateParam);
    }


    @Async("taskExecutor")
    @Override
    public void sendReport(Integer paymentStatus, Order order, OrderEnvironmental orderEnvironmental, String email) {
        log.info("发送报告,email:{},paymentStatus:{},outTradeNo:{}", email, paymentStatus, order.getOutTradeNo());
        if (Order.PayStatusEnum.SUCCESS.getValue().equals(paymentStatus)) {
            OrderRequestParam orderRequestParam = orderRequestParamService.getByOrderId(order.getId());

            String goodsName = goodsService.getGoodsName(order.getGoodsId(), order.getGoodsType(), order.getLanguageCode());
            Integer goodsType = order.getGoodsType();
            GoodsInfoBo goodsInfo = goodsService.getGoodsInfo(order.getGoodsId(), goodsType);
            Integer goodsCategoryId = goodsInfo.getGoodsCategoryId();
            String goodsCategory = goodsService.getGoodsCategory(goodsCategoryId, goodsType);
            StringBuilder reportUrlSj = new StringBuilder();
            reportUrlSj.append(orderEnvironmental.getWebSite())
                .append("/")
                .append(goodsInfo.getWebPackageName())
                .append("/result?orderNo=")
                .append(order.getOutTradeNo())
                .append("&lang=")
                .append(order.getLanguageCode())
                .append("&logid=")
                .append(order.getLogId());
            if (org.springframework.util.StringUtils.hasText(order.getTgId())) {
                reportUrlSj.append("&tgid=")
                    .append(order.getTgId());
            }
            if (Objects.nonNull(orderRequestParam)) {
                reportUrlSj.append(orderRequestParam.getAdParam());
            }
            EmailParamBO emailParamBO = new EmailParamBO();
            emailParamBO.setOrderNo(order.getOutTradeNo());
            emailParamBO.setButtonUrl(reportUrlSj.toString());
            emailParamBO.setProduceName(goodsName);
            emailParamBO.setTag(goodsCategory);
            //订单成功并且未发送成功
            String unsubscribeUrl = getUnsubscribeUrl(orderEnvironmental.getWebSite(), email, goodsInfo.getWebPackageName(), order.getLanguageCode());
            emailParamBO.setUnsubscribeUrl(unsubscribeUrl);
            String domain = DomainService.getDomain(orderEnvironmental.getWebSite());
            SendEmailBO sendEmailBO = new SendEmailBO(
                EmailTempletTypeEnum.PURCHASE.getCode(),
                EmailTypeEnum.REPORT.getCode(),
                order.getLanguageCode(),
                email,
                emailParamBO,
                unsubscribeUrl,
                order.getId(),
                order.getOutTradeNo(),
                order.getCreateTime(),
                goodsCategoryId,
                goodsType
            );
            boolean sendEmail = sendEmail(sendEmailBO, domain, goodsCategoryId, goodsType);
            if (sendEmail) {
                EmailOrderSuccess emailOrderSuccess = getEmailOrderSuccess(order, email, domain);
                emailOrderSuccessService.save(DateUtils.getDateString(order.getPaymentTime()), emailOrderSuccess);
                orderService.updateEmailStatus(order.getId(), email);
            }
        }
    }


    @NotNull
    private static EmailOrderSuccess getEmailOrderSuccess(Order order, String email, String domain) {
        EmailOrderSuccess emailOrderSuccess = new EmailOrderSuccess();
        emailOrderSuccess.setOutTradeNo(order.getOutTradeNo());
        emailOrderSuccess.setLanguageCode(order.getLanguageCode());
        emailOrderSuccess.setGoodsId(order.getGoodsId());
        emailOrderSuccess.setGoodsType(order.getGoodsType());
        emailOrderSuccess.setEmail(email);
        emailOrderSuccess.setPaymentTime(order.getPaymentTime());
        emailOrderSuccess.setDomain(domain);
        emailOrderSuccess.setOrderId(order.getId());
        emailOrderSuccess.setThirdEmail(order.getThirdEmail());
        return emailOrderSuccess;
    }

    @Override
    public boolean sendEmail(SendEmailBO sendEmailBo, Email email) {
        log.info("sendEmail, 模板发送邮件 sendEmailBo = {}, email = {}", sendEmailBo, email);
        EmailTemplate template = null;
        String title = null;
        String context = null;
        boolean send = false;
        String errorInfo = "";
        try {
            if (Objects.isNull(email)) {
                log.info("未指定发送邮箱");
                throw new BizException("email is null");
            }
            template = emailTemplateService.getTemplateByType(sendEmailBo.getEmailTemplate(),
                sendEmailBo.getEmailType(), sendEmailBo.getGoodsCategoryId(), sendEmailBo.getGoodsType());
            if (Objects.isNull(template)) {
                log.info("邮件模板不存在， templateType：{}, emailType:{}, goodCategoryId:{},goodsType:{}",
                    sendEmailBo.getEmailTemplate(),
                    sendEmailBo.getEmailType(),
                    sendEmailBo.getGoodsCategoryId(),
                    sendEmailBo.getGoodsType());
                throw new BizException("email template is null");
            }

            Map<String, EmailTemplateContent> contextMap = emailTemplateContentService.mapByTemplateId(template.getId());
            if (CollectionUtils.isEmpty(contextMap)) {
                log.info("邮件模板内容不存在,templateId:{}", template.getId());
                throw new BizException("email template content is empty");

            }
            String defaultLanguage = GoodsTypeEnum.getByValue(sendEmailBo.getGoodsType()).getDefaultLanguage();
            EmailTemplateContent contentEmail = contextMap.get(sendEmailBo.getLanguageCode());
            if (Objects.isNull(contentEmail)) {
                log.info("邮件模板内容语言未配置使用默认语言,用户语言:{}，默认语言：{}", sendEmailBo.getLanguageCode(), defaultLanguage);
                contentEmail = contextMap.get(defaultLanguage);
                if (Objects.isNull(contentEmail)) {
                    log.info("邮件模板内容默认语言未配置,默认语言:{}", defaultLanguage);
                    throw new BizException("email template content is empty");
                }
            }
            log.info("邮件模板, templateId:{}, content:{}", contentEmail.getId(), contentEmail);
            EmailParamBO emailParam = dealTemplate(sendEmailBo.getEmailParam(), template.getId());
            title = dealBusinessParent(contentEmail.getTitle(), emailParam);
            context = dealBusinessParent(contentEmail.getContent(), emailParam);
            log.info("邮件模板, templateId:{}, content:{}", contentEmail.getId(), contentEmail);
            send = emailSendService.send(email, sendEmailBo.getToEmail(), title, context, sendEmailBo.getUnsubscribeUrl());
            if (send) {
                emailService.incrementSendNumbersById(email.getId());
            }
        } catch (BizException e) {
            errorInfo = e.getMessage();
            log.error("邮件发送业务异常", e);
        } catch (Exception e) {
            errorInfo = e.getCause().getMessage();
            log.error("邮件发送报错", e);
        }

        EmailSendRecord emailSendRecord = new EmailSendRecord();
        emailSendRecord.setTitle(title);
        emailSendRecord.setContent(context);
        if (Objects.nonNull(template)) {
            emailSendRecord.setEmailTemplateId(template.getId());
        }else {
            emailSendRecord.setEmailTemplateId(0);
        }
        emailSendRecord.setOrderId(sendEmailBo.getOrderId());
        emailSendRecord.setOutTradeNo(sendEmailBo.getOutTradeNo());
        emailSendRecord.setOrderCreateTime(sendEmailBo.getOrderCreateTime());
        emailSendRecord.setSendStatus(send ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());
        emailSendRecord.setEmail(sendEmailBo.getToEmail());
        emailSendRecord.setErrorInfo(errorInfo);
        emailSendRecord.setLanguageCode(sendEmailBo.getLanguageCode());
        emailSendRecordService.save(DateUtils.getDateString(sendEmailBo.getOrderCreateTime()), emailSendRecord);
        return send;
    }

    //追加模板id 如果点击则标记为模板回来的
    private EmailParamBO dealTemplate(EmailParamBO emailParam, Integer templateId) {
        emailParam.setButtonUrl(emailParam.getButtonUrl() + "&templateId=" + templateId);
        return emailParam;
    }

    @Override
    public String getUnsubscribeUrl(String webSite, String toEmail, String webPackageName, String languageCode) {
        StringBuilder unsubscribeUrl = new StringBuilder(webSite);
        UnsubscribeEmailBO unsubscribeEmail = new UnsubscribeEmailBO();
        unsubscribeEmail.setEmail(toEmail);
        unsubscribeEmail.setWebPackageName(webPackageName);
        String token = AesCryptoUtil.generateUnsubscribeToken(systemSecretKeyProperties.getEmailTokenKey(), JSONObject.toJSONString(unsubscribeEmail));
        unsubscribeUrl.append("/").append(webPackageName);
        unsubscribeUrl.append("/td?token=");
        unsubscribeUrl.append(token);
        unsubscribeUrl.append("&email=");
        unsubscribeUrl.append(toEmail);
        unsubscribeUrl.append("&lang=");
        unsubscribeUrl.append(languageCode);
        return unsubscribeUrl.toString();
    }

    @Override
    public PageVO<UnsubscribeEmailVO> unsubscribeList(UnsubscribeEmailForm form) {
        Page<EmailUnsubscribe> page = emailUnsubscribeService.pageInfo(form);
        List<EmailUnsubscribe> records = page.getRecords();
        List<UnsubscribeEmailVO> unsubscribeEmailVOList = records.stream().map(record -> {
            UnsubscribeEmailVO unsubscribeEmail = BeanUtils.copy(record, UnsubscribeEmailVO.class);
            if (BooleanEnum.TRUE.getValue().equals(record.getStatus())) {
                unsubscribeEmail.setUnsubscribeTime(record.getTime());
            } else {
                unsubscribeEmail.setRecoverTime(record.getTime());
            }
            return unsubscribeEmail;
        }).collect(Collectors.toList());
        return new PageVO<UnsubscribeEmailVO>(page).convert(unsubscribeEmailVOList);
    }

    @Override
    public void recoverUnsubscribe(Integer id) {
        emailUnsubscribeService.recoverUnsubscribe(id);
    }

    @Override
    public boolean sendEmail(SendEmailBO sendEmailBo, String domain, Integer goodsTypeId, Integer goodsType) {
        //根据域名获取邮件
        Domain domainEntity = domainService.getByDomain(domain);
        if (Objects.isNull(domainEntity)) {
            log.info("没有找到对应域名的邮箱,domain:{},goodsTypeId:{},goodsType:{},sendEmailBo:{}", domain, goodsTypeId, goodsType, sendEmailBo);
            return false;
        }
        List<Email> emails = emailService.getEmailByIds(domainEntity.getEmailIds());
        for (Email email : emails) {
            boolean sendEmail = sendEmail(sendEmailBo, email);
            if (sendEmail) {
                return true;
            }
        }
        return false;
    }


    private static String dealBusinessParent(String content, EmailParamBO businessData) {
        if (org.springframework.util.StringUtils.hasText(businessData.getOrderNo())) {
            content = content
                    .replaceAll("\\{orderNo}", businessData.getOrderNo());
        }

        if (org.springframework.util.StringUtils.hasText(businessData.getButtonUrl())) {
            content = content
                    .replaceAll("\\{buttonUrl}", businessData.getButtonUrl());
        }
        if (org.springframework.util.StringUtils.hasText(businessData.getTag())) {
            content = content
                    .replaceAll("\\{tag}", businessData.getTag());
        }
        if (org.springframework.util.StringUtils.hasText(businessData.getEmail())) {
            content = content
                    .replaceAll("\\{email}", businessData.getEmail());
        }
        if (org.springframework.util.StringUtils.hasText(businessData.getDiscount())) {
            content = content
                    .replaceAll("\\{discount}", businessData.getDiscount());
        }

        if (org.springframework.util.StringUtils.hasText(businessData.getProduceName())) {
            content = content
                    .replaceAll("\\{produceName}", businessData.getProduceName());
        }

        if (org.springframework.util.StringUtils.hasText(businessData.getUnsubscribeUrl())) {
            content = content
                    .replaceAll("\\{unsubscribeUrl}", businessData.getUnsubscribeUrl());
        }
        return content;
    }


}




