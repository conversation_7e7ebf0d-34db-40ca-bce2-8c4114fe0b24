package com.miaowen.bh1xlhw.service.email;



import cn.hutool.extra.mail.MailAccount;
import com.miaowen.bh1xlhw.model.entity.Email;

import java.io.File;
import java.util.List;

/**
 * @Description 邮件发送策略接口
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/18 17:49
 */
public interface EmailSendService {
    /**
     * 发送邮件
     * @param account 邮件账户
     * @param toEmail 收件人邮箱
     * @param content 发送内容
     * @param attachment 发送文件
     */
    void send(MailAccount account, String toEmail, String content, File attachment);


    /**
     * 发送邮件
     * @param account 邮件账户
     * @param toEmail 收件人邮箱
     * @param content 发送内容
     * @param attachments 发送文件列表
     */
    void send(MailAccount account, String toEmail, String content, List<File> attachments);

    /**
     * 获取策略类型
     * @return 策略类型
     */
    Integer getType();

    /**
     * 发送邮件
     *
     * @param account 邮件账户
     * @param toEmail 收件人邮箱
     * @param subject 邮件标题
     * @param content 邮件内容
     */
    boolean send(Email account, String toEmail, String subject, String content);
    /**
     * 发送邮件
     *
     * @param account 邮件账户
     * @param toEmail 收件人邮箱
     * @param subject 邮件标题
     * @param content 邮件内容
     */
    boolean send(Email account, String toEmail, String subject, String content, String unsubscribeUrl);
}
