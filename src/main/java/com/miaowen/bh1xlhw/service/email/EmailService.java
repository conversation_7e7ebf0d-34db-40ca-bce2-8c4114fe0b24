package com.miaowen.bh1xlhw.service.email;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.bo.email.SendEmailBO;
import com.miaowen.bh1xlhw.model.entity.Email;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.entity.order.OrderEnvironmental;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email.EmailForm;
import com.miaowen.bh1xlhw.model.query.email.EmailPageForm;
import com.miaowen.bh1xlhw.model.query.email.SendEmailForm;
import com.miaowen.bh1xlhw.model.query.email.UnsubscribeEmailForm;
import com.miaowen.bh1xlhw.model.vo.email.EmailVO;
import com.miaowen.bh1xlhw.model.vo.email.UnsubscribeEmailVO;

/**
 * @ClassName EmailService
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/8 15:10
 */
public interface EmailService {

    void add(EmailForm email);
    void update(EmailForm email);
    void deleteBatch(DeleteBatchForm form);
    PageVO<EmailVO> page(EmailPageForm form);
    void sendEmail(SendEmailForm form);
    void updateState(UpdateStateForm form);
    void sendReport(Integer paymentStatus, Order order, OrderEnvironmental orderEnvironmental, String email);
    String getUnsubscribeUrl(String serverName, String toEmail, String webPackageName, String languageCode);

    PageVO<UnsubscribeEmailVO> unsubscribeList(UnsubscribeEmailForm form);

    void recoverUnsubscribe(Integer id);


    /**
     * 发送邮箱
     *
     * @param sendEmailBo 邮件发送相关参数
     * @param domain      发送邮件域名，通过域名找到对应的邮箱
     * @param goodsTypeId
     * @param goodsType
     * @return boolean 是否发送成功
     */
    boolean sendEmail(SendEmailBO sendEmailBo, String domain, Integer goodsTypeId, Integer goodsType);


    /**
     * 发送邮箱

     * @param sendEmailBo 邮件发送相关参数
     * @param email    发送邮件的email
     * @return boolean 是否发送成功
     */
    boolean sendEmail(SendEmailBO sendEmailBo, Email email);



}
