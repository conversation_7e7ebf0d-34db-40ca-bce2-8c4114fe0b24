package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.constant.enums.RedisKeyEnum;
import com.miaowen.bh1xlhw.model.entity.SystemEvent;
import com.miaowen.bh1xlhw.model.query.logManagement.EventSettingForm;
import com.miaowen.bh1xlhw.model.query.logManagement.EventSettingPageForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SystemEventVo;
import com.miaowen.bh1xlhw.repository.SystemEventService;
import com.miaowen.bh1xlhw.service.logManagement.EventSettingService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 后台事件配置
 *
 * @Author：huanglong
 * @Date：2025/5/7 10:58
 */
@Slf4j
@Service
@AllArgsConstructor
public class EventSettingServiceImpl implements EventSettingService {

    private final SystemEventService systemEventService;
    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public PageVO<SystemEventVo> page(EventSettingPageForm pageForm) {
        // 获取查询条件Wrapper
        QueryWrapper<SystemEvent> wrapper = new QueryWrapper<>();
        // 添加排序条件
        if (!StringUtils.isEmpty(pageForm.getEventName())) {
            wrapper.like("name", pageForm.getEventName());
        }
        if (!StringUtils.isEmpty(pageForm.getEventCode())) {
            wrapper.eq("code", pageForm.getEventCode());
        }
        if (!StringUtils.isEmpty(pageForm.getEventType())) {
            wrapper.eq("type_code", pageForm.getEventType());
        }
        wrapper.orderByDesc("code");  // 或使用"createTime"如果使用实体属性名
        wrapper.eq("status", 1);  // 添加其他条件


        // 执行分页查询
        Page<SystemEvent> page = systemEventService.page(new Page<>(pageForm.getPageInt(), pageForm.getPageSize()), wrapper);
        return new PageVO<>(page, SystemEventVo.class);
    }

    @Override
    public void delete(List<Long> ids) {
        systemEventService.removeByIds(ids);
    }

    @Override
    public ResultVO<Void> add(EventSettingForm settingForm) {
        //判断事件编码是否存在
        QueryWrapper<SystemEvent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", settingForm.getCode());
        if (systemEventService.count(queryWrapper) > 0) {
            return ResultVO.fail(ResultEnum.DataCheckFail, "事件编码已存在");
        }
        //保存
        systemEventService.save(BeanUtils.copy(settingForm, SystemEvent.class));
        cleanRedis(settingForm.getCode());
        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> update(EventSettingForm settingForm) {

        //判断事件编码是否存在 排除当前数据
        QueryWrapper<SystemEvent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("code", settingForm.getCode());
        queryWrapper.ne("id", settingForm.getId());
        if (systemEventService.count(queryWrapper) > 0) {
            return ResultVO.fail(ResultEnum.DataCheckFail, "事件编码已存在");
        }

        //更新
        systemEventService.updateById(BeanUtils.copy(settingForm, SystemEvent.class));
        cleanRedis(settingForm.getCode());
        return ResultVO.success();
    }

    private void cleanRedis(String eventCode){
        String key = RedisKeyEnum.SYSTEM_EVENT.getKey(eventCode);
        stringRedisTemplate.delete(key);
    }
}
