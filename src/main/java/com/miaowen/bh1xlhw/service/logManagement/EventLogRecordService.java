package com.miaowen.bh1xlhw.service.logManagement;

import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventBrowseCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventLogRecordVO;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/8 16:02
 */
public interface EventLogRecordService {
    List<EventLogRecordVO> list(Integer orderId);

    List<EventCountVO> count(String platformCode);

    List<EventCountVO> countByTgid(String tgid, String platformCode);

    List<EventBrowseCountVO> browseCount(BrowseCountForm form);

    List<EventLogRecordVO> listByLogId(String logId);
}
