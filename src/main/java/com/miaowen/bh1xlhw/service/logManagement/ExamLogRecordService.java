package com.miaowen.bh1xlhw.service.logManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.logManagement.ExamLogForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.ExamLogVO;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 10:37
 */
public interface ExamLogRecordService {

    ResultVO<PageVO<ExamLogVO>> page(ExamLogForm pageForm);

}
