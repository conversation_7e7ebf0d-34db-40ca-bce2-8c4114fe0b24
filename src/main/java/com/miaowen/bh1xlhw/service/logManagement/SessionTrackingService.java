package com.miaowen.bh1xlhw.service.logManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.logManagement.EventSessionLogParamVo;
import com.miaowen.bh1xlhw.model.query.logManagement.SessionTrackingForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SessionTrackingVO;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description 会话跟踪service
 * @Author：huanglong
 * @Date：2025/5/8 11:39
 */
public interface SessionTrackingService {
    PageVO<SessionTrackingVO> page(SessionTrackingForm pageForm);

}
