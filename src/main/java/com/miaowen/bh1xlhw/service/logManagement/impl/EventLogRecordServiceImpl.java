package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.enums.PaymentStatusEnum;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.entity.DictionaryType;
import com.miaowen.bh1xlhw.model.entity.EventLog;
import com.miaowen.bh1xlhw.model.entity.ExamLog;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventBrowseCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventLogRecordVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.logManagement.EventLogRecordService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 事件日志
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 16:02
 */
@Slf4j
@Service
@AllArgsConstructor
public class EventLogRecordServiceImpl implements EventLogRecordService {

    private final EventLogService eventLogService;
    private final SessionTrackingLogService sessionTrackingLogService;
    private final IOrderService orderService;
    private final ExamLogService examLogService;
    private final DictionaryService dictionaryService;
    private final DictionaryTypeService dictionaryTypeService;

    @Override
    public List<EventLogRecordVO> listByLogId(String logId) {
        try {
            QueryWrapper<EventLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("log_id", logId);
            queryWrapper.orderByAsc("create_time");
            MonthShardingTableNameHandler.setParams(DateUtils.getDateByLogId(logId));
            List<EventLog> eventLogs = eventLogService.list(queryWrapper);
            return BeanUtils.copyList(eventLogs, EventLogRecordVO.class);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    @Override
    public List<EventLogRecordVO> list(Integer orderId) {
        Order order = orderService.getById(orderId);
        if (order == null || order.getEmail() == null) {
            return Collections.emptyList();
        }
        try {
            QueryWrapper<EventLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("log_id", order.getLogId());
            queryWrapper.orderByAsc("create_time");
            MonthShardingTableNameHandler.setParams(DateUtils.getDateByLogId(order.getLogId()));
            List<EventLog> eventLogs = eventLogService.list(queryWrapper);
            return BeanUtils.copyList(eventLogs, EventLogRecordVO.class);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }

    }

    @Override
    public List<EventCountVO> count(String platformCode) {
        //查询存储的字典集合
        List<EventCountVO> result = new ArrayList<>();
        DictionaryType dictionaryType = dictionaryTypeService.getOneByCode("chartCode");
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dictionary_type_id", dictionaryType.getId());
        List<Dictionary> dictionaryList = dictionaryService.list(queryWrapper);

        for (Dictionary dictionary : dictionaryList) {
            EventCountVO eventCountVO = new EventCountVO();

            QueryWrapper<EventLog> eventLogQueryWrapper = new QueryWrapper<>();
            eventLogQueryWrapper.eq("event_code", dictionary.getCode());
            //根据平台编码查询
            if (!StringUtils.isEmpty(platformCode)) {
                eventLogQueryWrapper.eq("platform_code", platformCode);
            }
            eventCountVO.setCount(Math.toIntExact(eventLogService.count(eventLogQueryWrapper)));

            eventCountVO.setEventName(dictionary.getName());
            eventCountVO.setEventCode(dictionary.getCode());
            result.add(eventCountVO);
        }

        return result;
    }

    @Override
    public List<EventCountVO> countByTgid(String tgid, String platformCode) {
        //查询存储的字典集合
        List<EventCountVO> result = new ArrayList<>();
        DictionaryType dictionaryType = dictionaryTypeService.getOneByCode("chartCode");
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dictionary_type_id", dictionaryType.getId());
        List<Dictionary> dictionaryList = dictionaryService.list(queryWrapper);

        for (Dictionary dictionary : dictionaryList) {
            EventCountVO eventCountVO = new EventCountVO();

            QueryWrapper<EventLog> eventLogQueryWrapper = new QueryWrapper<>();
            eventLogQueryWrapper.eq("event_code", dictionary.getCode());
            //根据平台编码查询
            if (!StringUtils.isEmpty(platformCode)) {
                eventLogQueryWrapper.eq("platform_code", platformCode);
            }
            if (!StringUtils.isEmpty(tgid)) {
                eventLogQueryWrapper.eq("tgid", tgid);
            }
            eventCountVO.setCount(Math.toIntExact(eventLogService.count(eventLogQueryWrapper)));

            eventCountVO.setEventName(dictionary.getName());
            eventCountVO.setEventCode(dictionary.getCode());
            result.add(eventCountVO);
        }

        return result;
    }

    // 定义常量用于事件名称
    private static final String LANDING_PAGE_VISITORS = "进入落地页人数";
    private static final String EXAM_TAKERS = "做题人数";
    private static final String EXAM_COMPLETERS = "完成做题人数";
    private static final String PAYING_USERS = "付费人数";
    private static final String RED_PACKAGE_RATE = "红包拦截率";
    private static final String EXAM_COMPLETION_RATE = "做题完成率";
    private static final String OVERALL_PAYMENT_RATE = "整体付费率";
    private static final String PACKAGE_PAYMENT_RATE = "红包付费率";
    private static final String EMAIL_PAYMENT_RATE = "邮件付费率";


    @Override
    public List<EventBrowseCountVO> browseCount(BrowseCountForm form) {
        List<EventBrowseCountVO> result = new ArrayList<>();

        // 1. 进入落地页人数统计
        int landingPageCount = eventLogService.countByCondition(form);
        addEventCount(result, LANDING_PAGE_VISITORS,
                String.valueOf(Math.max(landingPageCount, 0)));

        // 2. 做题相关统计
        List<ExamLog> examLogs = examLogService.getByCondition(form);
        long totalExamTakers = CollectionUtils.isEmpty(examLogs) ? 0 : countDistinctLogIds(examLogs);
        addEventCount(result, EXAM_TAKERS, String.valueOf(totalExamTakers));

        long completedExamTakers = CollectionUtils.isEmpty(examLogs) ? 0 : countCompletedExamTakers(examLogs);
        addEventCount(result, EXAM_COMPLETERS, String.valueOf(completedExamTakers));

        // 3. 付费相关统计
        List<Order> orders = orderService.selectByCondition(form);
        long payingUsersCount = CollectionUtils.isEmpty(orders) ? 0 : countPayingUsers(orders);
        addEventCount(result, PAYING_USERS, String.valueOf(payingUsersCount));


        // 4. 计算比率
        //红包拦截率   红包领取总数（红包1+红包2 ） /  订单数
        addEventCount(result, RED_PACKAGE_RATE, calculateRate(countPackage(orders), orders.size()));

        addEventCount(result, EXAM_COMPLETION_RATE,
                calculateRate(completedExamTakers, totalExamTakers));
        addEventCount(result, OVERALL_PAYMENT_RATE,
                calculateRate(payingUsersCount,
                        CollectionUtils.isEmpty(orders) ? 0 : orders.size()));

        //红包付费率  红包支付数 / 领取红包总数 （排除邮件类型）
        addEventCount(result, PACKAGE_PAYMENT_RATE, packagePaymentRate(orders));
        //邮件付费 / 发送邮件订单数
        addEventCount(result, EMAIL_PAYMENT_RATE, getEmailPaymentRate(orders));
        return result;
    }


    private String getEmailPaymentRate(List<Order> orders) {
        if (orders.isEmpty()) {
            return "0%";
        }
        //邮件付费 / 发送邮件订单数
        long emailPayCount = 0;
        long emailGetCount = 0;
        for (Order order : orders) {
            if (order.getIsEmail() == 1) {
                emailGetCount++;
            }
            if (Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.Success.getType()) && order.getIsEmail() == 1) {
                emailPayCount++;
            }
        }
        return calculateRate(emailPayCount, emailGetCount);
    }

    private String packagePaymentRate(List<Order> orders) {
        if (orders.isEmpty()) {
            return "0%";
        }
        //红包支付数 / 领取红包总数 （排除邮件类型）
        //红包支付数
        long redPackPayCount = 0;
        long redPackGetCount = 0;
        for (Order order : orders) {
            if (order.getIsEmail() == 0) {
                if (order.getRedPackGetType() == 1 || order.getRedPackGetType() == 2) {
                    redPackGetCount++;
                }
                if (Objects.equals(order.getPaymentStatus(), PaymentStatusEnum.Success.getType()) && (order.getRedPackUseType() == 1 || order.getRedPackUseType() == 2)) {
                    redPackPayCount++;
                }
            }
        }
        return calculateRate(redPackPayCount, redPackGetCount);
    }


    // 辅助方法保持不变
    private void addEventCount(List<EventBrowseCountVO> result, String eventName, String index) {
        EventBrowseCountVO vo = new EventBrowseCountVO();
        vo.setEventName(eventName);
        vo.setIndex(index);
        result.add(vo);
    }

    private long countPackage(List<Order> orders) {
        return orders.stream()
                .filter(order -> order.getRedPackGetType() == 1 || order.getRedPackGetType() == 2)
                .count();
    }

    private long countDistinctLogIds(List<ExamLog> examLogs) {
        return examLogs.stream()
                .map(ExamLog::getLogId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
    }

    private long countCompletedExamTakers(List<ExamLog> examLogs) {
        return examLogs.stream()
                .filter(log -> log.getQuestionsNum() != null
                        && log.getExamNum() != null
                        && log.getQuestionsNum().equals(log.getExamNum()))
                .map(ExamLog::getLogId)
                .filter(Objects::nonNull)
                .distinct()
                .count();
    }

    private long countPayingUsers(List<Order> orders) {
        return orders.stream()
                .filter(order -> order.getPaymentStatus() != null
                        && order.getPaymentStatus().equals(PaymentStatusEnum.Success.getType()))
                .count();
    }

    private String calculateRate(long numerator, long denominator) {
        if (denominator == 0) {
            return "0%";
        }
        double rate = (double) numerator / denominator * 100;
        return String.format("%.2f%%", rate);
    }


}
