package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.entity.DictionaryType;
import com.miaowen.bh1xlhw.model.entity.EventLog;
import com.miaowen.bh1xlhw.model.entity.ExamLog;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventBrowseCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventCountVO;
import com.miaowen.bh1xlhw.model.vo.logManagement.EventLogRecordVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.logManagement.EventLogRecordService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 事件日志
 *
 * @Author：huanglong
 * @Date：2025/5/8 16:02
 */
@Slf4j
@Service
@AllArgsConstructor
public class EventLogRecordServiceImpl implements EventLogRecordService {

    private final EventLogService eventLogService;
    private final SessionTrackingLogService sessionTrackingLogService;
    private final IOrderService orderService;
    private final ExamLogService examLogService;
    private final DictionaryService dictionaryService;
    private final DictionaryTypeService dictionaryTypeService;

    @Override
    public List<EventLogRecordVO> listByLogId(String logId) {
        try {
            QueryWrapper<EventLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("log_id", logId);
            queryWrapper.orderByAsc("create_time");
            MonthShardingTableNameHandler.setParams(DateUtils.getDateByLogId(logId));
            List<EventLog> eventLogs = eventLogService.list(queryWrapper);
            return BeanUtils.copyList(eventLogs, EventLogRecordVO.class);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    @Override
    public List<EventLogRecordVO> list(Integer orderId) {
        Order order = orderService.getById(orderId);
        if (order == null || order.getEmail() == null) {
            return Collections.emptyList();
        }
        try {
            QueryWrapper<EventLog> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("log_id", order.getLogId());
            queryWrapper.orderByAsc("create_time");
            MonthShardingTableNameHandler.setParams(DateUtils.getDateByLogId(order.getLogId()));
            List<EventLog> eventLogs = eventLogService.list(queryWrapper);
            return BeanUtils.copyList(eventLogs, EventLogRecordVO.class);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }

    }

    @Override
    public List<EventCountVO> count(String platformCode) {
        //查询存储的字典集合
        List<EventCountVO> result = new ArrayList<>();
        DictionaryType dictionaryType = dictionaryTypeService.getOneByCode("chartCode");
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dictionary_type_id", dictionaryType.getId());
        List<Dictionary> dictionaryList = dictionaryService.list(queryWrapper);

        for (Dictionary dictionary : dictionaryList) {
            EventCountVO eventCountVO = new EventCountVO();

            QueryWrapper<EventLog> eventLogQueryWrapper = new QueryWrapper<>();
            eventLogQueryWrapper.eq("event_code", dictionary.getCode());
            //根据平台编码查询
            if (!StringUtils.isEmpty(platformCode)) {
                eventLogQueryWrapper.eq("platform_code", platformCode);
            }
            eventCountVO.setCount(Math.toIntExact(eventLogService.count(eventLogQueryWrapper)));

            eventCountVO.setEventName(dictionary.getName());
            eventCountVO.setEventCode(dictionary.getCode());
            result.add(eventCountVO);
        }

        return result;
    }

    @Override
    public List<EventCountVO> countByTgid(String tgid, String platformCode) {
        //查询存储的字典集合
        List<EventCountVO> result = new ArrayList<>();
        DictionaryType dictionaryType = dictionaryTypeService.getOneByCode("chartCode");
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dictionary_type_id", dictionaryType.getId());
        List<Dictionary> dictionaryList = dictionaryService.list(queryWrapper);

        for (Dictionary dictionary : dictionaryList) {
            EventCountVO eventCountVO = new EventCountVO();

            QueryWrapper<EventLog> eventLogQueryWrapper = new QueryWrapper<>();
            eventLogQueryWrapper.eq("event_code", dictionary.getCode());
            //根据平台编码查询
            if (!StringUtils.isEmpty(platformCode)) {
                eventLogQueryWrapper.eq("platform_code", platformCode);
            }
            if (!StringUtils.isEmpty(tgid)) {
                eventLogQueryWrapper.eq("tgid", tgid);
            }
            eventCountVO.setCount(Math.toIntExact(eventLogService.count(eventLogQueryWrapper)));

            eventCountVO.setEventName(dictionary.getName());
            eventCountVO.setEventCode(dictionary.getCode());
            result.add(eventCountVO);
        }

        return result;
    }

    // 定义常量用于事件名称
    private static final String LANDING_PAGE_VISITORS = "进入落地页人数";
    private static final String EXAM_TAKERS = "做题人数";
    private static final String EXAM_COMPLETERS = "完成做题人数";
    private static final String PAYING_USERS = "付费人数";
    private static final String RED_PACKAGE_RATE = "红包拦截率";
    private static final String EXAM_COMPLETION_RATE = "做题完成率";
    private static final String OVERALL_PAYMENT_RATE = "整体付费率";
    private static final String PACKAGE_PAYMENT_RATE = "红包付费率";
    private static final String EMAIL_PAYMENT_RATE = "邮件付费率";


    @Override
    public List<EventBrowseCountVO> browseCount(BrowseCountForm form) {
        try {
            MonthShardingTableNameHandler.setParams(form.getStartTime());
            List<EventBrowseCountVO> result = new ArrayList<>();

            // 1. 进入落地页人数统计
            int landingPageCount = eventLogService.countByCondition(form);
            addEventCount(result, LANDING_PAGE_VISITORS,
                    String.valueOf(Math.max(landingPageCount, 0)));

            // 2. 做题相关统计
            long totalExamTakers = countExam(form);
            addEventCount(result, EXAM_TAKERS, String.valueOf(totalExamTakers));

            long completedExamTakers = countCompleteExam(form);
            addEventCount(result, EXAM_COMPLETERS, String.valueOf(completedExamTakers));


            // 3. 付费相关统计
            //总数
            long total = selectByCondition(form);
            //付费人数
            long payingUsersCount = countPayingUsers(form);
            //红包领取总数
            long redPackageGetCount = countPackage(form);
            //领取红包总数 （排除邮件类型）
            long countPackageNoEmail = countPackageNoEmail(form);
            //红包支付数量 （排除邮件类型）
            long countPackageUseNoEmail = countPackageUseNoEmail(form);
            //邮件付费数
            long countEmailPay = countEmailPay(form);
            //发送邮件订单数
            long countEmailGet = countEmailGet(form);
            addEventCount(result, PAYING_USERS, String.valueOf(payingUsersCount));


            // 4. 计算比率
            //红包拦截率   红包领取总数（红包1+红包2 ） /  订单数
            addEventCount(result, RED_PACKAGE_RATE, calculateRate(redPackageGetCount, total));

            addEventCount(result, EXAM_COMPLETION_RATE,
                    calculateRate(completedExamTakers, totalExamTakers));
            addEventCount(result, OVERALL_PAYMENT_RATE,
                    calculateRate(payingUsersCount,
                            total));

            //红包付费率  红包支付数 / 领取红包总数 （排除邮件类型）
            addEventCount(result, PACKAGE_PAYMENT_RATE, calculateRate(countPackageUseNoEmail, countPackageNoEmail));
            //邮件付费 / 发送邮件订单数
            addEventCount(result, EMAIL_PAYMENT_RATE, calculateRate(countEmailPay, countEmailGet));
            return result;
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }


    private String calculateRate(long numerator, long denominator) {
        if (denominator == 0 || numerator == 0) {
            return "0%";
        }
        double rate = (double) numerator / denominator * 100;
        return String.format("%.2f%%", rate);
    }

    // 辅助方法保持不变
    private void addEventCount(List<EventBrowseCountVO> result, String eventName, String index) {
        EventBrowseCountVO vo = new EventBrowseCountVO();
        vo.setEventName(eventName);
        vo.setIndex(index);
        result.add(vo);
    }

    /**
     * 构建基础ExamLog查询条件
     */
    private QueryWrapper<ExamLog> buildExamLogQuery(BrowseCountForm form) {
        QueryWrapper<ExamLog> wrapper = new QueryWrapper<>();
        wrapper.eq("tuid", form.getTgid())
                .between("create_time", form.getStartTime(), form.getEndTime());

        if (!StringUtils.isEmpty(form.getSource())) {
            wrapper.eq("source", form.getSource());
        }
        wrapper.select("DISTINCT log_id");
        return wrapper;
    }

    /**
     * 构建基础Order查询条件
     */
    private QueryWrapper<Order> buildOrderQuery(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = new QueryWrapper<>();
        wrapper.eq("tg_id", form.getTgid())
                .between("create_time", form.getStartTime(), form.getEndTime());

        if (!StringUtils.isEmpty(form.getSource())) {
            wrapper.eq("source", form.getSource());
        }
        wrapper.select("DISTINCT log_id");
        return wrapper;
    }

    /**
     * 执行分表查询并确保参数清理
     */
    private long executeShardingQuery(QueryWrapper<ExamLog> wrapper) {
        return examLogService.count(wrapper);
    }

    //做题人数
    private long countExam(BrowseCountForm form) {
        QueryWrapper<ExamLog> wrapper = buildExamLogQuery(form);
        return executeShardingQuery(wrapper);
    }

    //完成做题人数
    private long countCompleteExam(BrowseCountForm form) {
        QueryWrapper<ExamLog> wrapper = buildExamLogQuery(form);
        wrapper.apply("questions_num = exam_num");
        return executeShardingQuery(wrapper);
    }

    //红包领取
    private long countPackage(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = buildOrderQuery(form);
        wrapper.in("red_pack_get_type", Arrays.asList(1, 2));
        return orderService.count(wrapper);
    }

    //领取红包总数（排除邮件类型）
    private long countPackageNoEmail(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = buildOrderQuery(form);
        wrapper.in("red_pack_get_type", Arrays.asList(1, 2))
                .eq("is_email", 0);
        return orderService.count(wrapper);
    }

    //红包支付数量（排除邮件类型）
    private long countPackageUseNoEmail(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = buildOrderQuery(form);
        wrapper.in("red_pack_use_type", Arrays.asList(1, 2))
                .eq("is_email", 0)
                .eq("payment_status", BaseOrder.PayStatusEnum.SUCCESS.getValue());
        return orderService.count(wrapper);
    }

    //邮件付费
    private long countEmailPay(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = buildOrderQuery(form);
        wrapper.eq("is_email", 1)
                .eq("payment_status", BaseOrder.PayStatusEnum.SUCCESS.getValue());
        return orderService.count(wrapper);
    }

    //邮件领取
    private long countEmailGet(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = buildOrderQuery(form);
        wrapper.eq("is_email", 1);
        return orderService.count(wrapper);
    }

    //付费人数
    private long countPayingUsers(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = buildOrderQuery(form);
        wrapper.eq("payment_status", BaseOrder.PayStatusEnum.SUCCESS.getValue());
        return orderService.count(wrapper);
    }

    //基础订单查询
    private long selectByCondition(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = buildOrderQuery(form);
        return orderService.count(wrapper);
    }
}
