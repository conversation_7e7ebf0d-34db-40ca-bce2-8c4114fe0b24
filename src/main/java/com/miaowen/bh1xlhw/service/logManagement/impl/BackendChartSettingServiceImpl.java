package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.entity.DictionaryType;
import com.miaowen.bh1xlhw.model.entity.SystemEvent;
import com.miaowen.bh1xlhw.repository.DictionaryService;
import com.miaowen.bh1xlhw.repository.DictionaryTypeService;
import com.miaowen.bh1xlhw.repository.SystemEventService;
import com.miaowen.bh1xlhw.service.logManagement.BackendChartSettingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 图表配置
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/9 14:15
 */
@Slf4j
@Service
@AllArgsConstructor
public class BackendChartSettingServiceImpl implements BackendChartSettingService {

    private final DictionaryService dictionaryService;
    private final DictionaryTypeService dictionaryTypeService;
    private final SystemEventService systemEventService;

    @Override
    public ResultVO<Void> chartSetting(String chartCode) {
        if (StringUtils.isEmpty(chartCode)) {
            return null;
        }
        List<String> collect = Arrays.stream(chartCode.split(","))
                .map(String::trim)
                .collect(Collectors.toList());
        QueryWrapper<SystemEvent> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("code", collect);
        List<SystemEvent> list = systemEventService.list(queryWrapper);
        //判断是否已经在事件配置表内
        if (!checkChartCode(list, collect)) {
            return ResultVO.fail(ResultEnum.DataCheckFail, "输入code未在事件配置表内");
        }
        saveDictionary(list, getDictionaryTypeId());
        return ResultVO.success();
    }

    @Override
    public String getChartSetting() {
        QueryWrapper<Dictionary> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dictionary_type_id", getDictionaryTypeId());
        List<Dictionary> list = dictionaryService.list(queryWrapper);
        return list.stream()
                .map(Dictionary::getCode)
                .collect(Collectors.joining(","));
    }


    private boolean checkChartCode(List<SystemEvent> list, List<String> collect) {
        if (list.size() != collect.size()) {
            return false;
        }
        for (SystemEvent systemEvent : list) {
            if (!collect.contains(systemEvent.getCode())) {
                return false;
            }
        }
        return true;
    }


    private Integer getDictionaryTypeId() {
        DictionaryType dictionaryType = dictionaryTypeService.getOneByCode("chartCode");
        if (Objects.isNull(dictionaryType)) {
            //为空则创建
            dictionaryType = new DictionaryType();
            dictionaryType.setCode("chartCode");
            dictionaryTypeService.save(dictionaryType);
            return getDictionaryTypeId();
        }
        return dictionaryType.getId();

    }

    private void saveDictionary(List<SystemEvent> list, Integer dictionaryTypeId) {

        //删除原有配置
        dictionaryService.remove(new QueryWrapper<Dictionary>().eq("dictionary_type_id", dictionaryTypeId));

        //保存新配置
        for (SystemEvent systemEvent : list) {
            Dictionary dictionary = new Dictionary();
            dictionary.setDictionaryTypeId(dictionaryTypeId);
            dictionary.setCode(systemEvent.getCode());
            dictionary.setName(systemEvent.getName());
            dictionary.setDescription("后台图表配置");
            dictionaryService.save(dictionary);
        }
    }

}
