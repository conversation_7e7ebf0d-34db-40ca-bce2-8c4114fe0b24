package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.entity.SessionTrackingLog;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.logManagement.SessionTrackingForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SessionTrackingVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.logManagement.SessionTrackingService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会话追踪
 *
 * @Author：huanglong
 * @Date：2025/5/8 11:39
 */
@Slf4j
@Service
@AllArgsConstructor
public class SessionTrackingServiceImpl implements SessionTrackingService {

    private final SessionTrackingLogService sessionTrackingLogService;
    private final IOrderService orderService;
    private final EventLogService eventLogService;
    private final SystemEventService systemEventService;
    private final ICurrencyService currencyService;


    @Override
    public PageVO<SessionTrackingVO> page(SessionTrackingForm form) {

        LocalDateTime startTime;
        if (form.getStartTime() != null && form.getEndTime() != null) {
            startTime = DateUtils.getEast8LocalDateTime(form.getTimezone(), form.getStartTime());
        } else {
            startTime = LocalDate.now().withDayOfMonth(1).atStartOfDay();
        }
        Page<SessionTrackingLog> page = sessionTrackingLogService.pageInfo(DateUtils.getDateString(startTime), form);
        List<SessionTrackingLog> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return new PageVO<>();
        }
        List<SessionTrackingVO> vo = getVo(records, form.getTimezone());
        return new PageVO<SessionTrackingVO>(page).convert(vo);
    }

    private List<SessionTrackingVO> getVo(List<SessionTrackingLog> records, String timezone) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        // 一次性提取所有有效订单号
        Set<String> orderNos = StreamUtil.fetchSet(records, SessionTrackingLog::getOrderNo);

        List<Order> orders = orderService.getByOutTradeNos(new ArrayList<>(orderNos));

        // 批量获取订单映射
        Map<String, Order> orderMap = orderNos.isEmpty() ? Collections.emptyMap() : StreamUtil.map(orders, Order::getOutTradeNo);

        // 获取货币映射（缓存）
        Map<String, String> currencyMap = currencyService.mapAll();

        // 转换为VO并处理订单信息
        return records.stream()
                .map(record -> buildSessionTrackingVO(record, orderMap, currencyMap, timezone))
                .collect(Collectors.toList());
    }



    /**
     * 构建SessionTrackingVO对象
     */
    private SessionTrackingVO buildSessionTrackingVO(SessionTrackingLog record,
                                                     Map<String, Order> orderMap,
                                                     Map<String, String> currencyMap, String timezone) {
        SessionTrackingVO copy = BeanUtils.copy(record, SessionTrackingVO.class);
        if (StringUtils.hasText(timezone)) {
            // 转换创建时间
            if (Objects.nonNull(record.getCreateTime())) {
                String createTimeStr = DateUtils.formatWithTimezone(record.getCreateTime(), timezone);
                copy.setCreateTime(createTimeStr);
            }
        }
        if (!StringUtils.hasText(record.getOrderNo())) {
            return copy;
        }
        Order order = orderMap.get(record.getOrderNo());
        if (Objects.isNull(order)) {
            return copy;
        }
        copy.setPayStatus(order.getPaymentStatus());
        if (Objects.nonNull(order.getPaymentAmount()) && order.getPaymentAmount() != 0) {
            copy.setPayAmount(order.getCurrency() + currencyMap.get(order.getCurrency()) + String.format("%.2f", order.getPaymentAmount() / 100.0));
        }
        if (Objects.nonNull(order.getAmount()) && order.getAmount() != 0) {
            copy.setTotalAmount(order.getCurrency() + currencyMap.get(order.getCurrency()) + String.format("%.2f", order.getAmount() / 100.0));
        }
        return copy;
    }

    /**
     * 填充订单相关信息
     */
    private void populateOrderInfo(SessionTrackingVO vo, Order order, Map<String, String> currencyMap) {
        vo.setPayStatus(order.getPaymentStatus());

        // 设置支付金额
        Optional.ofNullable(order.getPaymentAmount())
                .filter(amount -> amount > 0)
                .ifPresent(amount -> vo.setPayAmount(formatAmount(order.getCurrency(), amount, currencyMap)));

        // 设置总金额
        Optional.ofNullable(order.getAmount())
                .filter(amount -> amount > 0)
                .ifPresent(amount -> vo.setTotalAmount(formatAmount(order.getCurrency(), amount, currencyMap)));
    }

    /**
     * 格式化金额显示
     */
    private String formatAmount(String currency, Integer amount, Map<String, String> currencyMap) {
        String symbol = currencyMap.getOrDefault(currency, "");
        return currency + symbol + String.format("%.2f", amount / 100.0);
    }

}
