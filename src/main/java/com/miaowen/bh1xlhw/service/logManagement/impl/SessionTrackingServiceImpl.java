package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.model.entity.SessionTrackingLog;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.logManagement.SessionTrackingForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SessionTrackingVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.logManagement.SessionTrackingService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会话追踪
 *
 * @Author：huanglong
 * @Date：2025/5/8 11:39
 */
@Slf4j
@Service
@AllArgsConstructor
public class SessionTrackingServiceImpl implements SessionTrackingService {

    private final SessionTrackingLogService sessionTrackingLogService;
    private final IOrderService orderService;
    private final EventLogService eventLogService;
    private final SystemEventService systemEventService;
    private final ICurrencyService currencyService;


    @Override
    public ResultVO<PageVO<SessionTrackingVO>> page(SessionTrackingForm form) {
        // 获取查询条件Wrapper
        try {
            LambdaQueryWrapper<SessionTrackingLog> wrapper = Wrappers.<SessionTrackingLog>lambdaQuery()
                    .like(StringUtils.hasText(form.getLogId()), SessionTrackingLog::getLogId, form.getLogId())
                    .eq(StringUtils.hasText(form.getEnvType()), SessionTrackingLog::getEnvType, form.getEnvType())
                    .like(StringUtils.hasText(form.getTraceId()), SessionTrackingLog::getTraceId, form.getTraceId())
                    .like(StringUtils.hasText(form.getTuid()), SessionTrackingLog::getTuid, form.getTuid())
                    .like(StringUtils.hasText(form.getOrderNo()), SessionTrackingLog::getOrderNo, form.getOrderNo())
                    .like(StringUtils.hasText(form.getSource()), SessionTrackingLog::getSource, form.getSource())
                    .eq(StringUtils.hasText(form.getPlatformCode()), SessionTrackingLog::getPlatformCode, form.getPlatformCode());
            LocalDateTime startTime;
            LocalDateTime endTime;
            if (form.getStartTime() != null && form.getEndTime() != null) {
                startTime = DateUtils.getEast8LocalDateTime(form.getTimezone(), form.getStartTime());
                endTime = DateUtils.getEast8LocalDateTime(form.getTimezone(), form.getEndTime());
            } else {
                //没有的话则需要放入时间范围 默认查当月
                startTime = LocalDate.now().withDayOfMonth(1).atStartOfDay();
                endTime = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()).atTime(LocalTime.MAX);
            }
            wrapper.between(SessionTrackingLog::getCreateTime, startTime, endTime);
            Page<SessionTrackingVO> page = sessionTrackingLogService.pageInfo(DateUtils.getDateString(startTime),
                wrapper);
            Page<SessionTrackingVO> page = new Page<>(form.getPageInt(), form.getPageSize());
            if (count == 0) {
                return ResultVO.success(new PageVO<>(page, SessionTrackingVO.class));
            }
            wrapper.orderByDesc(SessionTrackingLog::getCreateTime);
            // 构建分页查询的SQL语句
            int offset = (form.getPageInt() - 1) * form.getPageSize();
            int limit = form.getPageSize();
            // 添加LIMIT条件
            wrapper.last("LIMIT " + offset + "," + limit);
            List<SessionTrackingLog> list = sessionTrackingLogService.list(wrapper);
            // 执行分页查询
            page.setRecords(getVo(list));
            page.setTotal(count);
            return ResultVO.success(new PageVO<>(page, SessionTrackingVO.class));
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    private List<SessionTrackingVO> getVo(List<SessionTrackingLog> records) {

        List<SessionTrackingVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        List<String> orderNos = new ArrayList<>();
        Map<String, Order> orderMap;
        records.forEach(record -> {
            if (!StringUtils.isEmpty(record.getOrderNo())) {
                orderNos.add(record.getOrderNo());
            }
        });
        if (!CollectionUtils.isEmpty(orderNos)) {
            List<String> distinctOrderNos = orderNos.stream()
                    .distinct()
                    .collect(Collectors.toList());
            List<Order> orders = orderService.getByOutTradeNos(distinctOrderNos);
            if (!CollectionUtils.isEmpty(orders)) {
                orderMap = StreamUtil.map(orders, Order::getOutTradeNo, Function.identity());
            } else {
                orderMap = new HashMap<>();
            }
        } else {
            orderMap = new HashMap<>();
        }
        Map<String, String> currencyMap = currencyService.mapAll();
        records.forEach(record -> {
            SessionTrackingVO copy = BeanUtils.copy(record, SessionTrackingVO.class);
            if (!StringUtils.isEmpty(record.getOrderNo())) {
                Order order = orderMap.get(record.getOrderNo());
                if (Objects.nonNull(order)) {
                    copy.setPayStatus(order.getPaymentStatus());
                    if (Objects.nonNull(order.getPaymentAmount()) && order.getPaymentAmount() != 0) {
                        copy.setPayAmount(order.getCurrency() + currencyMap.get(order.getCurrency()) + String.format("%.2f", order.getPaymentAmount() / 100.0));
                    }
                    if (Objects.nonNull(order.getAmount()) && order.getAmount() != 0) {
                        copy.setTotalAmount(order.getCurrency() + currencyMap.get(order.getCurrency()) + String.format("%.2f", order.getAmount() / 100.0));
                    }
                }
            }
            result.add(copy);
        });
        return result;
    }

}
