package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.model.entity.SessionTrackingLog;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.logManagement.SessionTrackingForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SessionTrackingVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.logManagement.SessionTrackingService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会话追踪
 *
 * @Author：huanglong
 * @Date：2025/5/8 11:39
 */
@Slf4j
@Service
@AllArgsConstructor
public class SessionTrackingServiceImpl implements SessionTrackingService {

    private final SessionTrackingLogService sessionTrackingLogService;
    private final IOrderService orderService;
    private final EventLogService eventLogService;
    private final SystemEventService systemEventService;
    private final ICurrencyService currencyService;


    @Override
    public ResultVO<PageVO<SessionTrackingVO>> page(SessionTrackingForm form) {
        // 获取查询条件Wrapper
        try {

            QueryWrapper<SessionTrackingLog> wrapper = new QueryWrapper<>();
            if (!StringUtils.isEmpty(form.getLogId())) {
                wrapper.like("log_id", form.getLogId());
            }
            if (!StringUtils.isEmpty(form.getEnvType())) {
                wrapper.eq("env_type", form.getEnvType());
            }
            if (!StringUtils.isEmpty(form.getTraceId())) {
                wrapper.like("trace_id", form.getTraceId());
            }
            if (!StringUtils.isEmpty(form.getTuid())) {
                wrapper.like("tuid", form.getTuid());
            }
            if (!StringUtils.isEmpty(form.getOrderNo())) {
                wrapper.like("order_no", form.getOrderNo());
            }
            if (!StringUtils.isEmpty(form.getSource())) {
                wrapper.like("source", form.getSource());
            }
            if (!StringUtils.isEmpty(form.getPlatformCode())) {
                wrapper.eq("platform_code", form.getPlatformCode());
            }
            if (form.getStartTime() != null && form.getEndTime() != null) {
                Date startTime = DateUtils.getData(form.getStartTime());
                Date endTime = DateUtils.getData(form.getEndTime());

                Date east8StartDate = DateUtils.getEast8Date(form.getTimezone(), startTime);
                Date east8EndDate = DateUtils.getEast8Date(form.getTimezone(), endTime);

                MonthShardingTableNameHandler.setParams(east8StartDate);
                if (!DateUtils.isSameMonth(east8StartDate, east8EndDate)) {
                    //跨月 则取开始时间当月
                    wrapper.between("create_time", east8StartDate, DateUtils.getLastDayOfMonth(east8StartDate));
                } else {
                    wrapper.between("create_time", east8StartDate, east8EndDate);
                }
            } else {
                //没有的话则需要放入时间范围 默认查当月
                wrapper.between("create_time", DateUtils.getFirstDayOfMonth(), new Date());
            }
            long count = sessionTrackingLogService.count(wrapper);
            Page<SessionTrackingVO> page = new Page<>(form.getPageInt(), form.getPageSize());
            if (count == 0) {
                return ResultVO.success(new PageVO<>(page, SessionTrackingVO.class));
            }
            wrapper.orderByDesc("create_time");
            // 构建分页查询的SQL语句
            int offset = (form.getPageInt() - 1) * form.getPageSize();
            int limit = form.getPageSize();
            // 添加LIMIT条件
            wrapper.last("LIMIT " + offset + "," + limit);
            List<SessionTrackingLog> list = sessionTrackingLogService.list(wrapper);
            // 执行分页查询
            page.setRecords(getVo(list));
            page.setTotal(count);
            return ResultVO.success(new PageVO<>(page, SessionTrackingVO.class));
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    private List<SessionTrackingVO> getVo(List<SessionTrackingLog> records) {

        List<SessionTrackingVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        List<String> orderNos = new ArrayList<>();
        Map<String, Order> orderMap;
        records.forEach(record -> {
            if (!StringUtils.isEmpty(record.getOrderNo())) {
                orderNos.add(record.getOrderNo());
            }
        });
        if (!CollectionUtils.isEmpty(orderNos)) {
            List<String> distinctOrderNos = orderNos.stream()
                    .distinct()
                    .collect(Collectors.toList());
            List<Order> orders = orderService.getByOutTradeNos(distinctOrderNos);
            if (!CollectionUtils.isEmpty(orders)) {
                orderMap = StreamUtil.map(orders, Order::getOutTradeNo, Function.identity());
            } else {
                orderMap = new HashMap<>();
            }
        } else {
            orderMap = new HashMap<>();
        }
        Map<String, String> currencyMap = currencyService.mapAll();
        records.forEach(record -> {
            SessionTrackingVO copy = BeanUtils.copy(record, SessionTrackingVO.class);
            if (!StringUtils.isEmpty(record.getOrderNo())) {
                Order order = orderMap.get(record.getOrderNo());
                if (Objects.nonNull(order)) {
                    copy.setPayStatus(order.getPaymentStatus());
                    if (Objects.nonNull(order.getPaymentAmount()) && order.getPaymentAmount() != 0) {
                        copy.setPayAmount(order.getCurrency() + currencyMap.get(order.getCurrency()) + String.format("%.2f", order.getPaymentAmount() / 100.0));
                    }
                    if (Objects.nonNull(order.getAmount()) && order.getAmount() != 0) {
                        copy.setTotalAmount(order.getCurrency() + currencyMap.get(order.getCurrency()) + String.format("%.2f", order.getAmount() / 100.0));
                    }
                }
            }
            result.add(copy);
        });
        return result;
    }

}
