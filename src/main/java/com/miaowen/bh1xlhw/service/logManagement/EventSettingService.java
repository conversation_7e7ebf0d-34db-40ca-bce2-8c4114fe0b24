package com.miaowen.bh1xlhw.service.logManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.logManagement.EventSettingForm;
import com.miaowen.bh1xlhw.model.query.logManagement.EventSettingPageForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SystemEventVo;

import java.util.List;

/**
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/7 10:57
 */
public interface EventSettingService {


    // 根据分页表单查询系统事件分页数据
    PageVO<SystemEventVo> page(EventSettingPageForm pageForm);

    // 删除指定id的记录
    void delete(List<Long> ids);

    // 添加事件设置表单
    ResultVO<Void> add(EventSettingForm settingForm);

    ResultVO<Void> update(EventSettingForm settingForm);

}
