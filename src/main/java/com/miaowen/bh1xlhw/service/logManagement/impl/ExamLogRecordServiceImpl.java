package com.miaowen.bh1xlhw.service.logManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.logManagement.ExamLogForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.ExamLogVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.service.logManagement.ExamLogRecordService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;

/**
 * 做题日志
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 10:37
 */
@Slf4j
@Service
@AllArgsConstructor
public class ExamLogRecordServiceImpl implements ExamLogRecordService {

    private final ExamLogService examLogService;
    private final GoodsService goodsService;
    private final IOrderService orderService;
    private final IGoodsMultilingualService goodsMultilingualService;
    private final IGoodsTraditionalService goodsTraditionalService;
    private final ICurrencyService currencyService;

    @Override
    public ResultVO<PageVO<ExamLogVO>> page(ExamLogForm pageForm) {

        try {
            LambdaQueryWrapper<ExamLog> wrapper = Wrappers.lambdaQuery();
            if (pageForm.getTuid() != null) {
                wrapper.eq(ExamLog::getTuid, pageForm.getTuid());
            }
            if (pageForm.getOrderNo() != null) {
                wrapper.eq(ExamLog::getOrderNo, pageForm.getOrderNo());
            }
            if (pageForm.getExamId() != null) {
                wrapper.eq(ExamLog::getExamId, pageForm.getExamId());
            }
            if (pageForm.getLogId() != null) {
                wrapper.eq(ExamLog::getLogId, pageForm.getLogId());
            }
            if (pageForm.getPlatformCode() != null) {
                wrapper.eq(ExamLog::getPlatformCode, pageForm.getPlatformCode());
            }
            if (pageForm.getGoodsId() != null && pageForm.getGoodsType() != null) {
                List<Integer> idsByGoodsName = getGoodsIdByGoodsName(pageForm);
                if (CollectionUtils.isEmpty(idsByGoodsName)) {
                    return ResultVO.success(new PageVO<>());
                }
                wrapper.in(ExamLog::getProductId, idsByGoodsName);
                wrapper.eq(ExamLog::getGoodsType, pageForm.getGoodsType());
            } else if (pageForm.getGoodsType() != null) {
                wrapper.eq(ExamLog::getGoodsType, pageForm.getGoodsType());
            }

            if (pageForm.getStartTime() != null && pageForm.getEndTime() != null) {

                MonthShardingTableNameHandler.setParams(DateUtils.getData(pageForm.getStartTime()));
                if (!DateUtils.isSameMonth(DateUtils.getData(pageForm.getStartTime()), DateUtils.getData(pageForm.getEndTime()))) {
                    //跨月 则取开始时间当月
                    wrapper.between(ExamLog::getCreateTime, pageForm.getStartTime(), DateUtils.getLastDayOfMonth(DateUtils.getData(pageForm.getStartTime())));
                } else {
                    wrapper.between(ExamLog::getCreateTime, pageForm.getStartTime(), pageForm.getEndTime());
                }
            } else {
                //没有的话则需要放入时间范围 默认查当月
                wrapper.between(ExamLog::getCreateTime, DateUtils.getFirstDayOfMonth(), new Date());
            }
            long count = examLogService.count(wrapper);
            if (count == 0) {
                return ResultVO.success(new PageVO<>(new Page<>(), ExamLogVO.class));
            }
            wrapper.orderByDesc(ExamLog::getCreateTime);
            // 构建分页查询的SQL语句
            int offset = (pageForm.getPageInt() - 1) * pageForm.getPageSize();
            int limit = pageForm.getPageSize();
            // 添加LIMIT条件
            wrapper.last("LIMIT " + offset + "," + limit);
            List<ExamLog> list = examLogService.list(wrapper);
            bulidList(list);
            Page<ExamLog> page = new Page<>(pageForm.getPageInt(), pageForm.getPageSize());
            page.setRecords(list);
            page.setTotal(count);
            return ResultVO.success(new PageVO<>(page, ExamLogVO.class));
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }
    /**
     * 获取符合条件的商品id
     */
    private List<Integer> getGoodsIdByGoodsName(ExamLogForm pageForm) {
        List<Integer> goodsIdList = new ArrayList<>();
        if (Objects.equals(pageForm.getGoodsType(), GoodsTypeEnum.MULTILINGUAL.getValue())) {
            QueryWrapper<GoodsMultilingual> multilingualQueryWrapper = new QueryWrapper<>();
            if (Objects.nonNull(pageForm.getGoodsId())) {
                multilingualQueryWrapper.eq("id", pageForm.getGoodsId());
            }
            List<GoodsMultilingual> multilingualList = goodsMultilingualService.list(multilingualQueryWrapper);
            if (!CollectionUtils.isEmpty(multilingualList)){
                multilingualList.forEach(goodsMultilingual -> goodsIdList.add(goodsMultilingual.getId()));
            }
        }
        if (Objects.equals(pageForm.getGoodsType(), GoodsTypeEnum.TRADITIONAL.getValue())) {
            QueryWrapper<GoodsTraditional> traditionalQueryWrapper = new QueryWrapper<>();
            if (Objects.nonNull(pageForm.getGoodsId())) {
                traditionalQueryWrapper.eq("id", pageForm.getGoodsId());
            }
            List<GoodsTraditional> traditions = goodsTraditionalService.list(traditionalQueryWrapper);
            if (!CollectionUtils.isEmpty(traditions)){
                traditions.forEach(goodsTraditional -> goodsIdList.add(goodsTraditional.getId()));
            }
        }
        return goodsIdList;
    }

    private void bulidList(List<ExamLog> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        List<String> orderNos = new ArrayList<>();
        List<Integer> multiGoodsId = new ArrayList<>();
        List<Integer> traditionalGoodsId = new ArrayList<>();

        records.forEach(record -> {
            if (!StringUtils.isEmpty(record.getOrderNo())) {
                orderNos.add(record.getOrderNo());
            }
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(record.getGoodsType())) {
                multiGoodsId.add(record.getProductId());
            } else {
                traditionalGoodsId.add(record.getProductId());
            }

        });
        List<Order> orders = new ArrayList<>();
        Map<String, Order> orderMap;
        if (!CollectionUtils.isEmpty(orderNos)) {
            LambdaQueryWrapper<Order> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(Order::getOutTradeNo, orderNos);
            orders = orderService.list(wrapper);
            if (!CollectionUtils.isEmpty(orders)) {
                orderMap = StreamUtil.map(orders, Order::getOutTradeNo, Function.identity());
            } else {
                orderMap = new HashMap<>();
            }
        } else {
            orderMap = new HashMap<>();
        }
        Map<String, String> currencyMap = currencyService.mapAll();
        Map<Integer, GoodsMultilingual> integerGoodsMultilingualMap = goodsMultilingualService.mapNameByIds(multiGoodsId);
        Map<Integer, GoodsTraditional> integerGoodsTraditionalMap = goodsTraditionalService.mapById(traditionalGoodsId);


        records.forEach(record -> {
            if (!StringUtils.isEmpty(record.getOrderNo())) {
                Order order = orderMap.get(record.getOrderNo());
                if (Objects.nonNull(order)) {
                    record.setPayStatus(order.getPaymentStatus());
                    if (Objects.nonNull(order.getPaymentAmount()) && order.getPaymentAmount() != 0) {
                        record.setPayAmount(order.getCurrency()
                                + currencyMap.get(order.getCurrency())
                                + String.format("%.2f", order.getPaymentAmount() / 100.0));
                    }
                    if (Objects.nonNull(order.getAmount()) && order.getAmount() != 0) {
                        record.setTotalAmount(order.getCurrency()
                                + currencyMap.get(order.getCurrency())
                                + String.format("%.2f", order.getAmount() / 100.0));
                    }
                }
            }
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(record.getGoodsType())){
                GoodsMultilingual goodsMultilingual = integerGoodsMultilingualMap.get(record.getProductId());
                if (Objects.nonNull(goodsMultilingual))
                    record.setProductName(goodsMultilingual.getName());
            }else {
                GoodsTraditional goodsTraditional = integerGoodsTraditionalMap.get(record.getProductId());
                if (Objects.nonNull(goodsTraditional))
                    record.setProductName(goodsTraditional.getName());
            }

        });

    }

}

