package com.miaowen.bh1xlhw.service.oa;

import com.miaowen.bh1xlhw.model.query.oa.StatisticOrderForm;
import com.miaowen.bh1xlhw.model.vo.oa.*;

import java.util.List;

/**
 * OaStatisticDataService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
public interface OaStatisticDataService {

    /**
     * 根据员工编号平台账号信息查询
     */
    PlatformAccountListVO platformAccounts(String workNo);
    /**
     * 退款数据查询
     * @param time 月份 2025-02
     */
    StatisticRefundListVO statisticRefund(String time);

    /**
     * 代理商返点数据查询
     * @param agentId 代理商id
     */
    RebateVO rebate(String agentIds);


    StatisticOrderListVO statisticOrder(StatisticOrderForm statisticOrderForm);

    PlatformListVO getAllPlatforms();

    AgentListVO getAllAgents();
}
