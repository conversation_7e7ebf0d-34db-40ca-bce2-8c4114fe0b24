package com.miaowen.bh1xlhw.service.oa.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.OrderTimeEnum;
import com.miaowen.bh1xlhw.model.bo.goods.PromotionGoodsInfoBo;
import com.miaowen.bh1xlhw.model.bo.oa.StatisticOrderBO;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.oa.StatisticOrderForm;
import com.miaowen.bh1xlhw.model.vo.goods.AdvertiseAccountVO;
import com.miaowen.bh1xlhw.model.vo.oa.*;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.good.AdvertiseAccountInfoService;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.service.oa.OaStatisticDataService;
import com.miaowen.bh1xlhw.service.operation.OperationService;
import com.miaowen.bh1xlhw.service.order.RefundService;
import com.miaowen.bh1xlhw.utils.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * OaStatisticDataServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@Slf4j
@Service
@AllArgsConstructor
public class OaStatisticDataServiceImpl implements OaStatisticDataService {
    private final AdvertiseAccountInfoService advertiseAccountInfoService;
    private final PlatformService platformService;
    private final GoodsService goodsService;
    private final IOrderService orderService;
    private final OperationUserService operationUserService;
    private final OperationService operationService;

    private final IGoodsMultilingualService goodsMultilingualService;
    private final IGoodsTraditionalService goodsTraditionalService;
    private final RefundService refundService;
    private final GoodsPromotionMultilingualService goodsPromotionMultilingualService;
    private final GoodsPromotionTraditionalService goodsPromotionTraditionalService;

    @Override
    public PlatformAccountListVO platformAccounts(String workNo, int reportType) {
        List<AdvertiseAccountVO> advertiseAccountVos;

            if (StringUtils.hasText(workNo)) {
                //reportType = 1时，查询运营日报
                if (reportType == 1) {
                    log.info("查询运营人员对应的数据");
                    OperationUser operationUser = operationUserService.getByUserCode(workNo);
                    if (Objects.nonNull(operationUser)) {
                        advertiseAccountVos = advertiseAccountInfoService.getListByOperationUser(workNo);
                        log.info("运营人员useCode对应的广告账户是{}", advertiseAccountVos);
                    } else {
                        log.error("userCode {}查询不到对应的运营人员", workNo);
                        return null;
                    }
                }else {

                    //reportType = 2时，查询主管日报
                    log.info("查询运营主管对应的数据");
                    OperationUser operationManager = operationUserService.getByUserCode(workNo);
                    if (Objects.nonNull(operationManager)){
                        advertiseAccountVos = advertiseAccountInfoService.getListByOperationManager(workNo);
                        log.info("运营主管useCode对应的广告账户是{}", advertiseAccountVos);
                    }else {
                        log.error("userCode {}查询不到对应的运营主管", workNo);
                        return null;
                    }
                }
            } else {
                advertiseAccountVos = advertiseAccountInfoService.allAdvertiseAccount();
            }




        PlatformAccountListVO platformAccountListVO = new PlatformAccountListVO();
        if (CollectionUtils.isEmpty(advertiseAccountVos)) {
            return platformAccountListVO;
        }
        List<PlatformAccountVO> platformAccounts = advertiseAccountVos.stream().map(advertiseAccountVO -> {
            PlatformAccountVO platformAccountVo = new PlatformAccountVO();
            platformAccountVo.setPlatformType(advertiseAccountVO.getPlatformType());
            platformAccountVo.setName(advertiseAccountVO.getName());
            PlatformVO platformVO = advertiseAccountVO.getPlatformVO();
            if (Objects.nonNull(platformVO)) {
                platformAccountVo.setAgentId(platformVO.getId());
                platformAccountVo.setOperationType(platformVO.getOperationType());
            }
            PriceVO price = advertiseAccountVO.getPrice();
            if (Objects.nonNull(price)) {
                platformAccountVo.setTargetRoi(price.getTargetRoi());
                platformAccountVo.setBaseRoi(price.getBaseRoi());
            }
            platformAccountVo.setName(advertiseAccountVO.getName());
            return platformAccountVo;
        }).collect(Collectors.toList());
        platformAccountListVO.setPlatformAccounts(platformAccounts);
        return platformAccountListVO;
    }

    @Override
    public StatisticRefundListVO statisticRefund(String time) {
        YearMonth yearMonth = YearMonth.parse(time, DateUtils.MONTH_FORMAT);
        List<StatisticRefundVO> statisticRefundVos = refundService.refundStatisticByMonth(yearMonth);
        StatisticRefundListVO statisticRefundListVo = new StatisticRefundListVO();
        statisticRefundListVo.setStatisticRefunds(statisticRefundVos);
        return statisticRefundListVo;
    }

    @Override
    public RebateVO rebate(String agentId) {
        List<String> agentList = CommonConstant.stringToList(agentId);
        List<Integer> agentIds = agentList.stream().map(Integer::parseInt).collect(Collectors.toList());
        List<Platform> platformList = platformService.listExistByIds(agentIds);
        RebateVO rebateVO = new RebateVO();
        if (Objects.isNull(platformList)) {
            return rebateVO;
        }
        Map<Integer, BigDecimal> map = StreamUtil.map(platformList, Platform::getId, Platform::getRebate);
        rebateVO.setRebateMap(map);
        return rebateVO;
    }

    @Override
    public StatisticOrderListVO statisticOrder(StatisticOrderForm statisticOrderForm) {
        StatisticOrderListVO statisticOrderListVO = new StatisticOrderListVO();
        LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class);
        wrapper.ne(Order::getTgId, "");
        if (Objects.nonNull(statisticOrderForm.getTimeType()) && statisticOrderForm.getTimeType().equals(OrderTimeEnum.CREATE_TIME.getType())) {
            wrapper.ge(Order::getCreateDate, LocalDate.parse(statisticOrderForm.getStartDate(), DateUtils.DATE_FORMAT));
            wrapper.le(Order::getCreateDate, LocalDate.parse(statisticOrderForm.getEndDate(), DateUtils.DATE_FORMAT));
        } else {
            wrapper.ge(Order::getPaymentDate, LocalDate.parse(statisticOrderForm.getStartDate(), DateUtils.DATE_FORMAT));
            wrapper.le(Order::getPaymentDate, LocalDate.parse(statisticOrderForm.getEndDate(), DateUtils.DATE_FORMAT));
        }

        //处理商品相关的三个查询条件
        Integer productType = statisticOrderForm.getProductType();
        if (Objects.nonNull(productType)) {
            wrapper.eq(Order::getGoodsType, productType);
            if (StringUtils.hasText(statisticOrderForm.getProductName()) || Objects.nonNull(statisticOrderForm.getProductId())) {
                List<Integer> goodsIdList = goodsService.getIdsByGoodsName(statisticOrderForm.getProductName(), statisticOrderForm.getProductType(), statisticOrderForm.getProductId());
                if (CollectionUtils.isEmpty(goodsIdList)) {
                    return statisticOrderListVO;
                }
                wrapper.eq(Order::getGoodsId, goodsIdList);
            }
        }

        //处理管理人员
        String operationManagerName = statisticOrderForm.getOperationManagerName();
        if (StringUtils.hasText(operationManagerName) || StringUtils.hasText(statisticOrderForm.getOperationManagerUserCode())) {
            List<Integer> managerUserIds = operationUserService.getManagerIdsByName(operationManagerName,
                statisticOrderForm.getOperationManagerUserCode());
            if (CollectionUtils.isEmpty(managerUserIds)) {
                return statisticOrderListVO;
            }
            List<OperationUser> operationUsers = operationUserService.listByPid(managerUserIds);
            if (CollectionUtils.isEmpty(operationUsers)) {
                return statisticOrderListVO;
            }
            Set<Integer> operationIds = StreamUtil.fetchSet(operationUsers, OperationUser::getId);
            wrapper.in(Order::getOperationUserId, operationIds);
        }

        //处理运营人员
        String operationName = statisticOrderForm.getOperationName();
        if (StringUtils.hasText(operationName) || StringUtils.hasText(statisticOrderForm.getOperationUserCode())) {
            List<OperationUser> operationUser = operationUserService.getIdsByName(operationName, statisticOrderForm.getOperationUserCode());
            if (CollectionUtils.isEmpty(operationUser)) {
                return statisticOrderListVO;
            }
            Set<Integer> operationUserIds = StreamUtil.fetchSet(operationUser, OperationUser::getId);
            wrapper.in(Order::getOperationUserId, operationUserIds);
        }

        if (StringUtils.hasText(statisticOrderForm.getPlatform())) {
            wrapper.in(Order::getPlatformType, statisticOrderForm.getPlatform());
        }
        List<StatisticOrderBO> statisticOrderBos = orderService.statisticOrder(wrapper);
        if (CollectionUtils.isEmpty(statisticOrderBos)) {
            return statisticOrderListVO;
        }
        //封装数据
        List<String> multilingualTiIds = new ArrayList<>();
        List<String> traditionalTiIds = new ArrayList<>();

        statisticOrderBos.forEach(statisticOrderBO -> {
            Integer goodsType = statisticOrderBO.getGoodsType();
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
                multilingualTiIds.add(statisticOrderBO.getTgId());

            } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)) {
                traditionalTiIds.add(statisticOrderBO.getTgId());
            }
        });
        List<GoodsPromotionMultilingual> goodsPromotionMultilingualList = goodsPromotionMultilingualService.listTgId(multilingualTiIds);
        List<GoodsPromotionTraditional> goodsPromotionTraditionalList = goodsPromotionTraditionalService.listTgId(traditionalTiIds);


        Map<String, GoodsPromotionMultilingual> goodsPromotionMultilingualMap = StreamUtil.map(goodsPromotionMultilingualList,
            GoodsPromotionMultilingual::getTgId, Function.identity());

        Map<String, GoodsPromotionTraditional> goodsPromotionTraditionalMap = StreamUtil.map(goodsPromotionTraditionalList, GoodsPromotionTraditional::getTgId, Function.identity());
        List<Integer> goodsMultilingualIds = new ArrayList<>();
        List<Integer> goodsTraditionalIds = new ArrayList<>();
        List<Integer> operationIds = new ArrayList<>();
        statisticOrderBos.forEach(statisticOrderBO -> {
            PromotionGoodsInfoBo promotionGoodsInfoBo = getPromotionGoodsInfoBo(goodsPromotionMultilingualMap, goodsPromotionTraditionalMap, statisticOrderBO);
            if (Objects.isNull(promotionGoodsInfoBo)){
                return;
            }
            Integer goodsId = promotionGoodsInfoBo.getGoodsId();
            Integer goodsType = statisticOrderBO.getGoodsType();
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
                goodsMultilingualIds.add(goodsId);
            } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)) {
                goodsTraditionalIds.add(goodsId);
            }
            operationIds.add(promotionGoodsInfoBo.getOperationId());
        });

        log.info("operationIds : {}", operationIds);

        Map<Integer, GoodsMultilingual> goodsMultilingualMap = goodsMultilingualService.mapById(goodsMultilingualIds);
        Map<Integer, GoodsTraditional> goodsTraditionalMap = goodsTraditionalService.mapById(goodsTraditionalIds);
        Map<Integer, OperationUser> operationUserMap = operationUserService.mapNameByIds(operationIds);
        Collection<OperationUser> values = operationUserMap.values();
        log.info("values : {}", values);
        Map<Integer, OperationUser> operationManagerMap;
        //如果运营人员能够查询出来 才能够进行下一步 查询运营人员对应的上级领导
        if (!CollectionUtils.isEmpty(values)) {
            log.info("查询运营人员对应的运营主管");
            Set<Integer> pUserIds = StreamUtil.fetchSet(values, OperationUser::getPid);
            operationManagerMap = operationUserService.mapNameByIds(pUserIds);
        } else {
            operationManagerMap = new HashMap<>();
        }
        List<StatisticOrderVO> statisticOrderList = statisticOrderBos.stream().map(statisticOrderBO -> {
            Integer goodsType = statisticOrderBO.getGoodsType();
            PromotionGoodsInfoBo promotionGoodsInfoBo = getPromotionGoodsInfoBo(goodsPromotionMultilingualMap, goodsPromotionTraditionalMap, statisticOrderBO);
            if (Objects.isNull(promotionGoodsInfoBo)) {
                return null;
            }
            StatisticOrderVO statisticOrderVo = BeanUtils.copy(statisticOrderBO, StatisticOrderVO.class);
            statisticOrderVo.setGoodsId(promotionGoodsInfoBo.getGoodsId());
            statisticOrderVo.setProductType(statisticOrderBO.getGoodsType());
            statisticOrderVo.setPlatform(promotionGoodsInfoBo.getPlatformType());
            if (goodsType.equals(GoodsTypeEnum.MULTILINGUAL.getValue())) {
                GoodsMultilingual goodsMultilingual = goodsMultilingualMap.get(promotionGoodsInfoBo.getGoodsId());
                if (Objects.nonNull(goodsMultilingual)) {
                    statisticOrderVo.setProductName(goodsMultilingual.getName());
                }
            } else if (goodsType.equals(GoodsTypeEnum.TRADITIONAL.getValue())) {
                GoodsTraditional goodsTraditional = goodsTraditionalMap.get(promotionGoodsInfoBo.getGoodsId());
                if (Objects.nonNull(goodsTraditional)) {
                    statisticOrderVo.setProductName(goodsTraditional.getName());
                }
            }
            OperationUser operationUser = operationUserMap.get(promotionGoodsInfoBo.getOperationId());
            if (Objects.nonNull(operationUser)) {
                statisticOrderVo.setOperationUserCode(operationUser.getWorkNo());
                statisticOrderVo.setOperationName(operationUser.getName());
                Integer pid = operationUser.getPid();
                if (pid != null && pid > 0){
                    OperationUser  operationManager = operationManagerMap.get(pid);
                    if (Objects.nonNull(operationManager)) {
                        statisticOrderVo.setOperationManagerUserCode(operationManager.getWorkNo());
                        statisticOrderVo.setOperationManagerName(operationManager.getName());
                    }else {
                        log.warn("找不到运营主管: pid={}, operationUser={}", pid, operationUser.getId());
                    }
                } else {
                    log.warn("无效的上级主管ID: pid={}, operationUser={}", pid, operationUser.getId());
                }
            }else {
                log.warn("找不到运营人员: operationId={}", promotionGoodsInfoBo.getOperationId());
            }
            statisticOrderVo.setTotalPayAmount(DecimalUtil.fromStoredValue(statisticOrderBO.getTotalPayAmount()));
            statisticOrderVo.setRedPackage1Amount(DecimalUtil.fromStoredValue(statisticOrderBO.getRedPackage1Amount()));
            statisticOrderVo.setRedPackage2Amount(DecimalUtil.fromStoredValue(statisticOrderBO.getRedPackage2Amount()));
            statisticOrderVo.setOriginalPriceAmount(DecimalUtil.fromStoredValue(statisticOrderBO.getOriginalPriceAmount()));
            statisticOrderVo.setOrderRate(DecimalUtil.getRate(statisticOrderBO.getPaidOrderNum(), statisticOrderBO.getTotalOrderNum()));
            statisticOrderVo.setOriginPriceRate(DecimalUtil.getRate(statisticOrderBO.getOriginalPriceOrderNum(), statisticOrderBO.getTotalOrderNum()));
            statisticOrderVo.setRedPackageRate(DecimalUtil.getRate(statisticOrderBO.getRedPackage1OrderNum() + statisticOrderBO.getRedPackage2OrderNum(), statisticOrderBO.getTotalOrderNum()));
            return statisticOrderVo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        statisticOrderListVO.setStatisticOrderList(statisticOrderList);
        return statisticOrderListVO;
    }


    @Override
    public AgentListVO getAllAgent() {
        AgentListVO vo = new AgentListVO();
        List<AdvertiseAccountVO> advertiseAccounts = new ArrayList<>();
        List<PlatformVO> platformVos = operationService.allPlatform();
        if (!CollectionUtils.isEmpty(platformVos)) {
            advertiseAccounts = advertiseAccountInfoService.allAdvertiseAccount();
        }

        if (CollectionUtils.isEmpty(platformVos)) {
            vo.setAgentVOList(new ArrayList<>());
        }else {
            List<AgentVO> vos = BeanUtils.copyList(platformVos, AgentVO.class);
            vo.setAgentVOList(vos);
        }

        for (AgentVO agentVO : vo.getAgentVOList()) {
            List<AgentVO.OperationUserDTO> operationUserDTOList = new ArrayList<>();
            if (Objects.equals(agentVO.getRebateSymbol(), 0)){
                agentVO.setRebate(agentVO.getRebate().multiply(new BigDecimal("-1")));
            }
            List<AdvertiseAccountVO> advertiseAccountOfAgent = advertiseAccounts.stream()
                    .filter(t->Objects.equals(t.getPlatformVO().getId(), agentVO.getId()))
                    .collect(Collectors.toList());
            for (AdvertiseAccountVO advertiseAccount : advertiseAccountOfAgent) {
                AgentVO.OperationUserDTO dto = new AgentVO.OperationUserDTO();
                dto.setId(advertiseAccount.getOperationUser().getId());
                dto.setUserCode(advertiseAccount.getOperationUser().getWorkNo());
                dto.setUsername(advertiseAccount.getOperationUser().getName());
                operationUserDTOList.add(dto);
            }

            List<AgentVO.OperationUserDTO> deduplicateList = AgentVO.OperationUserDTO.deduplicate(operationUserDTOList);
            agentVO.setOperationUserList(deduplicateList);
        }
        return vo;
    }

    private PromotionGoodsInfoBo getPromotionGoodsInfoBo(Map<String, GoodsPromotionMultilingual> goodsPromotionMultilingualMap, Map<String, GoodsPromotionTraditional> goodsPromotionTraditionalMap, StatisticOrderBO statisticOrderBO) {
        if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(statisticOrderBO.getGoodsType())) {
            GoodsPromotionMultilingual goodsPromotionMultilingual = goodsPromotionMultilingualMap.get(statisticOrderBO.getTgId());
            return BeanUtils.copy(goodsPromotionMultilingual, PromotionGoodsInfoBo.class);
        } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(statisticOrderBO.getGoodsType())) {
            GoodsPromotionTraditional goodsPromotionTraditional = goodsPromotionTraditionalMap.get(statisticOrderBO.getTgId());
            return BeanUtils.copy(goodsPromotionTraditional, PromotionGoodsInfoBo.class);
        }
        return null;
    }


}
