package com.miaowen.bh1xlhw.service.country.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.Country;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.country.CountryForm;
import com.miaowen.bh1xlhw.model.query.country.CountryPageForm;
import com.miaowen.bh1xlhw.model.vo.country.CountryPageVO;
import com.miaowen.bh1xlhw.repository.ICountryService;
import com.miaowen.bh1xlhw.service.country.CountryService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.*;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
public class CountryServiceImpl implements CountryService {


    @Resource
    private ICountryService countryService;

    @Override
    public PageVO<CountryPageVO> pageCountry(CountryPageForm form) {
        // 1. 构建分页查询条件
        Page<Country> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<Country> wrapper = new QueryWrapper<Country>().eq(DELETE_TIME, 0); // 过滤未删除的数据
        wrapper.eq(DELETE_TIME, 0);
        // 根据CountryPageForm的参数动态添加查询条件
        if (form.getId() != null) {
            wrapper.eq("id", form.getId());
        }
        if (StringUtils.isNotBlank(form.getName())) {
            wrapper.like("name", form.getName());
        }
        if (StringUtils.isNotBlank(form.getCode())) {
            wrapper.eq("code", form.getCode());
        }
        if (StringUtils.isNotBlank(form.getContinentCode())) {
            wrapper.eq("continent_code", form.getContinentCode());
        }
        if (form.getSort() != null) {
            wrapper.eq("sort", form.getSort());
        }
        if (form.getStatus() != null) {
            wrapper.eq("status", form.getStatus());
        }
        if (form.getCreateTime() != null) {
            wrapper.eq("create_time", form.getCreateTime());
        }
        wrapper.orderByAsc("sort");
        wrapper.orderByDesc("create_time");

        // 2. 执行分页查询
        Page<Country> result = countryService.page(page, wrapper);

        // 3. 构建分页响应
        return new PageVO<>(result, CountryPageVO.class);
    }

    @Override
    public ResultVO<Void> saveOrUpdateCountry(CountryForm CountryForm) {
        QueryWrapper<Country> wrapper = new QueryWrapper<Country>().eq(DELETE_TIME, 0);
        if (CountryForm.getId() != null) {
            wrapper.ne(ID, CountryForm.getId());
        }
        wrapper.eq(CODE, CountryForm.getCode());
        if (countryService.count(wrapper) > 0) {
            return ResultVO.fail(ResultEnum.PARAM_ERROR,"国家代码已存在");
        }
        // 1. 转换Form为Entity
        Country entity = new Country();
        entity = BeanUtils.copy(CountryForm, entity.getClass());

        // 2. 设置时间（如果自动填充未配置）
        if (entity.getId() == null) {
            entity.setCreateTime(LocalDateTime.now());
        }
        entity.setUpdateTime(LocalDateTime.now());

        // 3. 保存或更新
        countryService.saveOrUpdate(entity);
        return ResultVO.success();
    }

    @Override
    public void updateState(UpdateStateForm form) {
        // 1. 构建更新条件
        UpdateWrapper<Country> wrapper = new UpdateWrapper<Country>().eq(ID, form.getId()).set(STATUS, form.getStatus());

        // 2. 执行更新
        countryService.update(wrapper);
    }

    @Override
    public List<CountryPageVO> listAll() {
        // 1. 查询未删除数据 且未禁用的数据
        QueryWrapper<Country> wrapper = new QueryWrapper<Country>().eq(DELETE_TIME, 0).eq(STATUS, 1);

        // 2. 转换结果
        return countryService.list(wrapper).stream().map(this::convertToPageVO).collect(Collectors.toList());
    }

    @Override
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<Country> wrapper = new UpdateWrapper<Country>().in(ID, deleteBatchForm.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000); // 秒级时间戳

        // 2. 执行更新
        countryService.update(wrapper);
    }

    /**
     * 实体转VO方法
     */
    private CountryPageVO convertToPageVO(Country entity) {
        if (entity == null) {
            return null;
        }

        CountryPageVO vo =  BeanUtils.copy(entity, CountryPageVO.class);
        return vo;
    }
}




