package com.miaowen.bh1xlhw.service.country;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.country.CountryForm;
import com.miaowen.bh1xlhw.model.query.country.CountryPageForm;
import com.miaowen.bh1xlhw.model.vo.country.CountryPageVO;

import java.util.List;

/**
 * @ClassName PoCountryService
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 11:26
 */
public interface CountryService {

    PageVO<CountryPageVO> pageCountry(CountryPageForm form);

    ResultVO<Void> saveOrUpdateCountry(CountryForm CountryForm);

    void updateState(UpdateStateForm form);

    List<CountryPageVO> listAll();

    void deleteBatch(DeleteBatchForm deleteBatchForm);
}
