package com.miaowen.bh1xlhw.service.mq;


/**
 * @company 武汉秒闻网络科技有限公司
 */
import com.miaowen.bh1xlhw.config.rabbitmq.EmailSendMqConfig;
import com.miaowen.bh1xlhw.model.dto.EmailSendDto;
import com.miaowen.bh1xlhw.model.dto.EmailTaskDTO;
import org.springframework.amqp.core.MessageDeliveryMode;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class EmailTaskProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 创建延时邮件任务
     *
     * @param task    邮件任务DTO
     * @param delayMs 延迟时间（毫秒）
     */
    public void createDelayedEmailTask(EmailTaskDTO task, long delayMs) {
        rabbitTemplate.convertAndSend("email.delayed.exchange", "email.delay.key", task, message -> {
            // 设置延迟时间x-delay
            message.getMessageProperties().setHeader("", delayMs);
            // 设置持久化
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            return message;
        });
    }

    /**
     * 创建延时邮件任务
     *
     * @param task    邮件任务DTO
     */
    public void createEmailTask(EmailSendDto task) {
        rabbitTemplate.convertAndSend(EmailSendMqConfig.EXCHANGE, EmailSendMqConfig.KEY, task, message -> {
            // 设置延迟时间
            message.getMessageProperties().setHeader("x-delay",3000);
            // 设置持久化
            message.getMessageProperties().setDeliveryMode(MessageDeliveryMode.PERSISTENT);
            return message;
        });
    }
}
