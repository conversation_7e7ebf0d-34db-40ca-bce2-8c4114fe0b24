package com.miaowen.bh1xlhw.service.mq;

import com.miaowen.bh1xlhw.config.rabbitmq.EmailSendMqConfig;
import com.miaowen.bh1xlhw.model.dto.EmailSendDto;
import com.miaowen.bh1xlhw.model.dto.EmailTaskDTO;
import com.miaowen.bh1xlhw.repository.IEmailPromotionSendRecordService;
import com.miaowen.bh1xlhw.service.email.EmailSendService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;


@Slf4j
@Component
//@Service
public class EmailTaskConsumer {

    @Resource
    private EmailSendService emailSendService;

    @Resource
    IEmailPromotionSendRecordService iEmailPromotionSendRecordService;

//    /**
//     * 处理延时邮件任务
//     */
//    @RabbitListener(queues = "email.delayed.queue")
//    public void processEmailTask(EmailTaskDTO task) {
//        try {
//
/////
//
//        } catch (Exception e) {
//            // 记录失败日志，可加入重试队列
//            log.error("Consumer this message occurred error {}", e.getMessage(), e);
//        }
//    }


    /**
     * 处理延时邮件任务
     */
    @RabbitListener(queues = EmailSendMqConfig.QUEUE)
    public void processEmailSentTask(EmailSendDto task, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long tag) throws IOException {
        try {
            boolean send = emailSendService.send(task.getEmailEntity(),
                    task.getToEmail(),
                    task.getTitle(),
                    task.getContent(),
                    task.getUnsubscribeUrl());
            if (send) {
                iEmailPromotionSendRecordService.setSuccessNum(task.getId());
            }
            channel.basicAck(tag, false);
        } catch (Exception e) {
            // 记录失败日志，可加入重试队列
            log.error("Consumer this message occurred error {}", e.getMessage(), e);
            channel.basicNack(tag, false, true);
        }
    }

    /**
     * 构建邮件HTML内容（示例）
     */
    private String buildEmailContent(EmailTaskDTO task) {
        return String.format("<h3>业务通知</h3>" + "<p>业务数据：%s</p>" + "<p>详细信息请访问：<a href='%s'>查看详情</a></p>" + "<p>附件：%s</p>", task.getBusinessData().toString(), task.getDynamicUrl(), task.getPdfAttachmentPath());
    }
}