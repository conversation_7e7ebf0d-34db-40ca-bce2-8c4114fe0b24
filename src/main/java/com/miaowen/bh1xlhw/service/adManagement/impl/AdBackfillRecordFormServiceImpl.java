package com.miaowen.bh1xlhw.service.adManagement.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.enums.PlatformTypeEnum;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsInfoBo;
import com.miaowen.bh1xlhw.model.entity.AdBackfillRecord;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.entity.DictionaryType;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.entity.order.OrderQuestionResult;
import com.miaowen.bh1xlhw.model.query.adManagement.AdBackfillDeleteForm;
import com.miaowen.bh1xlhw.model.query.adManagement.AdBackfillForm;
import com.miaowen.bh1xlhw.model.query.adManagement.AdSendForm;
import com.miaowen.bh1xlhw.model.vo.adManagement.AdBackfillRecordVo;
import com.miaowen.bh1xlhw.properties.ApiUrlProperties;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.adManagement.AdBackfillRecordFormService;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

/**
 * @Description 广告回传记录
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/12 9:20
 */
@Slf4j
@Service
@AllArgsConstructor
public class AdBackfillRecordFormServiceImpl implements AdBackfillRecordFormService {
    private AdBackfillRecordService adBackfillRecordService;
    private IOrderService orderService;
    private IOrderQuestionResultService orderQuestionResultService;
    private DictionaryService dictionaryService;
    private DictionaryTypeService dictionaryTypeService;
    private GoodsService goodsService;
    private ICurrencyService currencyService;
    private ApiUrlProperties apiUrlProperties;

    @Override
    public PageVO<AdBackfillRecordVo> page(AdBackfillForm pageForm) {
        try {
            QueryWrapper<AdBackfillRecord> wrapper = new QueryWrapper<>();
            if (!StringUtils.isEmpty(pageForm.getTuid())) {
                wrapper.like("tuid", pageForm.getTuid());
            }
            if (!StringUtils.isEmpty(pageForm.getOrderNo())) {
                wrapper.like("order_no", pageForm.getOrderNo());
            }
            //取广告值
            if (!StringUtils.isEmpty(pageForm.getParams())) {
                wrapper.eq("trace_id", pageForm.getParams());
            }
            if (!StringUtils.isEmpty(pageForm.getPlatformCode())) {
                wrapper.eq("platform_code", pageForm.getPlatformCode());
            }
            if (!StringUtils.isEmpty(pageForm.getPageType())) {
                wrapper.eq("page_type", pageForm.getPageType());
            }
            if (pageForm.getStartDate() != null && pageForm.getEndDate() != null) {
                MonthShardingTableNameHandler.setParams(pageForm.getStartDate());
                if (!DateUtils.isSameMonth(pageForm.getStartDate(), pageForm.getEndDate())) {
                    //跨月 则取开始时间当月
                    wrapper.between("create_time", pageForm.getStartDate(), DateUtils.getLastDayOfMonth(pageForm.getStartDate()));
                } else {
                    wrapper.between("create_time", pageForm.getStartDate(), pageForm.getEndDate());
                }
            } else {
                //没有的话则需要放入时间范围 默认查当月
                wrapper.between("create_time", DateUtils.getFirstDayOfMonth(), new Date());
            }
            wrapper.isNull("delete_time");
            long count = adBackfillRecordService.count(wrapper);

            if (count == 0) {
                return new PageVO<>();
            }
            wrapper.orderByDesc("create_time");
            // 构建分页查询的SQL语句
            int offset = (pageForm.getPageInt() - 1) * pageForm.getPageSize();
            int limit = pageForm.getPageSize();
            // 添加LIMIT条件
            wrapper.last("LIMIT " + offset + "," + limit);
            List<AdBackfillRecord> records = adBackfillRecordService.list(wrapper);
            List<AdBackfillRecordVo> adBackfillRecordVos = BeanUtils.copyList(records, AdBackfillRecordVo.class);
            changeVo(adBackfillRecordVos);
            Page<AdBackfillRecordVo> page = new Page<>(pageForm.getPageInt(), pageForm.getPageSize());
            page.setRecords(adBackfillRecordVos);
            page.setTotal(count);
            return new PageVO<>(page, AdBackfillRecordVo.class);

        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    private void changeVo(List<AdBackfillRecordVo> records) {
        List<Integer> orderIds = new ArrayList<>();
        records.forEach(record -> {
            if (!StringUtils.isEmpty(record.getOrderId())) {
                orderIds.add(Integer.valueOf(record.getOrderId()));
            }
        });
        List<Order> orders = new ArrayList<>();
        List<OrderQuestionResult> orderQuestionResults = new ArrayList<>();
        Map<Long, Order> orderMap;
        Map<Long, OrderQuestionResult> orderQuestionResultMap;
        if (!orderIds.isEmpty()) {
            orders = orderService.listByIds(orderIds);
            if (!CollectionUtils.isEmpty(orders)) {
                orderMap = StreamUtil.map(orders, Order::getId, Function.identity());
            } else {
                orderMap = new HashMap<>();
            }
            orderQuestionResults = orderQuestionResultService.listByIds(orderIds);
            if (!CollectionUtils.isEmpty(orderQuestionResults)) {
                orderQuestionResultMap = StreamUtil.map(orderQuestionResults, OrderQuestionResult::getId,
                        Function.identity());
            } else {
                orderQuestionResultMap = new HashMap<>();
            }
        } else {
            orderMap = new HashMap<>();
            orderQuestionResultMap = new HashMap<>();
        }
        Map<String, String> currencyMap = currencyService.mapAll();

        //查询各个平台对应的页面类型数据
        DictionaryType sendPage = dictionaryTypeService.getOneByCode("sendPage");
        List<Dictionary> pageTypeList = dictionaryService.listByTypeId(sendPage.getId());
        records.forEach(record -> {
                    if (!StringUtils.isEmpty(record.getOrderId())) {
                        Order order = orderMap.get(Long.valueOf(record.getOrderId()));
                        if (Objects.nonNull(order)) {
                            record.setRedPackUseType(order.getRedPackUseType());
                            record.setPayStatus(order.getPaymentStatus());
                            if (Objects.nonNull(order.getPaymentAmount()) && order.getPaymentAmount() != 0) {
                                record.setPayAmount(order.getCurrency() + currencyMap.get(order.getCurrency()) + String.format("%.2f", order.getPaymentAmount() / 100.0));
                            }
                        }

                        OrderQuestionResult orderQuestionResult = orderQuestionResultMap.get(Long.valueOf(record.getOrderId()));
                        if (Objects.nonNull(orderQuestionResult)) {
                            record.setOrderTime(getExamTime(orderQuestionResult.getCompletionTime()));
                        }
                    }

                    for (Dictionary dictionary : pageTypeList) {
                        if (dictionary.getCode().equals(String.valueOf(record.getPageType()))) {
                            record.setPageTypeName(dictionary.getName());
                        }
                    }

                }
        );

    }


    @Override
    public void delete(AdBackfillDeleteForm adBackfillDeleteForm) {
        try {
            MonthShardingTableNameHandler.setParams(adBackfillDeleteForm.getCreateTime());
            UpdateWrapper<AdBackfillRecord> wrapper = new UpdateWrapper<>();
            wrapper.set("delete_time", new Date(System.currentTimeMillis()));
            wrapper.eq("id", adBackfillDeleteForm.getId());
            adBackfillRecordService.update(wrapper);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    @Override
    public String sendAd(AdSendForm form) {
        //先获取这一条数据
        if (Objects.isNull(form) || Objects.isNull(form.getId()) || StringUtils.isEmpty(form.getDate())) {
            return "";
        }

        try {
            Date date = DateUtils.getData(form.getDate());
            MonthShardingTableNameHandler.setParams(date);
            AdBackfillRecord adBackfillRecord = adBackfillRecordService.getById(form.getId());
            if (Objects.isNull(adBackfillRecord)) {
                return "";
            }
            if (adBackfillRecord.getStatus() == 1 && !adBackfillRecord.getPlatformCode().equals(PlatformTypeEnum.Google.getValue())) {
                //只有发送失败的需要重新发送。谷歌平台都可以发送
                return "";
            }
            if (adBackfillRecord.getPlatformCode().equals(PlatformTypeEnum.Google.getValue())) {
                //谷歌平台只有结果页需要发送
                if (adBackfillRecord.getPageType() != 4) {
                    return "";
                }
            }

            //根据推广id获取推广信息
            if (adBackfillRecord.getPlatformCode().equals(PlatformTypeEnum.Google.getValue())) {
                Order order = orderService.getById(adBackfillRecord.getOrderId());
                GoodsInfoBo goodsInfo = goodsService.getGoodsInfo(order.getGoodsId(), order.getGoodsType());
                //拼接广告平台
                String reportUrlSj = adBackfillRecord.getFrontUrl() +
                        goodsInfo.getWebPackageName() +
                        "/result?orderNo=" +
                        order.getOutTradeNo() +
                        "&tgid=" +
                        order.getTgId() +
                        "&lang=" +
                        order.getLanguageCode() +
                        "&logid=" +
                        order.getLogId() +
                        "&gclid=" +
                        adBackfillRecord.getTraceId();
                if (adBackfillRecord.getStatus() == -1) {
                    adBackfillRecord.setStatus(1);
                    adBackfillRecord.setSendStatus("已发送");
                    adBackfillRecordService.updateById(adBackfillRecord);
                }
                return reportUrlSj;
            } else {
                // 1. 安全构建URL
                String url = UriComponentsBuilder.fromHttpUrl(apiUrlProperties.getUserUrl())
                        .path("/api/event/sendBackendEventAd")
                        .queryParam("adId", adBackfillRecord.getId())
                        .queryParam("date", DateUtils.changeDate(date))
                        .encode() // 自动编码参数
                        .build()
                        .toUriString();
                url.replace("?", "/result?");
                // 2. 配置RestTemplate (推荐注入而不是每次新建)
                RestTemplate restTemplate = new RestTemplate();

                // 3. 设置请求头
                HttpHeaders headers = new HttpHeaders();
                headers.set("Content-Type", "application/json");
                headers.add("user-agent", "Application");
                // 可以添加认证头等
                // headers.set("Authorization", "Bearer xxx");

                HttpEntity<String> entity = new HttpEntity<>(headers);

                try {
                    log.info("发送广告url:{}", url);
                    // 4. 发送请求
                    ResponseEntity<String> response = restTemplate.exchange(
                            url,
                            HttpMethod.GET,
                            entity,
                            String.class
                    );

                    return "";
                } catch (Exception e) {
                    // 5. 处理异常
                    log.error("调用/api/event/sendBackendEventAd 发送失败:{}", e.getMessage());
                    throw new RuntimeException("调用/api/event/sendBackendEventAd 发送失败");
                }
            }
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    private String getExamTime(Integer completionTime) {
        String result = "";
        if (completionTime == null || completionTime == 0) {
            return result;
        }
        int days = completionTime / 86400;
        int remainingAfterDays = completionTime % 86400;
        int hours = remainingAfterDays / 3600;
        int remainingSeconds = remainingAfterDays % 3600;
        int minutes = remainingSeconds / 60;
        int seconds = remainingSeconds % 60;

        if (days > 0) {
            return String.format("%d天%d小时%02d分%02d秒", days, hours, minutes, seconds);
        } else if (hours > 0) {
            return String.format("%d小时%02d分%02d秒", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%d分%02d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }

    }

}
