package com.miaowen.bh1xlhw.service.adManagement;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.adManagement.AdBackfillDeleteForm;
import com.miaowen.bh1xlhw.model.query.adManagement.AdBackfillForm;
import com.miaowen.bh1xlhw.model.query.adManagement.AdSendForm;
import com.miaowen.bh1xlhw.model.vo.adManagement.AdBackfillRecordVo;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/12 9:19
 */
public interface AdBackfillRecordFormService {
    PageVO<AdBackfillRecordVo> page(AdBackfillForm form);

    void delete(AdBackfillDeleteForm adBackfillDeleteForm);

    String sendAd(AdSendForm form);
}
