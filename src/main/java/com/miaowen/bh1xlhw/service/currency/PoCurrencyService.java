package com.miaowen.bh1xlhw.service.currency;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.currency.PoCurrencyForm;
import com.miaowen.bh1xlhw.model.query.currency.PoCurrencyPageForm;
import com.miaowen.bh1xlhw.model.vo.currency.PoCurrencyPageVO;

import java.util.List;

/**
 * @ClassName PoCurrencyService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 11:26
 */
public interface PoCurrencyService {

    PageVO<PoCurrencyPageVO> pageCurrency(PoCurrencyPageForm form);

    void saveOrUpdateCurrency(PoCurrencyForm currencyForm);

    void updateState(UpdateStateForm form);

    List<PoCurrencyPageVO> listAll();

    void deleteBatch(DeleteBatchForm deleteBatchForm);

    void updateExchangeRate();

    void deleteRecycles(DeleteBatchForm deleteBatchForm);

    void recoverRecycles(DeleteBatchForm deleteBatchForm);

}
