package com.miaowen.bh1xlhw.service.currency.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.entity.Currency;
import com.miaowen.bh1xlhw.model.entity.PoExchangeRateLog;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.currency.PoCurrencyForm;
import com.miaowen.bh1xlhw.model.query.currency.PoCurrencyPageForm;
import com.miaowen.bh1xlhw.model.vo.currency.PoCurrencyPageVO;
import com.miaowen.bh1xlhw.properties.ExchangeRateProperties;
import com.miaowen.bh1xlhw.repository.ICurrencyService;
import com.miaowen.bh1xlhw.repository.IPoExchangeRateLogService;
import com.miaowen.bh1xlhw.service.currency.PoCurrencyService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.*;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
@AllArgsConstructor
public class PoCurrencyServiceImpl implements PoCurrencyService {
    private final ExchangeRateProperties exchangeRateProperties;
    private final ICurrencyService poCurrencyService;
    private final IPoExchangeRateLogService poExchangeRateLogService;

    @Override
    public PageVO<PoCurrencyPageVO> pageCurrency(PoCurrencyPageForm form) {
        // 1. 构建分页查询条件
        Page<Currency> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<Currency> wrapper = new QueryWrapper<>();
        if (BooleanEnum.TRUE.getValue().equals(form.getIsRecycle())){
            wrapper.gt(DELETE_TIME, SQLConstant.DEFAULT_DELETE_TIME);
        }else {
            wrapper.eq(DELETE_TIME, SQLConstant.DEFAULT_DELETE_TIME);
        }
        // 根据PoCurrencyPageForm的参数动态添加查询条件
        if (form.getId() != null) {
            wrapper.eq("id", form.getId());
        }
        if (StringUtils.isNotBlank(form.getName())) {
            wrapper.like("name", form.getName());
        }
        if (StringUtils.isNotBlank(form.getCurrencySymbol())) {
            wrapper.eq("currency_symbol", form.getCurrencySymbol());
        }
        if (StringUtils.isNotBlank(form.getCurrencyUnit())) {
            wrapper.eq("currency_unit", form.getCurrencyUnit());
        }
        if (StringUtils.isNotBlank(form.getCountryCode())) {
            wrapper.eq("country_code", form.getCountryCode());
        }
        if (StringUtils.isNotBlank(form.getExchangeRate())) {
            wrapper.eq("exchange_rate", form.getExchangeRate());
        }
        if (form.getStatus() != null) {
            wrapper.eq("status", form.getStatus());
        }
        if (form.getCreateTime() != null) {
            wrapper.eq("create_time", form.getCreateTime());
        }
        wrapper.orderByDesc("create_time"); // 按更新时间降序排序
        // 2. 执行分页查询
        Page<Currency> result = poCurrencyService.page(page, wrapper);
        return new PageVO<>(result, PoCurrencyPageVO.class);
    }

    @Override
    public void saveOrUpdateCurrency(PoCurrencyForm currencyForm) {
        // 1. 转换Form为Entity
        Currency entity = new Currency();
        entity = BeanUtils.copy(currencyForm, entity.getClass());

        // 2. 设置时间（如果自动填充未配置）
        if (entity.getId() == null) {
            entity.setCreateTime(LocalDateTime.now());
        }
        entity.setUpdateTime(LocalDateTime.now());

        // 3. 保存或更新
        poCurrencyService.saveOrUpdate(entity);
    }

    @Override
    public void updateState(UpdateStateForm form) {
        // 1. 构建更新条件
        UpdateWrapper<Currency> wrapper = new UpdateWrapper<Currency>().eq(ID, form.getId()).set(STATUS, form.getStatus());

        // 2. 执行更新
        poCurrencyService.update(wrapper);
    }

    @Override
    public List<PoCurrencyPageVO> listAll() {
        // 1. 查询未删除数据
        QueryWrapper<Currency> wrapper = new QueryWrapper<Currency>().eq(DELETE_TIME, 0);

        // 2. 转换结果
        return poCurrencyService.list(wrapper).stream().map(this::convertToPageVO).collect(Collectors.toList());
    }

    @Override
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        poCurrencyService.logicDeleteByIds(deleteBatchForm.getIds());
    }

    @Override
    public void updateExchangeRate() {
        // Setting URL
        RestTemplate restTemplate = new RestTemplate();
        String url = exchangeRateProperties.getApiUrl();
        String jsonString = restTemplate.getForObject(url, String.class);
        JsonObject jsonObj = new Gson().fromJson(jsonString, JsonObject.class);
        JsonElement jsonElement = jsonObj.get("conversion_rates");
        Map<String, Double> map = new Gson().fromJson(
                jsonElement,
                new TypeToken<Map<String, Double>>() {
                }.getType()
        );
        if (CollectionUtils.isEmpty(map)) {
            return;
        }
        List<PoCurrencyPageVO> poCurrencyPageVOS = listAll();
        Map<String, List<PoCurrencyPageVO>> collect = poCurrencyPageVOS.stream()
                .collect(Collectors.groupingBy(
                        PoCurrencyPageVO::getCurrencyUnit
                ));
        List<PoExchangeRateLog> exchangeRateLogs = new ArrayList<>();
        map.forEach((k, v) -> {
            PoExchangeRateLog poExchangeRateLog = new PoExchangeRateLog();
            poExchangeRateLog.setCurrencyUnit(k);
            poExchangeRateLog.setExchangeRate(new BigDecimal(v));
            poExchangeRateLog.setRateDate(new Date());
            poExchangeRateLog.setCreateTime((int) (System.currentTimeMillis() / 1000));
            poExchangeRateLog.setUpdateTime((int) (System.currentTimeMillis() / 1000));
            exchangeRateLogs.add(poExchangeRateLog);
            if (collect.containsKey(k) && !CollectionUtils.isEmpty(collect.get(k))) {
                List<PoCurrencyPageVO> poCurrencyPageVOS1 = collect.get(k);
                for (PoCurrencyPageVO poCurrencyPageVO : poCurrencyPageVOS1) {
                    if (poCurrencyPageVO.getExchangeRate().compareTo(new BigDecimal(v)) != 0){
                        poCurrencyService.setRateById(poCurrencyPageVO.getId(), new BigDecimal(v));
                    }
                }
            }
        });
        poExchangeRateLogService.saveBatch(exchangeRateLogs);
    }

    @Override
    public void deleteRecycles(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        poCurrencyService.removeBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void recoverRecycles(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        poCurrencyService.recoverBatchByIds(deleteBatchForm.getIds());
    }

    /**
     * 实体转VO方法
     */
    private PoCurrencyPageVO convertToPageVO(Currency entity) {
        PoCurrencyPageVO vo = BeanUtils.copy(entity, PoCurrencyPageVO.class);
        return vo;
    }
}




