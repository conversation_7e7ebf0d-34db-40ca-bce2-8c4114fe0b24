package com.miaowen.bh1xlhw.service.article.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.enums.RedisKeyEnum;
import com.miaowen.bh1xlhw.controller.logManagement.SessionTrackingController;
import com.miaowen.bh1xlhw.model.bo.goods.PromotionGoodsInfoBo;
import com.miaowen.bh1xlhw.model.entity.Article;
import com.miaowen.bh1xlhw.model.entity.ArticleLang;
import com.miaowen.bh1xlhw.model.query.article.ArticleAddForm;
import com.miaowen.bh1xlhw.model.query.article.ArticlePageForm;
import com.miaowen.bh1xlhw.model.vo.article.ArticleVo;
import com.miaowen.bh1xlhw.repository.IArticleLangService;
import com.miaowen.bh1xlhw.repository.IArticleService;
import com.miaowen.bh1xlhw.service.article.ArticleService;
import com.miaowen.bh1xlhw.service.good.GoodsPromotionService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/23 14:55
 */
@Slf4j
@Service
public class ArticleServiceImpl implements ArticleService {

    @Resource
    IArticleLangService iArticleLangService;
    @Resource
    IArticleService iArticleService;

    @Resource
    GoodsPromotionService goodsPromotionService;
    @Resource
    StringRedisTemplate stringRedisTemplate;
    @Autowired
    private SessionTrackingController sessionTrackingController;

    @Override
    public void saveOrUpdate(ArticleAddForm addForm) {
        Article article = addForm.getArticle();
        if (addForm.getArticle().getId() == null) {
            //新增
            iArticleService.save(article);
            Integer articleId = article.getId();
            addForm.getDetail().forEach(articleLang -> {
                articleLang.setArticleId(articleId);
            });
        } else {
            //更新
            article.setUpdateTime(LocalDateTime.now());
            iArticleService.updateById(article);
            Integer articleId = article.getId();
            addForm.getDetail().forEach(articleLang -> {
                articleLang.setArticleId(articleId);
            });
            iArticleLangService.deleteByArticleId(articleId);
        }
        iArticleLangService.saveBatch(addForm.getDetail());
        //清除redisKey
        cleanRedis(addForm);
    }

    @Override
    public void deleteById(List<Integer> ids) {
        iArticleService.removeByIds(ids);
        ids.forEach(
                id -> iArticleLangService.deleteByArticleId(id)
        );
    }

    @Override
    public PageVO<ArticleVo> page(ArticlePageForm pageForm, boolean isMulti) {
        IPage<Article> articleIPage = iArticleService.pageSort(pageForm, isMulti);
        PageVO<ArticleVo> result = new PageVO<>();
        result.setTotalCount(articleIPage.getTotal());
        result.setPageInt(pageForm.getPageInt());
        result.setPageSize(pageForm.getPageSize());
        result.setRecords(BeanUtils.copyList(articleIPage.getRecords(), ArticleVo.class));
        return result;
    }

    @Override
    public ArticleAddForm getById(Integer id) {
        Article article = iArticleService.getById(id);
        List<ArticleLang> byArticleId = iArticleLangService.findByArticleId(id);
        ArticleAddForm result = new ArticleAddForm();
        result.setArticle(article);
        result.setDetail(byArticleId);
        return result;
    }

    @Override
    public void updateStatus(Integer id, Integer status) {
        iArticleService.updateStatus(id, status);
    }


    private void cleanRedis(ArticleAddForm addForm) {
        if (Objects.isNull(addForm)
                || Objects.isNull(addForm.getArticle())
                || Objects.isNull(addForm.getArticle().getType())
                || CollectionUtils.isEmpty(addForm.getDetail())) {
            return;
        }
        //清除所有关联redis缓存
        Article article = addForm.getArticle();
        List<PromotionGoodsInfoBo> promotionGoodsInfos = goodsPromotionService.getPromotionGoodsInfos(article.getType());
        if (CollectionUtils.isEmpty(promotionGoodsInfos)) {
            return;
        }
        List<ArticleLang> detail = addForm.getDetail();
        Set<String> keys = new HashSet<>();
        for (PromotionGoodsInfoBo goodsInfo : promotionGoodsInfos) {
            for (ArticleLang articleLang : detail) {
                String key = RedisKeyEnum.ARTICLE_DETAIL_LANGUAGE.getKey(articleLang.getLang(), goodsInfo.getTgId());
                keys.add(key);
            }
        }
        if (CollectionUtils.isEmpty(keys)){
            log.debug("文章删除redis key:{}", JSONObject.toJSONString(keys));
            stringRedisTemplate.delete(keys);
        }
    }

}
