package com.miaowen.bh1xlhw.service.article;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.article.ArticleAddForm;
import com.miaowen.bh1xlhw.model.query.article.ArticlePageForm;
import com.miaowen.bh1xlhw.model.vo.article.ArticleVo;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/23 14:55
 */
public interface ArticleService {

    void saveOrUpdate(ArticleAddForm addForm);

    void deleteById(List<Integer> ids);

    PageVO<ArticleVo> page(ArticlePageForm pageForm, boolean isMulti);

    ArticleAddForm getById(Integer id);

    void updateStatus(Integer id,Integer status);

}
