package com.miaowen.bh1xlhw.service.log.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.entity.SystemLogs;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.log.LogPageForm;
import com.miaowen.bh1xlhw.model.vo.log.SystemLogsVO;
import com.miaowen.bh1xlhw.repository.ISystemLogsService;
import com.miaowen.bh1xlhw.service.log.LogsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * LogsServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-22
 */

@Slf4j
@Service
@AllArgsConstructor
public class LogsServiceImpl implements LogsService {
    private final ISystemLogsService systemLogsService;

    @Override
    public void logicBatchDelete(DeleteBatchForm form) {
        systemLogsService.logicBatchDelete(form.getIds());
    }

    @Override
    public PageVO<SystemLogsVO> page(LogPageForm form) {
        Page<SystemLogs> pages = systemLogsService.pages(form);
        return new PageVO<>(pages, SystemLogsVO.class);
    }
}
