package com.miaowen.bh1xlhw.service.log;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.log.LogPageForm;
import com.miaowen.bh1xlhw.model.vo.log.SystemLogsVO;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface LogsService {

    void logicBatchDelete(DeleteBatchForm form);

    PageVO<SystemLogsVO> page(LogPageForm form);
}
