package com.miaowen.bh1xlhw.service.merchant.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.entity.Merchant;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.merchant.MerchantForm;
import com.miaowen.bh1xlhw.model.query.merchant.MerchantPageForm;
import com.miaowen.bh1xlhw.model.query.merchant.PayConfigForm;
import com.miaowen.bh1xlhw.model.vo.merchant.MerchantPageVO;
import com.miaowen.bh1xlhw.model.vo.merchant.PaypalConfigVO;
import com.miaowen.bh1xlhw.repository.IMerchantService;
import com.miaowen.bh1xlhw.service.merchant.MerchantService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.*;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
public class MerchantServiceImpl implements MerchantService {


    @Resource
    private IMerchantService merchantService;


    @Override
    public PageVO<MerchantPageVO> pageMerchant(MerchantPageForm form) {
        QueryWrapper<Merchant> wrapper = new QueryWrapper<>();
        if (BooleanEnum.TRUE.getValue().equals(form.getIsRecycle())){
            wrapper.gt(DELETE_TIME, SQLConstant.DEFAULT_DELETE_TIME);
        }else {
            wrapper.eq(DELETE_TIME, SQLConstant.DEFAULT_DELETE_TIME);
        }
        // 根据条件构建查询
        if (form.getName() != null && !form.getName().trim().isEmpty()) {
            wrapper.like("name", form.getName());
        }
        if (form.getTypeCode() != null && !form.getTypeCode().trim().isEmpty()) {
            wrapper.eq("type_code", form.getTypeCode());
        }
        if (form.getCompany() != null && !form.getCompany().trim().isEmpty()) {
            wrapper.like("company", form.getCompany());
        }
        if (form.getShowName() != null && !form.getShowName().trim().isEmpty()) {
            wrapper.like("show_name", form.getShowName());
        }
        if (form.getStatus() != null) {
            wrapper.eq("status", form.getStatus());
        }
        if (form.getMerchantEnableStatus() != null) {
            wrapper.eq("merchant_enable_status", form.getMerchantEnableStatus());
        }
        wrapper.orderByDesc("create_time");

        Page<Merchant> page = new Page<>(form.getPageInt(), form.getPageSize());
        page = merchantService.getBaseMapper().selectPage(page, wrapper);
        return new PageVO<>(page, MerchantPageVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateMerchant(MerchantForm form) {
        Merchant merchant = BeanUtils.copy(form, Merchant.class);
        if (Objects.isNull(form.getId())) {
            merchantService.save(merchant);
        } else {
            merchantService.updateById(merchant);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateState(UpdateStateForm form) {
        // 1. 构建更新条件
        UpdateWrapper<Merchant> wrapper = new UpdateWrapper<Merchant>().eq(ID, form.getId()).set(STATUS, form.getStatus());
        // 2. 执行更新
        merchantService.update(wrapper);
    }

    @Override
    public List<MerchantPageVO> listAll() {
        // 1. 查询未删除数据
        QueryWrapper<Merchant> wrapper = new QueryWrapper<Merchant>().eq(DELETE_TIME, 0);

        // 2. 转换结果
        return merchantService.list(wrapper).stream().map(this::convertToPageVO).collect(Collectors.toList());
    }

    @Override
    public void deleteBatch(DeleteBatchForm form) {
        if (CollectionUtils.isEmpty(form.getIds())){
            return;
        }
        merchantService.logicDeleteByIds(form.getIds());
    }

    @Override
    public void uploadPayConfig(PayConfigForm form) {
        Merchant updateParam = Merchant.builder().configData(form.getConfigJsonData().toString()).build();
        updateParam.setId(form.getMerchantId());
        merchantService.updateById(updateParam);
    }

    @Override
    public String getStripePrivateKey() {
        Merchant merchant = merchantService.getBaseMapper().selectOne(new QueryWrapper<Merchant>().eq("type_code", "stripe").last("limit 1"));
        return getKey(merchant);
    }

    @Override
    public String getStripePrivateKeyById(Integer id) {
        Merchant merchant = merchantService.getBaseMapper().selectById(id);
        return getKey(merchant);
    }

    @Override
    public Map<Integer, String> getMerchantMap() {
        List<Merchant> list = merchantService.getBaseMapper().selectList(new QueryWrapper<>());
        return list.stream().collect(Collectors.toMap(Merchant::getId, Merchant::getName));
    }

    @Override
    public PaypalConfigVO getPaypalConfigById(Integer id) {
        Merchant merchant = merchantService.getBaseMapper().selectById(id);
        return getPaypalConfigVo(merchant);
    }

    @Override
    public PaypalConfigVO getPaypalConfig() {
        Merchant merchant = merchantService.getBaseMapper().selectOne(new QueryWrapper<Merchant>().eq("type_code", "paypal").last("limit 1"));
        return getPaypalConfigVo(merchant);
    }

    @Override
    public void deleteRecycles(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        merchantService.removeBatchByIds(deleteBatchForm.getIds());

    }

    @Override
    public void recoverRecycles(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        merchantService.recoverBatchByIds(deleteBatchForm.getIds());
    }

    PaypalConfigVO getPaypalConfigVo(Merchant merchant) {
        if (merchant == null) {
            throw new BizException(404, "paypal类型的商户不存在");
        }
        String configData = merchant.getConfigData();
        try {
            JSONObject jsonObject = JSONUtil.parseObj(configData);
            PaypalConfigVO vo = new PaypalConfigVO();
            vo.setEmail(jsonObject.getStr("email"));
            vo.setClientId(jsonObject.getStr("clientId"));
            vo.setPrivateKey(jsonObject.getStr("privateKey"));
            vo.setSandboxMode(jsonObject.getInt("sandboxMode"));
            vo.setMerchantId(merchant.getId());
            return vo;
        } catch (Exception e) {
            throw new BizException(404, "商户配置不正确或未配置");
        }
    }

    String getKey(Merchant merchant) {
        if (merchant == null) {
            throw new BizException(404, "stripe类型的商户不存在");
        }
        String configData = merchant.getConfigData();
        String privateKey = "";
        try {
            JSONObject jsonObject = JSONUtil.parseObj(configData);
            privateKey = jsonObject.getStr("privateKey");
        } catch (Exception e) {
            throw new BizException(404, "商户配置不正确或未配置");
        }

        return privateKey;
    }


    /**
     * 实体转VO方法
     */
    private MerchantPageVO convertToPageVO(Merchant entity) {
        if (entity == null) {
            return null;
        }

        MerchantPageVO vo = BeanUtils.copy(entity, MerchantPageVO.class);
        return vo;
    }
}




