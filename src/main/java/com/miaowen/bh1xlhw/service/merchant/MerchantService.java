package com.miaowen.bh1xlhw.service.merchant;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.merchant.MerchantForm;
import com.miaowen.bh1xlhw.model.query.merchant.MerchantPageForm;
import com.miaowen.bh1xlhw.model.query.merchant.PayConfigForm;
import com.miaowen.bh1xlhw.model.vo.merchant.MerchantPageVO;
import com.miaowen.bh1xlhw.model.vo.merchant.PaypalConfigVO;

import java.util.List;
import java.util.Map;

/**
 * @ClassName PoCountryService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 11:26
 */
public interface MerchantService {

    PageVO<MerchantPageVO> pageMerchant(MerchantPageForm form);

    void saveOrUpdateMerchant(MerchantForm merchantForm);

    void updateState(UpdateStateForm form);

    List<MerchantPageVO> listAll();

    void deleteBatch(DeleteBatchForm deleteBatchForm);

    void uploadPayConfig(PayConfigForm form);


    //按默认的策略获取stripe商户
    String getStripePrivateKey();

    //根据id取stripe商户
    String getStripePrivateKeyById(Integer id);

    //获取商户id和商户名的映射
    Map<Integer, String> getMerchantMap();

    //根据id取paypal商户
    PaypalConfigVO getPaypalConfigById(Integer id);

    //按默认的策略获取paypal商户
    PaypalConfigVO getPaypalConfig();

    void deleteRecycles(DeleteBatchForm deleteBatchForm);

    void recoverRecycles(DeleteBatchForm deleteBatchForm);
}
