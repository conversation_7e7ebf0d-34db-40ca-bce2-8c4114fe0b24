package com.miaowen.bh1xlhw.service.operation.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.model.entity.OperationManager;
import com.miaowen.bh1xlhw.model.entity.OperationUser;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerQryForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserQryForm;
import com.miaowen.bh1xlhw.model.vo.operation.OperationManagerVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationUserVO;
import com.miaowen.bh1xlhw.repository.OperationManagerService;
import com.miaowen.bh1xlhw.repository.OperationUserService;
import com.miaowen.bh1xlhw.service.operation.OperationUserInfoService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * OperationUserServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationUserInfoServiceImpl implements OperationUserInfoService {
    private final OperationUserService operationUserService;
    private final OperationManagerService operationManagerService;

    @Override
    public void saveUser(OperationUserForm operationUserForm) {
        OperationUser operationUser = BeanUtils.copy(operationUserForm, OperationUser.class);
        try {
            operationUserService.save(operationUser);
        } catch (Exception e) {
            throw new BizException(ResultEnum.WORK_NO_EXIST);
        }
    }

    @Override
    public void updateUser(Integer id, OperationUserForm operationUserForm) {
        OperationUser operationUser = BeanUtils.copy(operationUserForm, OperationUser.class);
        operationUser.setId(id);
        operationUserService.updateById(operationUser);
    }

    @Override
    public void removeUser(Integer id) {
        operationUserService.logicRemoveById(id);
    }

    @Override
    public void recoverUser(Integer id) {
        operationUserService.recoverById(id);
    }

    @Override
    public PageVO<OperationUserVO> pageUser(OperationUserQryForm pageForm) {
        Page<OperationUser> pageList = operationUserService.pageInfo(pageForm);
        List<OperationUser> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return new PageVO<>();
        }
        List<OperationUserVO> operationUserVoList = getOperationUserVos(records);
        return new PageVO<OperationUserVO>(pageList).convert(operationUserVoList);
    }

    private List<OperationUserVO> getOperationUserVos(List<OperationUser> records) {
        Set<Integer> managerIds = StreamUtil.fetchSet(records, OperationUser::getPid);
        List<OperationManager> operationManagers = operationManagerService.listExistByIds(managerIds);
        Map<Integer, OperationManager> managerMap = StreamUtil.map(operationManagers, OperationManager::getId, Function.identity());
        return records.stream().map(operationUser -> {
            OperationUserVO operationUserVo = BeanUtils.copy(operationUser, OperationUserVO.class);
            OperationManager operationManager = managerMap.get(operationUser.getPid());
            if (Objects.nonNull(operationManager)) {
                operationUserVo.setPUserName(operationManager.getName());
            }
            return operationUserVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OperationUserVO> listUser() {
        List<OperationUser> records = operationUserService.allExist();
        return getOperationUserVos(records);
    }

    @Override
    public void saveManager(OperationManagerForm operationManagerForm) {
        OperationManager operationManager = BeanUtils.copy(operationManagerForm, OperationManager.class);
        try {
            operationManagerService.save(operationManager);
        } catch (Exception e) {
            throw new BizException(ResultEnum.WORK_NO_EXIST);
        }
    }

    @Override
    public void updateManager(Integer id, OperationManagerForm operationManagerForm) {
        OperationManager operationManager = BeanUtils.copy(operationManagerForm, OperationManager.class);
        operationManager.setId(id);
        operationManagerService.updateById(operationManager);
    }

    @Override
    public void removeManager(Integer id) {
        operationManagerService.logicRemoveById(id);
    }

    @Override
    public void recoverManager(Integer id) {
        operationManagerService.recoverById(id);

    }

    @Override
    public PageVO<OperationManagerVO> pageManager(OperationManagerQryForm pageForm) {
        Page<OperationManager> pageList = operationManagerService.pageInfo(pageForm);
        List<OperationManager> records = pageList.getRecords();
        List<OperationManagerVO> operationUserVoList = BeanUtils.copyList(records, OperationManagerVO.class);
        return new PageVO<OperationManagerVO>(pageList).convert(operationUserVoList);
    }

    @Override
    public List<OperationManagerVO> allManager() {
        List<OperationManager> records = operationManagerService.allExist();
        return BeanUtils.copyList(records, OperationManagerVO.class);
    }
}
