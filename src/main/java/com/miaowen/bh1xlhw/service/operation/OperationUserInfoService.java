package com.miaowen.bh1xlhw.service.operation;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerQryForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserQryForm;
import com.miaowen.bh1xlhw.model.vo.operation.OperationManagerVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationUserVO;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface OperationUserInfoService {

    /**
     * 保存运营人员
     */
    void saveUser(OperationUserForm operationUserForm);

    /**
     * 修改运营人员
     */
    void updateUser(Integer id, OperationUserForm operationUserForm);

    /**
     * 删除运营人员
     */
    void removeUser(Integer id);

    /**
     * 恢复运营人员
     */
    void recoverUser(Integer id);

    /**
     * 运营人员列表
     */
    PageVO<OperationUserVO> pageUser(OperationUserQryForm pageForm);

    /**
     * 运营人员列表全部
     */
    List<OperationUserVO> listUser();

    /**
     * 保存运营主管
     */
    void saveManager(OperationManagerForm operationManagerForm);

    /**
     * 修改运营主管
     */
    void updateManager(Integer id, OperationManagerForm operationManagerForm);

    /**
     * 删除运营主管
     */
    void removeManager(Integer id);

    /**
     * 恢复运营主管
     */
    void recoverManager(Integer id);

    /**
     * 运营主管列表
     */
    PageVO<OperationManagerVO> pageManager(OperationManagerQryForm pageForm);

    /**
     * 全部运营主管列表
     */
    List<OperationManagerVO> allManager();




}
