package com.miaowen.bh1xlhw.service.operation;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.operation.PlatformPageForm;
import com.miaowen.bh1xlhw.model.query.operation.*;
import com.miaowen.bh1xlhw.model.vo.operation.AgentVO;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.model.vo.operation.DomainVO;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;

import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
public interface OperationService {


    /**
     * 新增代理商
     */
    void savePlatform(PlatformForm platformForm);

    /**
     * 新增代理商
     */
    void updatePlatform(Integer id, PlatformForm platformForm);

    /**
     * 新增代理商
     */
    void removePlatform(Integer id);

    /**
     * 新增代理商
     */
    void recoverPlatform(Integer id);

    /**
     * 新增代理商
     */
    PageVO<PlatformVO> pagePlatform(PlatformPageForm pageForm);

    /**
     * 新增代理商全部
     */
    List<PlatformVO> allPlatform();

    /**
     * 保存域名
     */
    void saveDomain(DomainForm domainForm);

    /**
     * 修改域名
     */
    void updateDomain(Integer id, DomainForm domainForm);

    /**
     * 删除域名
     */
    void removeDomain(Integer id);

    /**
     * 恢复域名
     */
    void recoverDomain(Integer id);

    /**
     * 域名列表
     */
    PageVO<DomainVO> pageDomain(DomainPageForm pageForm);

    /**
     * 域名列表全部
     */
    List<DomainVO> listDomain();

    /**
     * 保存价格方案
     */
    void savePrice(PriceForm priceForm);

    /**
     * 修改价格方案
     */
    void updatePrice(Integer id, PriceForm priceForm);

    /**
     * 删除价格方案
     */
    void removePrice(Integer id);

    /**
     * 恢复价格方案
     */
    void recoverPrice(Integer id);

    /**
     * 价格方案列表
     */
    PageVO<PriceVO> pagePrice(PricePageForm pageForm);

    /**
     * 价格方案列表全部
     */
    List<PriceVO> listPrice();


    void saveAgent(AgentForm agentForm);

    void updateAgent(Integer id, AgentForm agentForm);

    void deleteAgent(Integer id);

    PageVO<AgentVO> pageAgent(AgentPageForm pageForm);

    List<AgentVO> allAgent();

}
