package com.miaowen.bh1xlhw.service.operation;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.AgentsPageForm;
import com.miaowen.bh1xlhw.model.query.goods.PlatformPageForm;
import com.miaowen.bh1xlhw.model.query.operation.*;
import com.miaowen.bh1xlhw.model.vo.operation.AgentsVO;
import com.miaowen.bh1xlhw.model.vo.operation.DomainVO;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;

import java.util.List;

/**
 * PlatformService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
public interface OperationService {

    /**
     * 新增平台
     */
    void savePlatform(PlatformForm platformForm);

    /**
     * 修改平台
     */
    void updatePlatform(Integer id, PlatformForm platformForm);

    /**
     * 删除平台
     */
    void removePlatform(Integer id);

    /**
     * 恢复平台
     */
    void recoverPlatform(Integer id);

    /**
     * 平台列表
     */
    PageVO<PlatformVO> pagePlatform(PlatformPageForm pageForm);

    /**
     * 全平台列表
     */
    List<PlatformVO> allPlatform();


    /**
     * 新增代理商
     */
    void saveAgents(AgentsForm agentsForm);

    /**
     * 新增代理商
     */
    void updateAgents(Integer id, AgentsForm agentsForm);

    /**
     * 新增代理商
     */
    void removeAgents(Integer id);

    /**
     * 新增代理商
     */
    void recoverAgents(Integer id);

    /**
     * 新增代理商
     */
    PageVO<AgentsVO> pageAgents(AgentsPageForm pageForm);

    /**
     * 新增代理商全部
     */
    List<AgentsVO> allAgents();

    /**
     * 保存域名
     */
    void saveDomain(DomainForm domainForm);

    /**
     * 修改域名
     */
    void updateDomain(Integer id, DomainForm domainForm);

    /**
     * 删除域名
     */
    void removeDomain(Integer id);

    /**
     * 恢复域名
     */
    void recoverDomain(Integer id);

    /**
     * 域名列表
     */
    PageVO<DomainVO> pageDomain(DomainPageForm pageForm);

    /**
     * 域名列表全部
     */
    List<DomainVO> listDomain();

    /**
     * 保存价格方案
     */
    void savePrice(PriceForm priceForm);

    /**
     * 修改价格方案
     */
    void updatePrice(Integer id, PriceForm priceForm);

    /**
     * 删除价格方案
     */
    void removePrice(Integer id);

    /**
     * 恢复价格方案
     */
    void recoverPrice(Integer id);

    /**
     * 价格方案列表
     */
    PageVO<PriceVO> pagePrice(PricePageForm pageForm);

    /**
     * 价格方案列表全部
     */
    List<PriceVO> listPrice();



}
