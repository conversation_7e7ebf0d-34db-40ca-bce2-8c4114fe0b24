package com.miaowen.bh1xlhw.service.operation.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.bo.DomainPlatformCountBO;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.goods.AgentsPageForm;
import com.miaowen.bh1xlhw.model.query.goods.PlatformPageForm;
import com.miaowen.bh1xlhw.model.query.operation.*;
import com.miaowen.bh1xlhw.model.vo.operation.AgentsVO;
import com.miaowen.bh1xlhw.model.vo.operation.DomainVO;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.operation.OperationService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PlatformServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationServiceImpl implements OperationService {
    private final PlatformService platformService;
    private final AgentsService agentsService;
    private final DomainService domainService;
    private final IPriceService priceService;
    private IEmailService emailService;
    private final DomainPlatformService domainPlatformService;

    @Override
    public void savePlatform(PlatformForm platformForm) {
        Platform platform = BeanUtils.copy(platformForm, Platform.class);
        platformService.save(platform);
    }

    @Override
    public void updatePlatform(Integer id, PlatformForm platformForm) {
        Platform platform = BeanUtils.copy(platformForm, Platform.class);
        platform.setId(id);
        platformService.updateById(platform);
    }

    @Override
    public void removePlatform(Integer id) {
        platformService.logicRemoveById(id);
    }

    @Override
    public void recoverPlatform(Integer id) {
        platformService.recoverById(id);
    }

    @Override
    public PageVO<PlatformVO> pagePlatform(PlatformPageForm pageForm) {
        Page<Platform> pageList = platformService.pageInfo(pageForm);
        List<Platform> records = pageList.getRecords();
        List<PlatformVO> platformVOList = BeanUtils.copyList(records, PlatformVO.class);
        return new PageVO<PlatformVO>(pageList).convert(platformVOList);

    }

    @Override
    public List<PlatformVO> allPlatform() {
        List<Platform> records = platformService.allExist();
        return  BeanUtils.copyList(records, PlatformVO.class);
    }

    @Override
    public void saveAgents(AgentsForm agentsForm) {
        Agents agents = BeanUtils.copy(agentsForm, Agents.class);
        Platform platform = platformService.getExistById(agentsForm.getPlatformId());
        BigDecimal rebate = agentsForm.getRebate().abs();
        if (agentsForm.getRebateSymbol() != 1){
            rebate = rebate.multiply(BigDecimal.valueOf(-1));
        }
        agents.setRebate(rebate);
        agents.setPlatform(platform.getType());
        agentsService.save(agents);
    }

    @Override
    public void updateAgents(Integer id, AgentsForm agentsForm) {
        Agents agents = BeanUtils.copy(agentsForm, Agents.class);
        BigDecimal rebate = agentsForm.getRebate().abs();
        if (agentsForm.getRebateSymbol() != 1){
            rebate = rebate.multiply(BigDecimal.valueOf(-1));
        }
        agents.setId(id);
        agents.setRebate(rebate);
        agentsService.updateById(agents);
    }

    @Override
    public void removeAgents(Integer id) {
        agentsService.logicRemoveById(id);
    }

    @Override
    public void recoverAgents(Integer id) {
        agentsService.recoverById(id);
    }

    @Override
    public PageVO<AgentsVO> pageAgents(AgentsPageForm pageForm) {
        Page<Agents> pageList = agentsService.pageInfo(pageForm);
        List<Agents> records = pageList.getRecords();
        List<AgentsVO> agentsVOList = getAgentsVoList(records);
        return new PageVO<AgentsVO>(pageList).convert(agentsVOList);
    }

    private List<AgentsVO> getAgentsVoList(List<Agents> records) {
        Set<Integer> platformIds = StreamUtil.fetchSet(records, Agents::getPlatformId);
        Map<Integer, Platform> platformMap = platformService.mapByIds(platformIds);
        return records.stream().map(record -> {
            AgentsVO agentsVo = BeanUtils.copy(record, AgentsVO.class);
            Platform platform = platformMap.get(record.getPlatformId());
            if (Objects.nonNull(platform)) {
                agentsVo.setRebateSymbol(record.getRebate().compareTo(BigDecimal.ONE) >= 0 ? BooleanEnum.TRUE.getValue(): BooleanEnum.FALSE.getValue());
                agentsVo.setRebate(record.getRebate().abs());
                agentsVo.setPlatformName(platform.getName());
                agentsVo.setPlatformType(platform.getType());
            }
            return agentsVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AgentsVO> allAgents() {
        List<Agents> records = agentsService.listAll();
        return getAgentsVoList(records);
    }

    @Override
    public void saveDomain(DomainForm domainForm) {
        Domain domain = BeanUtils.copy(domainForm, Domain.class);
        domain.setEmailIds(CommonConstant.idsToString(domainForm.getEmailIds()));
        domainService.save(domain);
    }

    @Override
    public void updateDomain(Integer id, DomainForm domainForm) {
        Domain domain = BeanUtils.copy(domainForm, Domain.class);
        domain.setEmailIds(CommonConstant.idsToString(domainForm.getEmailIds()));
        domain.setId(id);
        domainService.updateById(domain);
    }

    @Override
    public void removeDomain(Integer id) {
        domainService.logicRemoveById(id);
    }

    @Override
    public void recoverDomain(Integer id) {
        domainService.recoverById(id);
    }

    @Override
    public PageVO<DomainVO> pageDomain(DomainPageForm pageForm) {
        Page<Domain> pageList = domainService.pageInfo(pageForm);
        List<Domain> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return new PageVO<>();
        }

        List<DomainVO> domainVoList = getDomainVos(records);
        return new PageVO<DomainVO>(pageList).convert(domainVoList);
    }

    private List<DomainVO> getDomainVos(List<Domain> records) {
        Set<Integer> emailIdSet = new HashSet<>();
        records.forEach(record -> emailIdSet.addAll(CommonConstant.stringToIds(record.getEmailIds())));
        Map<Integer, Email> emailMap = emailService.listExistByIds(emailIdSet);
        Map<Integer, String> platformMap = platformService.allExistMap();

        //获取域名平台使用情况
        List<DomainPlatformCountBO> domainPlatformCountBos = domainPlatformService.listDomainPlatformCount();
        Map<Integer, Integer> domainPlatformCountMap = StreamUtil.map(
                domainPlatformCountBos,
                DomainPlatformCountBO::getDomainId,
                DomainPlatformCountBO::getCount);

        return records.stream().map(record -> {
            Integer count = domainPlatformCountMap.getOrDefault(record.getId(), 0);
            String platformName = platformMap.get(record.getPlatformId());
            DomainVO domainVO = BeanUtils.copy(record, DomainVO.class);
            List<Integer> emailIds = CommonConstant.stringToIds(record.getEmailIds());
            List<Email> emails = new ArrayList<>();
            emailIds.forEach(emailId -> {
                Email email = emailMap.get(emailId);
                emails.add(email);
            });
            domainVO.setPlatformName(platformName);
            domainVO.setEmails(emails);
            domainVO.setUse(Objects.nonNull(count) ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());
            return domainVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DomainVO> listDomain() {
        List<Domain> records = domainService.listAll();
        Map<Integer, String> platformMap = platformService.allExistMap();
        return getDomainVos(records);
    }

    @Override
    public void savePrice(PriceForm priceForm) {
        Price price = priceForm.transform();
        priceService.save(price);
    }

    @Override
    public void updatePrice(Integer id, PriceForm priceForm) {
        Price price = priceForm.transform();
        price.setId(id);
        priceService.updateById(price);
    }

    @Override
    public void removePrice(Integer id) {
        priceService.logicRemoveById(id);
    }

    @Override
    public void recoverPrice(Integer id) {
        priceService.recoverById(id);

    }

    @Override
    public PageVO<PriceVO> pagePrice(PricePageForm pageForm) {
        Page<Price> pageList = priceService.pageInfo(pageForm);
        List<Price> records = pageList.getRecords();
        //对象转换
        List<PriceVO> priceVoList = records.stream().map(PriceVO::transformVo).collect(Collectors.toList());
        return new PageVO<PriceVO>(pageList).convert(priceVoList);
    }

    @Override
    public List<PriceVO> listPrice() {
        List<Price> records = priceService.listAll();
        //对象转换
        return records.stream().map(PriceVO::transformVo).collect(Collectors.toList());
    }
}
