package com.miaowen.bh1xlhw.service.operation.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.bo.DomainPlatformCountBO;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.operation.PlatformPageForm;
import com.miaowen.bh1xlhw.model.query.operation.*;
import com.miaowen.bh1xlhw.model.vo.operation.AgentVO;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.model.vo.operation.DomainVO;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.operation.OperationService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * PlatformServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Slf4j
@Service
@AllArgsConstructor
public class OperationServiceImpl implements OperationService {
    private final PlatformService platformService;
    private final DomainService domainService;
    private final IPriceService priceService;
    private final IEmailService emailService;
    private final IAgentService agentService;
    private final GoodsPromotionMultilingualService goodsPromotionMultilingualService;
    private final GoodsPromotionTraditionalService goodsPromotionTraditionalService;



    @Override
    public void savePlatform(PlatformForm platformForm) {
        Platform platform = BeanUtils.copy(platformForm, Platform.class);
        BigDecimal rebate = platformForm.getRebate().abs();
        if (platformForm.getRebateSymbol() != 1){
            rebate = rebate.multiply(BigDecimal.valueOf(-1));
        }
        platform.setRebate(rebate);
        platformService.save(platform);
    }

    @Override
    public void updatePlatform(Integer id, PlatformForm platformForm) {
        Platform platform = BeanUtils.copy(platformForm, Platform.class);
        BigDecimal rebate = platformForm.getRebate().abs();
        if (platformForm.getRebateSymbol() != 1){
            rebate = rebate.multiply(BigDecimal.valueOf(-1));
        }
        platform.setId(id);
        platform.setRebate(rebate);
        platformService.updateById(platform);
    }

    @Override
    public void removePlatform(Integer id) {
        platformService.logicRemoveById(id);
    }

    @Override
    public void recoverPlatform(Integer id) {
        platformService.recoverById(id);
    }

    @Override
    public PageVO<PlatformVO> pagePlatform(PlatformPageForm pageForm) {
        Page<Platform> pageList = platformService.pageInfo(pageForm);
        List<Platform> records = pageList.getRecords();
        List<PlatformVO> platformVOList = getPlatformVoList(records);
        return new PageVO<PlatformVO>(pageList).convert(platformVOList);
    }

    private List<PlatformVO> getPlatformVoList(List<Platform> records) {
        Set<Integer> agentIds = StreamUtil.fetchSet(records, Platform::getAgentId);
        Map<Integer, Agent> agentMap = agentService.mapByIds(agentIds);
        return records.stream().map(record -> {
            PlatformVO platformVo = BeanUtils.copy(record, PlatformVO.class);
            Agent agent = agentMap.get(record.getAgentId());
            if (Objects.nonNull(agent)){
                platformVo.setAgentId(agent.getId());
                platformVo.setSettleType(agent.getSettleType());
            }
            platformVo.setRebateSymbol(record.getRebate().compareTo(BigDecimal.ONE) >= 0 ? BooleanEnum.TRUE.getValue(): BooleanEnum.FALSE.getValue());
            platformVo.setRebate(record.getRebate().abs());
            return platformVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PlatformVO> allPlatform() {
        List<Platform> records = platformService.listAll();
        return getPlatformVoList(records);
    }

    @Override
    public void saveDomain(DomainForm domainForm) {
        Domain domain = BeanUtils.copy(domainForm, Domain.class);
        domain.setEmailIds(CommonConstant.idsToString(domainForm.getEmailIds()));
        domainService.save(domain);
    }

    @Override
    public void updateDomain(Integer id, DomainForm domainForm) {
        Domain domain = BeanUtils.copy(domainForm, Domain.class);
        domain.setEmailIds(CommonConstant.idsToString(domainForm.getEmailIds()));
        domain.setId(id);
        domainService.updateById(domain);
    }

    @Override
    public void removeDomain(Integer id) {
        domainService.logicRemoveById(id);
    }

    @Override
    public void recoverDomain(Integer id) {
        domainService.recoverById(id);
    }

    @Override
    public PageVO<DomainVO> pageDomain(DomainPageForm pageForm) {
        Page<Domain> pageList = domainService.pageInfo(pageForm);
        List<Domain> records = pageList.getRecords();
        if (CollectionUtils.isEmpty(records)){
            return new PageVO<>();
        }

        List<DomainVO> domainVoList = getDomainVos(records);
        return new PageVO<DomainVO>(pageList).convert(domainVoList);
    }

    private List<DomainVO> getDomainVos(List<Domain> records) {
        Map<Integer, Set<String>> domainUseMap = new HashMap<>();
        List<GoodsPromotionMultilingual> goodsPromotionMultilingualList = goodsPromotionMultilingualService.listAll();
        List<GoodsPromotionTraditional> goodsPromotionTraditionalList = goodsPromotionTraditionalService.listAll();
        goodsPromotionMultilingualList.stream()
                .forEach(promotion -> {
                    Integer domainId = promotion.getDomainId();
                    String platformType = promotion.getPlatformType();
                    domainUseMap
                        .computeIfAbsent(domainId, k -> new HashSet<>())
                        .add(platformType); // 添加平台类型
                });

        goodsPromotionTraditionalList.stream()
                .forEach(promotion -> {
                    Integer domainId = promotion.getDomainId();
                    String platformType = promotion.getPlatformType();
                    domainUseMap
                        .computeIfAbsent(domainId, k -> new HashSet<>())
                        .add(platformType);
                });

        Set<Integer> emailIdSet = records.stream()
                .flatMap(record -> CommonConstant.stringToIds(record.getEmailIds()).stream())
                .collect(Collectors.toSet());
        Map<Integer, Email> emailMap = emailService.listExistByIds(emailIdSet);

        return records.stream().map(record -> {
            DomainVO domainVO = BeanUtils.copy(record, DomainVO.class);
            List<Integer> emailIds = CommonConstant.stringToIds(record.getEmailIds());
            List<Email> emails = new ArrayList<>();
            emailIds.forEach(emailId -> {
                Email email = emailMap.get(emailId);
                emails.add(email);
            });
            domainVO.setEmails(emails);
            domainVO.setPlatformUsed(domainUseMap.get(record.getId()));
            return domainVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<DomainVO> listDomain() {
        List<Domain> records = domainService.listAll();
        return getDomainVos(records);
    }

    @Override
    public void savePrice(PriceForm priceForm) {
        Price price = priceForm.transform();
        priceService.save(price);
    }

    @Override
    public void updatePrice(Integer id, PriceForm priceForm) {
        Price price = priceForm.transform();
        price.setId(id);
        priceService.updateById(price);
    }

    @Override
    public void removePrice(Integer id) {
        priceService.logicRemoveById(id);
    }

    @Override
    public void recoverPrice(Integer id) {
        priceService.recoverById(id);

    }

    @Override
    public PageVO<PriceVO> pagePrice(PricePageForm pageForm) {
        Page<Price> pageList = priceService.pageInfo(pageForm);
        List<Price> records = pageList.getRecords();
        //对象转换
        List<PriceVO> priceVoList = records.stream().map(PriceVO::transformVo).collect(Collectors.toList());
        return new PageVO<PriceVO>(pageList).convert(priceVoList);
    }

    @Override
    public List<PriceVO> listPrice() {
        List<Price> records = priceService.listAll();
        //对象转换
        return records.stream().map(PriceVO::transformVo).collect(Collectors.toList());
    }

    @Override
    public void saveAgent(AgentForm agentForm) {
        Agent agent = BeanUtils.copy(agentForm, Agent.class);
        agentService.save(agent);
    }

    @Override
    public void updateAgent(Integer id, AgentForm agentForm) {
        Agent agent = BeanUtils.copy(agentForm, Agent.class);
        agent.setId(id);
        agentService.updateById(agent);
    }

    @Override
    public void deleteAgent(Integer id) {
        agentService.logicDeleteById(id);
    }

    @Override
    public PageVO<AgentVO> pageAgent(AgentPageForm pageForm) {
        Page<Agent> agentPage = agentService.pageInfo(pageForm);
        return new PageVO<>(agentPage, AgentVO.class);
    }

    @Override
    public List<AgentVO> allAgent() {
        List<Agent> agents = agentService.listAll();
        return BeanUtils.copyList(agents, AgentVO.class);
    }
}
