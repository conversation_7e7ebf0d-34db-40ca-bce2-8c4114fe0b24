package com.miaowen.bh1xlhw.service.order;

import com.miaowen.bh1xlhw.model.bo.oa.StatisticOrderBO;
import com.miaowen.bh1xlhw.model.query.order.OrderStatisticForm;

import java.time.LocalDate;
import java.util.List;

/**
 * 订单统计服务接口
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-25
 */
public interface OrderStatisticService {

    /**
     * 使用条件构造器进行订单统计查询
     *
     * @param form 统计查询表单
     * @return 统计结果列表
     */
    List<StatisticOrderBO> statisticOrderByConditions(OrderStatisticForm form);

    /**
     * 根据推广ID统计订单
     *
     * @param tgId 推广ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果列表
     */
    List<StatisticOrderBO> statisticOrderByTgId(String tgId, LocalDate startDate, LocalDate endDate);

    /**
     * 根据商品类型统计订单
     *
     * @param goodsType 商品类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果列表
     */
    List<StatisticOrderBO> statisticOrderByGoodsType(Integer goodsType, LocalDate startDate, LocalDate endDate);

    /**
     * 统计已支付订单
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果列表
     */
    List<StatisticOrderBO> statisticPaidOrders(LocalDate startDate, LocalDate endDate);
}
