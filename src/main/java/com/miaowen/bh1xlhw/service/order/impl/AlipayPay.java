package com.miaowen.bh1xlhw.service.order.impl;


import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.domain.AlipayTradeRefundModel;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.miaowen.bh1xlhw.client.alibaba.AlipayClientConfig;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.enums.RefundStatusEnum;
import com.miaowen.bh1xlhw.model.bo.refund.RefundBO;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.service.order.BasePay;
import com.miaowen.bh1xlhw.utils.IdUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * PaypalPay :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-29
 */
@Slf4j
@Service("alipay")
@AllArgsConstructor
public class AlipayPay implements BasePay {
    private final AlipayClientConfig alipayClientConfig;

    @Override
    public RefundBO refund(BaseOrder order, BigDecimal amount, String notifyUrl) {
        AlipayClient alipayClient = alipayClientConfig.getClient(order.getMerchantId());
        // 构造请求参数以调用接口
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        AlipayTradeRefundModel model = new AlipayTradeRefundModel();
        // 设置支付宝交易号
        model.setTradeNo(order.getThirdOutTradeNo());
        // 设置退款金额
        model.setRefundAmount(amount.toString());
        // 设置退款原因说明
//        model.setRefundReason("正常退款");
        // 设置退款请求号
        String refundTradeNo = IdUtil.generateRefundTradeNo();
        model.setOutRequestNo(refundTradeNo);
        request.setBizModel(model);
        RefundBO refundBO = new RefundBO();
        refundBO.setStatus(RefundStatusEnum.FAILED.getStatus());

        try {
            AlipayTradeRefundResponse response = alipayClient.certificateExecute(request);
            log.info("支付宝退款,response:{}", JSONObject.toJSONString(response));
            if ("10000".equals(response.getCode())){
                refundBO.setRefundId(order.getCaptureId());
                refundBO.setRefundTradeNo(refundTradeNo);
                refundBO.setStatus("Y".equals(response.getFundChange())? RefundStatusEnum.SUCCEEDED.getStatus() : RefundStatusEnum.FAILED.getStatus());
            }else {
               throw new BizException(response.getSubCode());
            }
            return refundBO;
        } catch (AlipayApiException e) {
            log.error("支付宝退款失败",e);
            throw new BizException(e.getMessage());
        }
    }
}
