package com.miaowen.bh1xlhw.service.order.impl;

import com.miaowen.bh1xlhw.model.entity.RefundStatistic;
import com.miaowen.bh1xlhw.model.vo.oa.StatisticRefundVO;
import com.miaowen.bh1xlhw.repository.IRefundRecordService;
import com.miaowen.bh1xlhw.repository.RefundStatisticService;
import com.miaowen.bh1xlhw.service.order.RefundService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.DecimalUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.YearMonth;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * RefundServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-28
 */
@Service
@AllArgsConstructor
public class RefundServiceImpl implements RefundService {
    private final IRefundRecordService refundRecordService;
    private final RefundStatisticService refundStatisticService;

    @Override
    public void refundStatistic(LocalDate lastMonthDay) {
        RefundStatistic refundStatistic = refundStatisticService.getByDate(lastMonthDay);
        Integer sumRefundAmount = refundRecordService.sumByDate(lastMonthDay);
        if (Objects.isNull(refundStatistic)){
            refundStatistic = new RefundStatistic();
            refundStatistic.setRefundAmount(sumRefundAmount);
        }
        refundStatistic.setRefundAmount(sumRefundAmount);
        refundStatisticService.saveOrUpdate(refundStatistic);
    }


    @Override
    public List<StatisticRefundVO> refundStatisticByMonth(YearMonth yearMonth) {
        List<RefundStatistic> refundStatistics = refundStatisticService.listRefundStatisticByMonth(yearMonth.atDay(1), yearMonth.atEndOfMonth());
        if (CollectionUtils.isEmpty(refundStatistics)){
            return Collections.emptyList();
        }
        return refundStatistics.stream().map(refundStatistic -> {
            StatisticRefundVO statisticRefundVO = new StatisticRefundVO();
            statisticRefundVO.setRefundDate(refundStatistic.getStatisticDate());
            statisticRefundVO.setRefundAmount(DecimalUtil.fromStoredValue(refundStatistic.getRefundAmount()));
            return statisticRefundVO;
        }).collect(Collectors.toList());

    }
}
