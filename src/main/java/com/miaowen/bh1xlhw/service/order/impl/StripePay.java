package com.miaowen.bh1xlhw.service.order.impl;


import com.alibaba.fastjson2.JSONObject;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.enums.RefundStatusEnum;
import com.miaowen.bh1xlhw.model.bo.refund.RefundBO;
import com.miaowen.bh1xlhw.client.stripe.StripeClientConfig;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.service.order.BasePay;
import com.miaowen.bh1xlhw.utils.DecimalUtil;
import com.miaowen.bh1xlhw.utils.IdUtil;
import com.stripe.exception.StripeException;
import com.stripe.model.Refund;
import com.stripe.net.RequestOptions;
import com.stripe.param.RefundCreateParams;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * StripePay :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-29
 */
@Slf4j
@Service("stripe")
@AllArgsConstructor
public class StripePay implements BasePay {
    private final StripeClientConfig stripeClientConfig;
    //以下货币在stripe中不能传分位单位
    public static final Set<String> SPECIAL_CURRENCIES = new HashSet<>(Arrays.asList(
        "BIF", "CLP", "DJF", "GNF", "JPY", "KMF", "KRW", "MGA",
        "PYG", "RWF", "UGX", "VND", "VUV", "XAF", "XOF", "XPF"
    ));

    @Override
    public RefundBO refund(BaseOrder order, BigDecimal amount, String notifyUrl) {
        RequestOptions requestOptions = stripeClientConfig.getRequestOptions(order.getMerchantId());
        String refundTradeNo = IdUtil.generateRefundTradeNo();

        RefundCreateParams.Builder builder = RefundCreateParams.builder()
            .setPaymentIntent(order.getThirdOutTradeNo())
            .putMetadata("refundTradeNo", refundTradeNo);

        if (SPECIAL_CURRENCIES.contains(order.getCurrency())) {
            builder.setAmount((long) DecimalUtil.toStoredValue(amount) / 100);
        } else {
            builder.setAmount((long) DecimalUtil.toStoredValue(amount));
        }

        RefundCreateParams refundCreateParams = builder.build();
        try {
            Refund refund = Refund.create(refundCreateParams, requestOptions);
            RefundBO refundBo = new RefundBO();
            refundBo.setRefundId(refund.getId());
            refundBo.setRefundTradeNo(refundTradeNo);
            refundBo.setStatus(RefundStatusEnum.getByValue(refund.getStatus()).getStatus());
            log.info("stripe 退款请求:{}", JSONObject.toJSONString(refund));
            return refundBo;
        } catch (StripeException e) {
            log.error("stripe 退款 报错:{}", e.getMessage(), e);
            throw new BizException(e.getMessage());
        } catch (Exception e) {
            log.error("stripe退款 未知 报错", e);
            throw new BizException(ResultEnum.REFUND_FAILED);
        }
    }
}
