package com.miaowen.bh1xlhw.service.order.impl;

import com.alibaba.fastjson2.JSONObject;
import com.miaowen.bh1xlhw.client.wechat.WechatPayClient;
import com.miaowen.bh1xlhw.client.wechat.WechatPayClientConfig;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.enums.RefundStatusEnum;
import com.miaowen.bh1xlhw.model.bo.refund.RefundBO;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.model.entity.order.OrderEnvironmental;
import com.miaowen.bh1xlhw.repository.IOrderEnvironmentalService;
import com.miaowen.bh1xlhw.service.order.BasePay;
import com.miaowen.bh1xlhw.utils.DecimalUtil;
import com.miaowen.bh1xlhw.utils.IdUtil;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import com.wechat.pay.java.service.refund.model.Status;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * WechatPay :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-07
 */
@Slf4j
@Service("wechat")
@AllArgsConstructor
public class WechatPay implements BasePay {
    private final WechatPayClientConfig wechatPayClientConfig;

    @Override
    public RefundBO refund(BaseOrder order, BigDecimal amount, String apiSite) {
        WechatPayClient client = wechatPayClientConfig.getClient(order.getMerchantId());
        RSAAutoCertificateConfig rsaAutoCertificateConfig = client.getRsaAutoCertificateConfig();
        String notifyUrl = getNotifyUrl(apiSite, order.getMerchantId());
        RefundService service = new RefundService.Builder().config(rsaAutoCertificateConfig).build();
        CreateRequest createRequest = new CreateRequest();
        AmountReq amountReq = new AmountReq();
        amountReq.setRefund((long)DecimalUtil.toStoredValue(amount));
        amountReq.setTotal(order.getPaymentAmount().longValue());
        amountReq.setCurrency(order.getPaymentCurrency());
        createRequest.setAmount(amountReq);
        String refundTradeNo = IdUtil.generateRefundTradeNo();
        createRequest.setOutTradeNo(order.getOutTradePayNo());
        createRequest.setNotifyUrl(notifyUrl);
        createRequest.setTransactionId(order.getThirdOutTradeNo());
        createRequest.setOutRefundNo(refundTradeNo);
        Refund refund;
        try {
            refund = service.create(createRequest);
        } catch (ServiceException e) {
            String responseBody = e.getResponseBody();
            log.error("微信退款失败,order:{},responseBody:{}", order.getOutTradeNo(), responseBody);
            JSONObject parse = JSONObject.parse(responseBody);
            throw new BizException(parse.getString("message"));
        }
        log.info("微信退款，order:{},refund:{}", order.getOutTradeNo(), refund.toString());
        RefundBO refundBo = new RefundBO();
        refundBo.setRefundId(refund.getRefundId());
        refundBo.setRefundTradeNo(refundTradeNo);
        refundBo.setStatus(Status.SUCCESS.equals(refund.getStatus()) ?  RefundStatusEnum.SUCCEEDED.getStatus(): RefundStatusEnum.PENDING.getStatus());
//        refundBo.setStatus(RefundStatusEnum.SUCCEEDED.getStatus());
        return refundBo;
    }

    private String getNotifyUrl(String apiSite, Integer merchantId) {
        return apiSite + "/api/wechat/pay/notify/" + merchantId;
    }

}
