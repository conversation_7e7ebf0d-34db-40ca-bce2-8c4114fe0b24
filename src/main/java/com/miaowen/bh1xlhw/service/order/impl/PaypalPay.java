package com.miaowen.bh1xlhw.service.order.impl;


import com.miaowen.bh1xlhw.client.paypal.PaypalClientConfig;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.enums.RefundStatusEnum;
import com.miaowen.bh1xlhw.model.bo.refund.RefundBO;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.service.order.BasePay;
import com.miaowen.bh1xlhw.utils.IdUtil;

import com.paypal.sdk.PaypalServerSdkClient;
import com.paypal.sdk.controllers.PaymentsController;
import com.paypal.sdk.http.response.ApiResponse;
import com.paypal.sdk.models.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * PaypalPay :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-29
 */
@Slf4j
@Service("paypal")
@AllArgsConstructor
public class PaypalPay implements BasePay {
    private PaypalClientConfig paypalClientConfig;


    @Override
    public RefundBO refund(BaseOrder order, BigDecimal amount, String notifyUrl) {
        PaypalServerSdkClient client = paypalClientConfig.getClient(order.getMerchantId());
        PaymentsController paymentsController = client.getPaymentsController();

        Money money = new Money();
        money.setCurrencyCode(order.getPaymentCurrency());
        money.setValue(amount.toString());
        RefundRequest refund = new RefundRequest();
        String refundTradeNo = IdUtil.generateRefundTradeNo();
        refund.setInvoiceId(refundTradeNo);
        refund.setAmount(money);
        RefundCapturedPaymentInput capturedPaymentInput = new RefundCapturedPaymentInput();
        capturedPaymentInput.setCaptureId(order.getCaptureId());
        capturedPaymentInput.setBody(refund);
        try {
            ApiResponse<Refund> refundApiResponse = paymentsController.refundCapturedPayment(capturedPaymentInput);
            Refund result = refundApiResponse.getResult();
            RefundBO refundBO = new RefundBO();
            refundBO.setRefundId(result.getId());
            refundBO.setRefundTradeNo(refundTradeNo);
            refundBO.setStatus(RefundStatus.COMPLETED.equals(result.getStatus()) ? RefundStatusEnum.SUCCEEDED.getStatus() : RefundStatusEnum.FAILED.getStatus());
            return refundBO;
        } catch (Exception e) {
            log.error("paypal api报错,message:{}", e.getMessage());
            throw new BizException(e.getMessage());
        }
    }
}
