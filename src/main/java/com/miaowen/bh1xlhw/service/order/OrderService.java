package com.miaowen.bh1xlhw.service.order;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;
import com.miaowen.bh1xlhw.model.query.order.OrderMbtiSubCreateForm;
import com.miaowen.bh1xlhw.model.query.order.RefundForm;
import com.miaowen.bh1xlhw.model.query.order.SendForm;
import com.miaowen.bh1xlhw.model.vo.order.OrderDetailVO;
import com.miaowen.bh1xlhw.model.vo.order.OrderListVO;
import com.miaowen.bh1xlhw.model.vo.order.OrderMbtiSubVO;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025/8/13
 */
public interface OrderService {
    /**
     * 获取交易订单成功表信息列表
     */
    PageVO<OrderListVO> pageSuccessOrder(OrderForm orderForm);

    /**
     * 获取交易订单未支付表信息列表
     */
    PageVO<OrderListVO> pageOrder(OrderForm orderForm);

    /**
     * 获取交易订单详情
     */
    OrderDetailVO orderDetail(Long id);

    /**
     * 退款
     */
    void refund(RefundForm refundForm);

    /**
     * 修改状态
     */
    void changeStatus(Integer id);

    /**
     * 发送邮件
     */
    void sendEmail(SendForm form);


    void changeReadStatus(Integer id);

    PageVO<OrderMbtiSubVO> orderMbtiSubPage(OrderForm orderForm);

    /**
     * 子订单退款
     */
    void mbtiSubRefund(RefundForm refundForm);

    /**
     * 后台创建mbti赠送订单
     */
    void orderMbtiSubCreate(OrderMbtiSubCreateForm orderForm);

    List<OrderMbtiSubVO> orderSubList(Long id);

}
