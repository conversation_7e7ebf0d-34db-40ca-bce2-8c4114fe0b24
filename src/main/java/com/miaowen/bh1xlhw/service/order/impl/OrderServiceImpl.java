package com.miaowen.bh1xlhw.service.order.impl;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.goods.MbtiReportEnum;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsInfoBo;
import com.miaowen.bh1xlhw.model.bo.goods.PromotionGoodsInfoBo;
import com.miaowen.bh1xlhw.model.bo.refund.RefundBO;
import com.miaowen.bh1xlhw.model.entity.Currency;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.entity.order.*;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;
import com.miaowen.bh1xlhw.model.query.order.OrderMbtiSubCreateForm;
import com.miaowen.bh1xlhw.model.query.order.RefundForm;
import com.miaowen.bh1xlhw.model.query.order.SendForm;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationManagerVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationUserVO;
import com.miaowen.bh1xlhw.model.vo.order.*;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email.EmailService;
import com.miaowen.bh1xlhw.service.good.GoodsPromotionService;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.service.order.BasePay;
import com.miaowen.bh1xlhw.service.order.OrderService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DecimalUtil;
import com.miaowen.bh1xlhw.utils.IdUtil;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13
 */
@Slf4j
@Service
@AllArgsConstructor
public class OrderServiceImpl implements OrderService {
    private final IOrderService orderService;
    private final IOrderSuccessService orderSuccessService;
    private final IOrderQuestionResultService orderQuestionResultService;
    private final IOrderEnvironmentalService orderEnvironmentalService;
    private final IPriceService priceService;
    private final Map<String, BasePay> basePayMap;
    private final IRefundRecordService refundRecordService;

    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;
    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;
    private final OperationUserService operationUserService;
    private final EmailService emailService;
    private final ICurrencyService currencyService;
    private final GoodsService goodsService;
    private final GoodsPromotionService goodsPromotionService;
    private final PlatformService platformService;
    private final IPaymentService paymentService;
    private final IMerchantService merchantService;
    private final IOrderMbtiService orderMbtiService;

    @Override
    public PageVO<OrderListVO> pageSuccessOrder(OrderForm orderForm) {
        if (Objects.nonNull(orderForm.getGoodsType())) {
            if (!StringUtils.hasText(orderForm.getGoodsIds())) {
                return new PageVO<>();
            }
        }
        Page<OrderSuccess> orderSuccessPage = orderSuccessService.pageInfo(orderForm);
        return getOrderListVos(orderSuccessPage);
    }

    @Override
    public PageVO<OrderListVO> pageOrder(OrderForm orderForm) {
        if (Objects.nonNull(orderForm.getGoodsType())) {
            if (!StringUtils.hasText(orderForm.getGoodsIds())) {
                return new PageVO<>();
            }
        }
        Page<Order> orderPage = orderService.pageInfo(orderForm);
        return getOrderListVos(orderPage);
    }

    private PageVO<OrderListVO> getOrderListVos(Page<? extends Order> page) {
        List<? extends Order> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>();
        }
        List<Integer> goodsMultilingualIds = new ArrayList<>();
        List<Integer> goodsTraditionalIds = new ArrayList<>();

        List<Integer> operationUserIds = new ArrayList<>();
        List<Integer> paymentIds = new ArrayList<>();
        List<Long> orderIds = new ArrayList<>();
        records.forEach(order -> {
            orderIds.add(order.getId());
            paymentIds.add(order.getPaymentTypeId());
            operationUserIds.add(order.getOperationUserId());
            Integer goodsType = order.getGoodsType();
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
                goodsMultilingualIds.add(order.getGoodsId());
            } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)) {
                goodsTraditionalIds.add(order.getGoodsId());
            }
        });
        List<OrderQuestionResult> orderQuestionResults = orderQuestionResultService.listByIds(orderIds);
        Map<Long, OrderQuestionResult> orderQuestionResultMap = StreamUtil.map(orderQuestionResults, OrderQuestionResult::getId,
                Function.identity());

        List<SystemPayment> payments = paymentService.listByIds(paymentIds);
        Map<Integer, SystemPayment> paymentMap = StreamUtil.map(payments, SystemPayment::getId, Function.identity());
        Map<Integer, OperationUser> operationUserMap = operationUserService.allMap();
        Map<Integer, GoodsMultilingual> goodsMultilingualMap = goodsMultilingualService.mapById(goodsMultilingualIds);
        Set<Integer> goodsTypeMultilingualIds = StreamUtil.fetchSet(goodsMultilingualMap.values(), GoodsMultilingual::getGoodsCategoryId);
        Map<Integer, GoodsCategoryMultilingual> goodsTypeMultilingualMap = goodsCategoryMultilingualService.mapNameByIds(new ArrayList<>(goodsTypeMultilingualIds));
        Map<Integer, GoodsTraditional> goodsTraditionalMap = goodsTraditionalService.mapById(goodsTraditionalIds);
        Set<Integer> goodsTypeTraditionalIds = StreamUtil.fetchSet(goodsTraditionalMap.values(), GoodsTraditional::getGoodsCategoryId);
        Map<Integer, GoodsCategoryTraditional> goodsTypeTraditionalMap = goodsCategoryTraditionalService.mapNameByIds(new ArrayList<>(goodsTypeTraditionalIds));
        Map<String, String> currencyMap = currencyService.mapAll();
        List<OrderListVO> orderListVoList = records.stream().map(order -> {

            OrderListVO orderListVO = BeanUtils.copy(order, OrderListVO.class);
            orderListVO.setSymbol(currencyMap.get(order.getCurrency()));
            orderListVO.setCurrency(order.getCurrency());
            orderListVO.setPaymentAmount(DecimalUtil.fromStoredValue(order.getPaymentAmount()));
            orderListVO.setOriginalAmount(DecimalUtil.fromStoredValue(order.getOriginalAmount()));
            orderListVO.setRedAmount(DecimalUtil.fromStoredValue(Math.max(order.getOriginalAmount() - order.getAmount(), 0)));
            orderListVO.setUsdAmount(DecimalUtil.fromStoredValue(order.getUsdAmount()));
            orderListVO.setRefundAmount(DecimalUtil.fromStoredValue(order.getRefundAmount()));
            int leftAmount = order.getPaymentAmount() - order.getRefundAmount();
            orderListVO.setLeftAmount(DecimalUtil.fromStoredValue(leftAmount));
            orderListVO.setPartialRefund((order.getRefundAmount() > 0 && leftAmount > 0) ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());
            SystemPayment systemPayment = paymentMap.get(order.getPaymentTypeId());
            if (Objects.nonNull(systemPayment)) {
                orderListVO.setPaymentCode(systemPayment.getName());
            }
            orderListVO.setId(order.getId());
            if (orderQuestionResultMap.containsKey(order.getId())) {
                OrderQuestionResult orderQuestionResult = orderQuestionResultMap.get(order.getId());
                orderListVO.setExerciseTime(getExamTime(orderQuestionResult.getCompletionTime()));
            }
            Integer goodsType = order.getGoodsType();
            Integer goodsId = order.getGoodsId();
            Integer goodsPromotionId = order.getGoodsPromotionId();
            OperationUser operationUser = operationUserMap.get(order.getOperationUserId());

            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
                GoodsMultilingual goodsMultilingual = goodsMultilingualMap.get(goodsId);
                if (Objects.nonNull(goodsMultilingual)) {
                    GoodsCategoryMultilingual goodsCategoryMultilingual = goodsTypeMultilingualMap.get(goodsMultilingual.getGoodsCategoryId());
                    orderListVO.setGoodsCategoryName(goodsCategoryMultilingual.getName());
                    orderListVO.setGoodsCategoryId(goodsCategoryMultilingual.getId());
                    orderListVO.setGoodsName(goodsMultilingual.getName());
                    if (Objects.nonNull(goodsPromotionId) && !goodsPromotionId.equals(0)) {
                        orderListVO.setPromotionId(goodsPromotionId);
                    }
                }

            } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)) {
                GoodsTraditional goodsTraditional = goodsTraditionalMap.get(goodsId);
                if (Objects.nonNull(goodsTraditional)) {
                    GoodsCategoryTraditional goodsCategoryTraditional = goodsTypeTraditionalMap.get(goodsTraditional.getGoodsCategoryId());
                    orderListVO.setGoodsCategoryName(goodsCategoryTraditional.getName());
                    orderListVO.setGoodsCategoryId(goodsCategoryTraditional.getId());
                    orderListVO.setGoodsName(goodsTraditional.getName());
                    if (Objects.nonNull(goodsPromotionId) && !goodsPromotionId.equals(0)) {
                        orderListVO.setPromotionId(goodsPromotionId);
                    }
                }
            }
            if (Objects.nonNull(operationUser)) {
                orderListVO.setOperatorUser(BeanUtils.copy(operationUser, OperationUserVO.class));
                Integer pid = operationUser.getPid();
                OperationUser operationManager = operationUserMap.get(pid);
                orderListVO.setOperationManagerVO(BeanUtils.copy(operationManager, OperationManagerVO.class));
            }

            return orderListVO;
        }).collect(Collectors.toList());

        return new PageVO<OrderListVO>(page).convert(orderListVoList);
    }

    @Override
    public OrderDetailVO orderDetail(Long id) {
        Order order = orderService.getById(id);
        OrderEnvironmental orderEnvironmental = orderEnvironmentalService.getById(id);
        OrderQuestionResult orderQuestionResult = orderQuestionResultService.getById(id);
        OrderGoodsVO orderGoodsVo = BeanUtils.copy(order, OrderGoodsVO.class);

        orderGoodsVo.setGoodsType(order.getGoodsType());
        //
        Map<String, String> currencyMap = currencyService.mapAll();
        String tgId = order.getTgId();
        Integer goodsId = order.getGoodsId();
        GoodsInfoBo goodsInfo = goodsService.getGoodsInfo(goodsId, order.getGoodsType());


        if (BaseOrder.PayStatusEnum.SUCCESS.getValue().equals(order.getPaymentStatus())) {
            String reportUrlSj = orderEnvironmental.getWebSite() +
                    "/" +
                    goodsInfo.getWebPackageName() +
                    "/admin-result?orderNo=" +
                    order.getOutTradeNo();
            orderGoodsVo.setResultUrl(reportUrlSj);
        }

        Integer priceId;
        PromotionGoodsInfoBo promotionGoodsInfo = null;
        if (StringUtils.hasText(tgId)) {
            promotionGoodsInfo = goodsPromotionService.getPromotionGoodsInfo(tgId, order.getGoodsType());
            if (Objects.nonNull(promotionGoodsInfo)) {
                orderGoodsVo.setPromotionGoodsId(promotionGoodsInfo.getId());
            }
        }
        if (Objects.nonNull(promotionGoodsInfo)) {
            orderGoodsVo.setTgId(promotionGoodsInfo.getTgId());
            orderGoodsVo.setPriceName(promotionGoodsInfo.getTgId());
            priceId = promotionGoodsInfo.getPriceId();
            Integer platformId = promotionGoodsInfo.getPlatformId();
            Integer operationId = order.getOperationUserId();
            OperationUser operationUser = operationUserService.getById(operationId);
            OperationUser operationUserManager =
                Optional.ofNullable(operationUserService.getById(operationUser.getPid())).orElse(operationUser);
            Platform platform = platformService.getExistById(platformId);
            orderGoodsVo.setPlatform(BeanUtils.copy(platform, PlatformVO.class));
            orderGoodsVo.setOperationManager(BeanUtils.copy(operationUserManager, OperationManagerVO.class));
            orderGoodsVo.setOperationUser(BeanUtils.copy(operationUser, OperationUserVO.class));
        } else {
            priceId = goodsInfo.getPriceId();
        }

        Price price = priceService.getById(priceId);
        if (Objects.nonNull(price)) {
            orderGoodsVo.setPriceId(priceId);
            orderGoodsVo.setPriceName(price.getName());
        }
        if (Objects.nonNull(goodsInfo)) {
            orderGoodsVo.setGoodsName(goodsInfo.getName());
            orderGoodsVo.setGoodsCategoryId(goodsInfo.getGoodsCategoryId());
        }

        orderGoodsVo.setOrderId(order.getId());
        orderGoodsVo.setOrderAmount(DecimalUtil.fromStoredValue(order.getPaymentAmount()).toString());
        orderGoodsVo.setIsPromotionOrder(!BooleanEnum.FALSE.getValue().equals(order.getGoodsPromotionId()));
        orderGoodsVo.setResultAnswer(orderQuestionResult.getResultAnswer());
        orderGoodsVo.setCompletionTime(getExamTime(orderQuestionResult.getCompletionTime()));
        orderGoodsVo.setReturnType(order.getIsEmail());
        PaymentInfoVO paymentInfoVO = BeanUtils.copy(order, PaymentInfoVO.class);
        paymentInfoVO.setAmount(DecimalUtil.fromStoredValue(order.getAmount()));
        paymentInfoVO.setSymbol(currencyMap.get(paymentInfoVO.getCurrency()));
        paymentInfoVO.setLevel(order.getPriceLevel());
        paymentInfoVO.setPaymentAmount(DecimalUtil.fromStoredValue(order.getPaymentAmount()));
        paymentInfoVO.setOriginalAmount(DecimalUtil.fromStoredValue(order.getOriginalAmount()));
        paymentInfoVO.setRedAmount(DecimalUtil.fromStoredValue(order.getOriginalAmount() - order.getPaymentAmount()));
        paymentInfoVO.setRefundAmount(DecimalUtil.fromStoredValue(order.getRefundAmount()));
        int leftAmount = order.getPaymentAmount() - order.getRefundAmount();
        paymentInfoVO.setPartialRefund((order.getRefundAmount() > 0 && leftAmount > 0) ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());
        paymentInfoVO.setThirdEmail(order.getThirdEmail());
        paymentInfoVO.setEmail(order.getEmail());
        paymentInfoVO.setCardNumber(order.getCardNumber());
        Integer paymentTypeId = order.getPaymentTypeId();
        if (Objects.nonNull(paymentTypeId) && paymentTypeId != 0) {
            SystemPayment poSystemPayment = paymentService.getById(paymentTypeId);
            if (Objects.nonNull(poSystemPayment)) {
                paymentInfoVO.setPaymentName(poSystemPayment.getName());
                Integer merchantId = poSystemPayment.getMerchantId();
                Merchant merchant = merchantService.getMerchantById(merchantId);
                if (Objects.nonNull(merchant)) {
                    paymentInfoVO.setMerchantName(merchant.getName());
                }
            }
        }
        PaymentEnvInfoVO paymentEnvInfoVO = BeanUtils.copy(orderEnvironmental, PaymentEnvInfoVO.class);
        paymentEnvInfoVO.setPaymentEnv(order.getPaymentEnv());
        OrderDetailVO orderDetailVo = new OrderDetailVO();
        orderDetailVo.setOrderGoodsVO(orderGoodsVo);
        orderDetailVo.setPaymentInfoVO(paymentInfoVO);
        orderDetailVo.setPaymentEnvInfoVO(paymentEnvInfoVO);
        return orderDetailVo;
    }

    @Override
    public void refund(RefundForm refundForm) {
        Long orderId = refundForm.getOrderId();
        Order order = orderService.getById(orderId);
        OrderEnvironmental orderEnvironmental = orderEnvironmentalService.getById(orderId);
        RefundBO refund = dealRefund(refundForm.getAmount(), order, orderEnvironmental.getApiSite());
        Integer sumRefundAmount = refundRecordService.sumRefundRecord(refundForm.getOrderId());
        orderService.dealRefundRecord(sumRefundAmount, refund.getStatus(), order);
    }


    @Override
    public void mbtiSubRefund(RefundForm refundForm) {
        Long orderId = refundForm.getOrderId();
        OrderMbti order = orderMbtiService.getById(orderId);
        //子订单全额退款
        OrderEnvironmental orderEnvironmental = orderEnvironmentalService.getById(order.getMainOrderId());

        RefundBO refund = dealRefund(DecimalUtil.fromStoredValue(order.getPaymentAmount()), order, orderEnvironmental.getApiSite());
        Integer sumRefundAmount = refundRecordService.sumRefundRecord(refundForm.getOrderId());
        orderMbtiService.dealRefundRecord(sumRefundAmount, refund.getStatus(), order);
    }

    @Override
    public void orderMbtiSubCreate(OrderMbtiSubCreateForm orderForm) {
        Order order = orderService.getByOutTradeNo(orderForm.getMainOutTradeNo());
        if (Objects.isNull(order)) {
            throw new BizException(ResultEnum.ORDER_NOT_EXIST);
        }
        List<String> mbtiReportList = orderForm.getMbtiReportList();
        List<Integer> contentTypeList = mbtiReportList.stream().map(mbtiReportString -> {
            MbtiReportEnum reportEnum = MbtiReportEnum.getByValue(mbtiReportString);
            if (Objects.nonNull(reportEnum)) {
                return reportEnum.getContentType();
            } else {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(contentTypeList)) {
            throw new BizException(ResultEnum.MBTI_ORDER_REPORT_NOT_EXIST);
        }
        List<OrderMbti> orderMbtiList = orderMbtiService.listByMainOutTradeNoType(orderForm.getMainOutTradeNo(), contentTypeList);

        //移除 已赠送已购买已退款的
        Set<Integer> existSet = orderMbtiList.stream().map(OrderMbti::getType).collect(Collectors.toSet());
        contentTypeList.removeIf(existSet::contains);

        if (CollectionUtils.isEmpty(existSet)) {
            return;
        }
        String mbti;
        OrderQuestionResult orderQuestionResult = getOrderQuestionResult(order.getId());
        String score = orderQuestionResult.getScore();
        String[] split = score.split("-");
        if (split.length > 0) {
            mbti = split[0];
        } else {
            mbti = "";
        }

        List<OrderMbti> newOrderMbtiList = contentTypeList.stream().map(contentType -> buildSubOrder(contentType, order, mbti)).collect(Collectors.toList());
        orderMbtiService.saveBatch(newOrderMbtiList);
    }

    @Override
    public List<OrderMbtiSubVO> orderSubList(Long id) {
        Order order = orderService.getById(id);
        if (Objects.isNull(order)){
            return Collections.emptyList();
        }

        List<OrderMbti> orderMbtiList = orderMbtiService.listByMainOutTradeNo(order.getOutTradeNo());
        if (CollectionUtils.isEmpty(orderMbtiList)) {
            return Collections.emptyList();
        }
        return getOrderMbtiSubList(orderMbtiList);
    }

    private OrderMbti buildSubOrder(Integer contentType, Order mainOrder, String mbti) {
        OrderMbti order = new OrderMbti();
        String outTradeNo = IdUtil.generateMbtiOutTradeNo();
        order.setOutTradeNo(outTradeNo);
        order.setMbti(mbti);
        order.setPaymentTypeId(0);
        order.setAmount(0);
        order.setMainOrderId(mainOrder.getId());
        order.setMainOutTradeNo(mainOrder.getOutTradeNo());
        order.setPaymentStatus(Order.PayStatusEnum.ADMIN_SUCCESS.getValue());
        order.setType(contentType);
        order.setMerchantId(0);
        order.setGoodsId(mainOrder.getGoodsId());
        order.setGoodsType(mainOrder.getGoodsType());
        return order;
    }

    private OrderQuestionResult getOrderQuestionResult(Long orderId) {
        OrderQuestionResult orderQuestionResult = orderQuestionResultService.getById(orderId);

        if (Objects.isNull(orderQuestionResult)) {
            throw new BizException(ResultEnum.ORDER_NOT_EXIST);
        }
        return orderQuestionResult;
    }

    @NotNull
    private RefundBO dealRefund(BigDecimal amount, BaseOrder order, String apiSite) {
        Currency currency = checkOrderRefund(order);
        Integer degreeAccuracy = currency.getDegreeAccuracy();
        DecimalUtil.validateAmountPrecision(amount, degreeAccuracy);
        BasePay basePay = basePayMap.get(order.getMerchantTypeCode());
        RefundBO refund = basePay.refund(order, amount, apiSite);
        RefundRecord refundRecord = new RefundRecord();
        refundRecord.setOrderId(order.getId());
        refundRecord.setOutTradeNo(order.getOutTradeNo());
        refundRecord.setUsdExchangeRate(order.getUsdExchangeRate());
        refundRecord.setRefundTradeNo(refund.getRefundTradeNo());
        refundRecord.setThirdRefundTradeNo(refund.getRefundId());
        refundRecord.setStatus(refund.getStatus());
        refundRecord.setAmount(DecimalUtil.toStoredValue(amount));
        refundRecord.setCurrency(order.getCurrency());
        refundRecordService.save(refundRecord);
        return refund;
    }

    private Currency checkOrderRefund(BaseOrder orderMbti) {
        if (Objects.isNull(orderMbti)) {
            throw new BizException(ResultEnum.ORDER_NOT_EXIST);
        }
        Currency currency = currencyService.getByCurrency(orderMbti.getCurrency());
        if (orderMbti.getRefundAmount() >= orderMbti.getPaymentAmount()) {
            throw new BizException(ResultEnum.REFUND_FINISH);
        }
        return currency;
    }

    @Override
    public void changeStatus(Integer id) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, id);
        updateWrapper.set(Order::getPaymentStatus, BaseOrder.PayStatusEnum.ADMIN_SUCCESS.getValue());
        updateWrapper.set(Order::getPaymentDate, LocalDate.now());
        updateWrapper.set(Order::getPaymentTime, LocalDateTime.now());
        orderService.update(updateWrapper);
        Order order = orderService.getById(id);
        OrderSuccess orderSuccess = BeanUtils.copy(order, OrderSuccess.class);
        orderSuccessService.save(orderSuccess);
    }

    @Override
    public void changeReadStatus(Integer id) {
        LambdaUpdateWrapper<Order> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Order::getId, id);
        updateWrapper.set(Order::getIsReadResult, 1);
        orderService.update(updateWrapper);
    }

    @Override
    public PageVO<OrderMbtiSubVO> orderMbtiSubPage(OrderForm orderForm) {
        //获取商品id
        if (Objects.nonNull(orderForm.getGoodsType())) {
            if (!StringUtils.hasText(orderForm.getGoodsIds())) {
                return new PageVO<>();
            }
        }
        Page<OrderMbti> orderPage = orderMbtiService.pageInfo(orderForm);
        List<OrderMbti> records = orderPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>();
        }

        List<OrderMbtiSubVO> orderMbtiSubVoList = getOrderMbtiSubList(records);
        return new PageVO<OrderMbtiSubVO>(orderPage).convert(orderMbtiSubVoList);
    }


    private List<OrderMbtiSubVO> getOrderMbtiSubList(List<OrderMbti> records) {
        List<Integer> goodsMultilingualIds = new ArrayList<>();
        List<Integer> goodsTraditionalIds = new ArrayList<>();
        List<Long> mainOrderIds = new ArrayList<>();

        List<Integer> paymentIds = new ArrayList<>();
        records.forEach(order -> {
            mainOrderIds.add(order.getMainOrderId());
            paymentIds.add(order.getPaymentTypeId());
            Integer goodsType = order.getGoodsType();
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
                goodsMultilingualIds.add(order.getGoodsId());
            } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)) {
                goodsTraditionalIds.add(order.getGoodsId());
            }
        });
        List<OrderEnvironmental> orderEnvironmentalList = orderEnvironmentalService.listByIds(mainOrderIds);
        Map<Long, OrderEnvironmental> orderEnvironmentalMap = StreamUtil.map(orderEnvironmentalList,
                OrderEnvironmental::getId, Function.identity());


        List<SystemPayment> payments = paymentService.listByIds(paymentIds);
        Map<Integer, SystemPayment> paymentMap = StreamUtil.map(payments, SystemPayment::getId, Function.identity());

        Map<Integer, GoodsMultilingual> goodsMultilingualMap = goodsMultilingualService.mapById(goodsMultilingualIds);
        Map<Integer, GoodsTraditional> goodsTraditionalMap = goodsTraditionalService.mapById(goodsTraditionalIds);
        Map<String, String> currencyMap = currencyService.mapAll();
        return records.stream().map(order -> {
            OrderMbtiSubVO orderMbtiSubVo = BeanUtils.copy(order, OrderMbtiSubVO.class);
            orderMbtiSubVo.setSymbol(currencyMap.get(order.getCurrency()));
            orderMbtiSubVo.setMbtiReport(MbtiReportEnum.getByContentType(order.getType()).getValue());
            orderMbtiSubVo.setPaymentAmount(DecimalUtil.fromStoredValue(order.getPaymentAmount()));
            orderMbtiSubVo.setUsdAmount(DecimalUtil.fromStoredValue(order.getUsdAmount()));
            int leftAmount = order.getPaymentAmount() - order.getRefundAmount();
            orderMbtiSubVo.setLeftAmount(DecimalUtil.fromStoredValue(leftAmount));


            orderMbtiSubVo.setId(order.getId());
            Integer goodsType = order.getGoodsType();
            Integer goodsId = order.getGoodsId();
            String packageName = "";
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
                GoodsMultilingual goodsMultilingual = goodsMultilingualMap.get(goodsId);
                if (Objects.nonNull(goodsMultilingual)) {
                    orderMbtiSubVo.setGoodsName(goodsMultilingual.getName());
                    packageName = goodsMultilingual.getWebPackageName();
                }

            } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)) {
                GoodsTraditional goodsTraditional = goodsTraditionalMap.get(goodsId);
                if (Objects.nonNull(goodsTraditional)) {
                    orderMbtiSubVo.setGoodsName(goodsTraditional.getName());
                    packageName = goodsTraditional.getWebPackageName();
                }
            }
            OrderEnvironmental orderEnvironmental = orderEnvironmentalMap.get(order.getMainOrderId());
            if (BaseOrder.PayStatusEnum.SUCCESS.getValue().equals(order.getPaymentStatus()) && Objects.nonNull(orderEnvironmental)) {
                Integer type = order.getType();
                MbtiReportEnum mbtiReportEnum = MbtiReportEnum.getByContentType(type);
                if (Objects.nonNull(mbtiReportEnum)) {
                    String reportUrlSj = orderEnvironmental.getWebSite() +
                        "/" +
                        packageName +
                        "/"
                        + mbtiReportEnum.getWebPage()
                        + "?orderNo=" +
                        order.getOutTradeNo()
                        + "?content_type=" + type;
                    orderMbtiSubVo.setResultUrl(reportUrlSj);
                }

            }

            orderMbtiSubVo.setPartialRefund((order.getRefundAmount() > 0 && leftAmount > 0) ? BooleanEnum.TRUE.getValue() : BooleanEnum.FALSE.getValue());
            orderMbtiSubVo.setRefundAmount(DecimalUtil.fromStoredValue(order.getRefundAmount()));

            SystemPayment systemPayment = paymentMap.get(order.getPaymentTypeId());
            if (Objects.nonNull(systemPayment)) {
                orderMbtiSubVo.setPaymentCode(systemPayment.getName());
            }

            return orderMbtiSubVo;
        }).collect(Collectors.toList());
    }

    @Override
    public void sendEmail(SendForm form) {
        if (StringUtils.isEmpty(form.getEmail())) {
            return;
        }
        Order order = orderService.getById(form.getId());
        //只有支付成功的可以发送报告
        if (!Objects.equals(order.getPaymentStatus(), BaseOrder.PayStatusEnum.SUCCESS.getValue())) {
            return;
        }
        OrderEnvironmental environmental = orderEnvironmentalService.getById(form.getId());
        emailService.sendReport(order.getPaymentStatus(), order, environmental, form.getEmail());
    }


    private String getExamTime(Integer completionTime) {
        String result = "";
        if (completionTime == null || completionTime == 0) {
            return result;
        }
        int days = completionTime / 86400;
        int remainingAfterDays = completionTime % 86400;
        int hours = remainingAfterDays / 3600;
        int remainingSeconds = remainingAfterDays % 3600;
        int minutes = remainingSeconds / 60;
        int seconds = remainingSeconds % 60;

        if (days > 0) {
            return String.format("%d天%d小时%02d分%02d秒", days, hours, minutes, seconds);
        } else if (hours > 0) {
            return String.format("%d小时%02d分%02d秒", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%d分%02d秒", minutes, seconds);
        } else {
            return String.format("%d秒", seconds);
        }

    }

}
