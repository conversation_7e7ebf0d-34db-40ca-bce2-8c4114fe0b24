package com.miaowen.bh1xlhw.service.good.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.goods.AdvertiseAccountForm;
import com.miaowen.bh1xlhw.model.query.goods.AdvertisePageForm;
import com.miaowen.bh1xlhw.model.vo.goods.AdvertiseAccountGoodLinkVO;
import com.miaowen.bh1xlhw.model.vo.goods.AdvertiseAccountVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsLinkVO;
import com.miaowen.bh1xlhw.model.vo.operation.*;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.good.AdvertiseAccountInfoService;
import com.miaowen.bh1xlhw.service.good.GoodsPromotionService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * AdvertiseAccountInfoServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
@Slf4j
@Service
@AllArgsConstructor
public class AdvertiseAccountInfoServiceImpl implements AdvertiseAccountInfoService {
    private final AdvertiseAccountService advertiseAccountService;
    private final OperationManagerService operationManagerService;
    private final OperationUserService operationUserService;
    private final PlatformService platformService;
    private final IPriceService priceService;
    private final GoodsPromotionMultilingualService goodsPromotionMultilingualService;
    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsPromotionTraditionalService goodsPromotionTraditionalService;
    private final IGoodsTraditionalService goodsTraditionalService;
    private final LanguageContextConfigService languageContextConfigService;
    private final DomainService domainService;
    private final AgentsService agentsService;

    @Override
    public void saveAdvertiseAccount(AdvertiseAccountForm advertiseAccountForm) {
        AdvertiseAccount advertiseAccount = BeanUtils.copy(advertiseAccountForm, AdvertiseAccount.class);
        advertiseAccountService.save(advertiseAccount);
    }

    @Override
    public void updateAdvertiseAccount(Integer id, AdvertiseAccountForm advertiseAccountForm) {
        AdvertiseAccount advertiseAccount = BeanUtils.copy(advertiseAccountForm, AdvertiseAccount.class);
        advertiseAccount.setId(id);
        advertiseAccountService.updateById(advertiseAccount);
    }

    @Override
    public PageVO<AdvertiseAccountVO> pageAdvertiseAccount(AdvertisePageForm pageForm) {
        Page<AdvertiseAccount> advertiseAccountPage = advertiseAccountService.pageInfo(pageForm);
        List<AdvertiseAccount> records = advertiseAccountPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>();
        }
        List<AdvertiseAccountVO> accountVos = getAdvertiseAccountVos(records);
        return new PageVO<AdvertiseAccountVO>(advertiseAccountPage).convert(accountVos);
    }

    private List<AdvertiseAccountVO> getAdvertiseAccountVos(List<AdvertiseAccount> records) {
        List<Integer> operationUserIds = new ArrayList<>();
        List<Integer> priceIds = new ArrayList<>();
        List<Integer> platformIds = new ArrayList<>();
        List<Integer> agentIds = new ArrayList<>();
        records.forEach(record -> {
            platformIds.add(record.getPlatformId());
            operationUserIds.add(record.getOperationId());
            priceIds.add(record.getPriceId());
            agentIds.add(record.getAgentId());
        });

        Map<Integer, OperationUser> operationUserMap = operationUserService.mapNameByIds(operationUserIds);
        Set<Integer> operationManagerIds = StreamUtil.fetchSet(operationUserMap.values(), OperationUser::getPid);
        Map<Integer, OperationManager> operationManagerMap = operationManagerService.mapNameByIds(operationManagerIds);
        Map<Integer, Platform> platformMap = platformService.mapByIds(platformIds);
        Map<Integer, Price> priceMap = priceService.mapByIds(priceIds);
        Map<Integer, Agents> agentsMap = agentsService.mapNameByIds(agentIds);

        return records.stream().map(advertiseAccount -> {
            Price price = priceMap.get(advertiseAccount.getPriceId());
            Platform platform = platformMap.get(advertiseAccount.getPlatformId());
            AdvertiseAccountVO advertiseAccountVO = BeanUtils.copy(advertiseAccount, AdvertiseAccountVO.class);
            advertiseAccountVO.setPrice(PriceVO.transformVo(price));
            if (operationUserMap.containsKey(advertiseAccount.getOperationId())
                    && Objects.nonNull(operationUserMap.get(advertiseAccount.getOperationId()))) {
                OperationUser operationUser = operationUserMap.get(advertiseAccount.getOperationId());
                advertiseAccountVO.setOperationUser(BeanUtils.copy(operationUser, OperationUserVO.class));
                if (operationManagerMap.containsKey(operationUser.getPid())) {
                    OperationManager operationManager = operationManagerMap.get(operationUser.getPid());
                    advertiseAccountVO.setOperationManagerVo(BeanUtils.copy(operationManager, OperationManagerVO.class));
                }
            }
            if (agentsMap.containsKey(advertiseAccount.getAgentId())
                    && Objects.nonNull(agentsMap.get(advertiseAccount.getAgentId()))) {
                Agents agent = agentsMap.get(advertiseAccount.getAgentId());
                advertiseAccountVO.setAgent(BeanUtils.copy(agent, AgentsVO.class));
                advertiseAccountVO.setAgentsVO(BeanUtils.copy(agent, AgentsVO.class));
            }
            advertiseAccountVO.setPlatformVO(BeanUtils.copy(platform, PlatformVO.class));
            return advertiseAccountVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AdvertiseAccountVO> allAdvertiseAccount() {
        List<AdvertiseAccount> records = advertiseAccountService.allList();
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return getAdvertiseAccountVos(records);
    }

    @Override
    public void deleteAdvertiseAccount(Integer id) {
        advertiseAccountService.logicRemoveById(id);
    }

    @Override
    public AdvertiseAccountGoodLinkVO listAdvertiseAccountTgLink(Integer id) {
        List<GoodsPromotionMultilingual> multilingualList = goodsPromotionMultilingualService.listByAdvertiseAccountId(id);
        List<GoodsPromotionTraditional> traditionalList = goodsPromotionTraditionalService.listByAdvertiseAccountId(id);
        List<Integer> multilingualGoodsIds = StreamUtil.fetchList(multilingualList, GoodsPromotionMultilingual::getGoodsId);
        List<Integer> traditionalGoodsIds = StreamUtil.fetchList(traditionalList, GoodsPromotionTraditional::getGoodsId);

        Map<Integer, Platform> platformMap = platformService.mapAll();
        Map<Integer, Domain> domainMap = domainService.mapAll();

        Map<Integer, GoodsMultilingual> goodsMultilingualMap = goodsMultilingualService.mapNameByIds(multilingualGoodsIds);
        Map<Integer, GoodsTraditional> goodsTraditionalMap = goodsTraditionalService.mapNameByIds(traditionalGoodsIds);


        Map<Integer, List<LanguageContextConfig>> languageContextMap = languageContextConfigService.mapByRelationIds(LanguageContextConfig.RelationType.GOODS_MULTILINGUAL, multilingualGoodsIds);
        AdvertiseAccountGoodLinkVO advertiseAccountGoodLinkVO = new AdvertiseAccountGoodLinkVO();
        List<GoodsLinkVO> multilingualGoodsLinkVos = multilingualList.stream().map(multilingual -> {
            Domain domain = domainMap.get(multilingual.getDomainId());
            GoodsMultilingual goodsMultilingual = goodsMultilingualMap.get(multilingual.getGoodsId());
            Platform platform = platformMap.get(multilingual.getPlatformId());
            List<LanguageContextConfig> languageContextConfigs = languageContextMap.get(multilingual.getGoodsId());
            List<String> linkList = GoodsPromotionService.getMultilingualLinkList(multilingual, goodsMultilingual, domain, platform, languageContextConfigs);
            GoodsLinkVO goodsLinkVO = new GoodsLinkVO();
            if (Objects.nonNull(goodsMultilingual)){
                goodsLinkVO.setName(goodsMultilingual.getName());
            }
            goodsLinkVO.setLink(linkList);
            return goodsLinkVO;
        }).collect(Collectors.toList());

        List<GoodsLinkVO> traditionalGoodsLinkVos = traditionalList.stream().map(traditional -> {
            Domain domain = domainMap.get(traditional.getDomainId());
            GoodsTraditional goodsTraditional = goodsTraditionalMap.get(traditional.getGoodsId());
            Platform platform = platformMap.get(traditional.getPlatformId());
            List<String> linkList = GoodsPromotionService.getTraditionalLinkList(traditional, goodsTraditional, domain, platform);
            GoodsLinkVO goodsLinkVO = new GoodsLinkVO();
            goodsLinkVO.setName(goodsTraditional.getName());
            goodsLinkVO.setLink(linkList);
            return goodsLinkVO;
        }).collect(Collectors.toList());


        advertiseAccountGoodLinkVO.setMultilingualLinkList(multilingualGoodsLinkVos);
        advertiseAccountGoodLinkVO.setTraditionalLinkList(traditionalGoodsLinkVos);
        return advertiseAccountGoodLinkVO;
    }

    @Override
    public List<AdvertiseAccountVO> getListByOperationUser(String workNo) {
        OperationUser operationUser = operationUserService.getByUserCode(workNo);
        if (Objects.isNull(operationUser)) {
            return Collections.emptyList();
        }
        List<AdvertiseAccount> records = advertiseAccountService.listByOperationId(operationUser.getId());
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return getAdvertiseAccountVos(records);
    }
}
