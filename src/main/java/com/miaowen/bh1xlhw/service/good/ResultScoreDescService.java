package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.goods.ResultScoreDetailForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultScoreForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 18:01
 */
public interface ResultScoreDescService {

    public PageVO<ResultScoreForm> getResultScoreForm(String tag, Integer pageInt, Integer pageSize, boolean isMultiLanguage);

    public ResultScoreDetailForm getResultScoreDetailForm(String tag,Integer id, boolean isMultiLanguage);

    public void saveResultScoreForm(ResultScoreDetailForm form, boolean isMultiLanguage);

    public void deleteResultScoreForm(List<Integer> ids, boolean isMultiLanguage);


}
