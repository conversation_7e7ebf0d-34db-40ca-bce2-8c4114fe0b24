package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsInfoBo;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsTraditionalVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsMultilingualVO;

import java.util.List;

/**
 * GoodService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09
 */
public interface GoodsService {
    /// -------------- 多语言 ---------------
    /**
     * 保存多语言商品信息
     */
    void saveGoodsMultilingual(GoodsMultilingualForm goodsMultilingualForm);

    /**
     * 修改多语言商品信息
     */
    void updateGoodsMultilingual(Integer id, GoodsMultilingualForm goodsMultilingualForm);

    /**
     * 获取多语言商品信息列表
     */
    PageVO<GoodsMultilingualVO> pageGoodsMultilingual(GoodsPageForm pageForm);

    /**
     * 获取多语言商品信息列表全部
     */
    List<GoodsMultilingualVO> listGoodsMultilingual();
    /**
     * 删除多语言商品信息
     */
    void deleteGoodsMultilingual(Integer id);


    /// -------------- 繁体 ---------------
    /**
     * 保存繁体商品信息
     */
    void saveGoodsTraditional(GoodsTraditionalForm goodsTraditionalForm);

    /**
     * 修改繁体商品信息
     */
    void updateGoodsTraditional(Integer id, GoodsTraditionalForm goodsTraditionalForm);

    /**
     * 获取繁体商品信息列表
     */
    PageVO<GoodsTraditionalVO> pageGoodsTraditional(GoodsPageForm pageForm);

    /**
     * 获取繁体商品信息列表全部
     */
    List<GoodsTraditionalVO> listGoodsTraditional();

    /**
     * 删除繁体商品信息
     */
    void deleteGoodsTraditional(Integer id);

    /**
     * 根据商品id和商品类型获取价格id
     */
    GoodsInfoBo getGoodsInfo(Integer goodsId, Integer goodsType);

    /**
     * 根据商品id和商品类型获取商品类型
     */
    String getGoodsCategory(Integer goodsTypeId, Integer goodsType);

    /**
     * 根据商品id和商品类型获取商品名称
     */
    String getGoodsName(Integer goodsId, Integer goodsType, String languageCode);
    /**
     * 模糊查询商品
     */
    List<Integer> getIdsByGoodsName(String goodsName, Integer goodsType, Integer productId);


    void recoverRecyclesGoodsMultilingual(DeleteBatchForm deleteBatchForm);

    void deleteRecyclesGoodsMultilingual(DeleteBatchForm deleteBatchForm);

    void deleteRecyclesGoodsTraditional(DeleteBatchForm deleteBatchForm);

    void recoverRecyclesGoodsTraditional(DeleteBatchForm deleteBatchForm);

}
