package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.model.query.goods.ResultMBTIPriceForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultMBTIWeiduForm;

import java.util.List;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/6/5 15:47
 */
public interface ResultSettingMBTIService {

    void saveOrUpdate(ResultMBTIWeiduForm form);

    List<ResultMBTIWeiduForm> listAll(Integer version);

    void delete(Integer id);

    void saveOrUpdatePrice(ResultMBTIPriceForm form);

    ResultMBTIPriceForm listAllPrice();

}
