package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.model.query.goods.ScoreManagementForm;
import com.miaowen.bh1xlhw.model.vo.goods.ScoreManagementVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/6/4 19:14
 */
public interface ScoreManagementService {

    void save(ScoreManagementForm form, boolean isMulti);

    ScoreManagementForm upload(MultipartFile file, String tag, boolean isMulti) throws IOException;

    ScoreManagementVo getScoreManagement(String tag,boolean isMulti);

    void export(HttpServletResponse response, String tag, Integer version, boolean isMulti) throws IOException;
}
