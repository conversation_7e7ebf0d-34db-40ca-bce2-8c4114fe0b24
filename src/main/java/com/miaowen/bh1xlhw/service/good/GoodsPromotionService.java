package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import com.miaowen.bh1xlhw.model.bo.goods.PromotionGoodsInfoBo;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsPromotionMultilingualVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsPromotionTraditionalVO;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;

import java.util.*;

/**
 * GoodsPromotionService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
public interface GoodsPromotionService {

    /**
     * 保存推广商品信息多语言
     */
    void saveGoodPromotionMultilingual(GoodsPromotionMultilingualForm goodsPromotionMultilingualForm);

    /**
     * 修改推广商品信息多语言
     */
    void updateGoodPromotionMultilingual(Integer id, GoodsPromotionMultilingualForm goodsPromotionMultilingualForm);

    /**
     * 获取推广商品信息多语言列表
     */
    PageVO<GoodsPromotionMultilingualVO> pageGoodPromotionMultilingual(GoodsPromotionPageForm pageForm);

    /**
     * 删除推广商品信息多语言
     */
    void deleteGoodPromotionMultilingual(Integer id);

    /**
     * 复制推广商品信息多语言
     */
    void copyGoodPromotionMultilingual(Integer id);

    /**
     * 获取多语言商品推广链接
     */
    static List<String> getMultilingualLinkList(GoodsPromotionMultilingual goodsPromotionMultilingual,
                                                GoodsMultilingual goodsMultilingual, Domain domain,
                                                PlatformVO platform, List<LanguageContextConfig> languageContextConfigs) {
        List<String> linkList = new ArrayList<>();
        if (Objects.isNull(domain) || Objects.isNull(goodsMultilingual) || Objects.isNull(goodsPromotionMultilingual) || Objects.isNull(platform)) {
            return linkList;
        }
        linkList.add(SystemConstant.buildTgLink(domain.getDomain(), goodsMultilingual.getWebPackageName(),
            goodsPromotionMultilingual.getTgId(), null, platform.getSource()));
        languageContextConfigs.forEach(languageContextConfig -> linkList.add(SystemConstant.buildTgLink(domain.getDomain(), goodsMultilingual.getWebPackageName(),
            goodsPromotionMultilingual.getTgId(), languageContextConfig.getLanguageCode(), platform.getSource())));
        return linkList;
    }



    /**
     * 保存推广商品信息繁体
     */
    void saveGoodPromotionTraditional(GoodsPromotionTraditionalForm goodsPromotionTraditionalForm);

    /**
     * 修改推广商品信息繁体
     */
    void updateGoodPromotionTraditional(Integer id, GoodsPromotionTraditionalForm goodsPromotionTraditionalForm);

    /**
     * 获取推广商品信息繁体列表
     */
    PageVO<GoodsPromotionTraditionalVO> pageGoodPromotionTraditional(GoodsPromotionPageForm pageForm);

    /**
     * 删除推广商品信息繁体
     */
    void deleteGoodPromotionTraditional(Integer id);

    /**
     * 复制推广商品信息繁体
     */
    void copyGoodPromotionTraditional(Integer id);

    /**
     * 获取繁体商品推广链接
     */
    static List<String> getTraditionalLinkList(GoodsPromotionTraditional goodsPromotionTraditional,
                                                GoodsTraditional goodsTraditional, Domain domain,
                                               PlatformVO platform) {
        if (Objects.isNull(goodsPromotionTraditional) || Objects.isNull(goodsTraditional) || Objects.isNull(domain) || Objects.isNull(platform)){
            return Collections.emptyList();
        }
        return Collections.singletonList(SystemConstant.buildTgLink(domain.getDomain(), goodsTraditional.getWebPackageName(), goodsPromotionTraditional.getTgId(), null, platform.getSource()));
    }

    PromotionGoodsInfoBo getPromotionGoodsInfo(String tgId, Integer goodsType);

    List<PromotionGoodsInfoBo> getPromotionGoodsInfos( Integer goodsType);

    void recoverRecyclesGoodsPromotionTraditional(DeleteBatchForm deleteBatchForm);

    void deleteRecyclesGoodsPromotionTraditional(DeleteBatchForm deleteBatchForm);

    void deleteRecyclesGoodsPromotionMultilingual(DeleteBatchForm deleteBatchForm);

    void recoverRecyclesGoodsPromotionMultilingual(DeleteBatchForm deleteBatchForm);
}
