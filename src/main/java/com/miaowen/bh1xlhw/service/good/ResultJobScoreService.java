package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.goods.ResultJobDetailForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultJobForm;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 18:01
 */
public interface ResultJobScoreService {

    public PageVO<ResultJobForm> getResultJobForm(String tag,Integer pageInt,Integer pageSize, boolean isMultiLanguage);

    public ResultJobDetailForm getResultJobDetailForm(String tag,Integer id, boolean isMultiLanguage);

    public void saveResultJobForm(ResultJobDetailForm form, boolean isMultiLanguage);

    public void deleteResultJobForm(List<Integer> ids, boolean isMultiLanguage);


}
