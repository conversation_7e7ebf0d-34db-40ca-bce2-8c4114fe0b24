package com.miaowen.bh1xlhw.service.good.impl;

import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysisPrice;
import com.miaowen.bh1xlhw.model.query.goods.ResultWrongAnalysisPriceForm;
import com.miaowen.bh1xlhw.repository.ResultWrongAnalysisPriceService;
import com.miaowen.bh1xlhw.service.good.ResultWrongPriceService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/23 17:27
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResultWrongPriceServiceImpl implements ResultWrongPriceService {

    @Resource
    ResultWrongAnalysisPriceService resultWrongAnalysisPriceService;


    @Override
    public ResultWrongAnalysisPriceForm getWrongAnalysisPrice(String tag, boolean isMulti) {
        ResultWrongAnalysisPriceForm result = new ResultWrongAnalysisPriceForm();
        List<ResultWrongAnalysisPrice> byTag = resultWrongAnalysisPriceService.getByTag(tag, isMulti);
        result.setTag(tag);

        if(!CollectionUtils.isEmpty(byTag)){
            result.setSale(byTag.get(0).getSale());
            result.setForm(byTag.stream().map(item -> {
                ResultWrongAnalysisPriceForm.Form form = new ResultWrongAnalysisPriceForm.Form();
                form.setName(item.getPriceName());
                form.setDesc(item.getPriceDesc());
                form.setExplain(item.getPriceExplain());
                form.setLang(item.getLang());
                return form;
            }).collect(Collectors.toList()));
        }

        return result;
    }

    @Override
    public void save(ResultWrongAnalysisPriceForm form, boolean isMulti) {
        resultWrongAnalysisPriceService.deleteByTag(form.getTag(), isMulti);
        List<ResultWrongAnalysisPrice> list = new ArrayList<>();
        form.getForm().forEach(item -> {
            ResultWrongAnalysisPrice price = new ResultWrongAnalysisPrice();
            price.setTag(form.getTag());
            price.setSale(form.getSale());
            price.setPriceName(item.getName());
            price.setPriceDesc(item.getDesc());
            price.setPriceExplain(item.getExplain());
            price.setLang(item.getLang());
            price.setType(isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
            list.add(price);
        });
        resultWrongAnalysisPriceService.saveBatch(list);
    }
}
