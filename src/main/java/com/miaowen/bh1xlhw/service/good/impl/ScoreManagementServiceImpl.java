package com.miaowen.bh1xlhw.service.good.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.miaowen.bh1xlhw.config.excel.DynamicExcelListener;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ScoreManagement;
import com.miaowen.bh1xlhw.model.entity.ScoreManagementDetail;
import com.miaowen.bh1xlhw.model.query.goods.ScoreManagementForm;
import com.miaowen.bh1xlhw.model.vo.goods.ScoreManagementVo;
import com.miaowen.bh1xlhw.repository.IScoreManagementDetailService;
import com.miaowen.bh1xlhw.repository.IScoreManagementService;
import com.miaowen.bh1xlhw.service.good.ScoreManagementService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/6/4 19:14
 */
@Slf4j
@Service
@AllArgsConstructor
public class ScoreManagementServiceImpl implements ScoreManagementService {

    private final IScoreManagementService scoreManagementService;
    private final IScoreManagementDetailService scoreManagementDetailService;


    @Override
    public void save(ScoreManagementForm form, boolean isMulti) {
        //先移除原有的 再添加新的
        List<Integer> scoreIds = scoreManagementService.findByTag(form.getTag(), isMulti);
        if (!CollectionUtils.isEmpty(scoreIds)) {
            scoreManagementService.removeBatchByIds(scoreIds);
            scoreManagementDetailService.removeByScoreId(scoreIds);
        }
        for (ScoreManagementForm.Score score : form.getScores()) {
            ScoreManagement scoreManagement = new ScoreManagement();
            scoreManagement.setTag(form.getTag());
            scoreManagement.setType(isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
            scoreManagement.setExamNo(score.getExamNo());
            scoreManagementService.save(scoreManagement);
            List<ScoreManagementDetail> details = new ArrayList<>();
            score.getDetails().forEach(detail -> {
                ScoreManagementDetail entity = new ScoreManagementDetail();
                entity.setScoreId(scoreManagement.getId());
                entity.setExamOption(detail.getExamOption());
                entity.setExamResult(detail.getExamResult());
                details.add(entity);
            });
            scoreManagementDetailService.saveBatch(details);
        }
    }

    /**
     * 导入
     *
     * @param file
     * @param tag
     * @param isMulti
     */
    @Override
    public ScoreManagementForm upload(MultipartFile file, String tag, boolean isMulti) throws IOException {
        DynamicExcelListener listener = new DynamicExcelListener();
        EasyExcel.read(file.getInputStream(), listener).sheet().doRead();
        List<Map<String, String>> data = listener.getData();
        if (CollectionUtils.isEmpty(data)) {
            return null;
        }
        ScoreManagementForm form = new ScoreManagementForm();
        form.setTag(tag);
        List<ScoreManagementForm.Score> scores = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            ScoreManagementForm.Score score = new ScoreManagementForm.Score();
            score.setExamNo(i + 1);
            List<ScoreManagementForm.Detail> details = new ArrayList<>();
            AtomicInteger keySite = new AtomicInteger(1);
            data.get(i).forEach((key, value) -> {
                ScoreManagementForm.Detail detail = new ScoreManagementForm.Detail();
                detail.setExamOption(keySite.get());
                if (value == null) {
                    detail.setExamResult("");
                } else {
                    detail.setExamResult(value);
                }
                details.add(detail);
                keySite.getAndIncrement();

            });
            score.setDetails(details);
            scores.add(score);
        }
        form.setScores(scores);
        return form;
    }

    @Override
    public ScoreManagementVo getScoreManagement(String tag, boolean isMulti) {
        List<ScoreManagement> scoreManagements = scoreManagementService.findByTagAndIsMulti(tag, isMulti);
        if (CollectionUtils.isEmpty(scoreManagements)) {
            return null;
        }
        List<Integer> scoreIds = scoreManagements.stream().map(ScoreManagement::getId).collect(Collectors.toList());
        Map<Integer, List<ScoreManagementDetail>> detailMap = scoreManagementDetailService.mapByScoreIds(scoreIds);
        ScoreManagementVo vo = new ScoreManagementVo();
        vo.setTag(tag);
        vo.setType(isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        List<ScoreManagementForm.Score> scores = new ArrayList<>();
        scoreManagements.forEach(scoreManagement -> {
            ScoreManagementForm.Score score = new ScoreManagementForm.Score();
            score.setExamNo(scoreManagement.getExamNo());
            List<ScoreManagementForm.Detail> details = new ArrayList<>();
            List<ScoreManagementDetail> scoreManagementDetails = detailMap.get(scoreManagement.getId());
            if (!CollectionUtils.isEmpty(scoreManagementDetails)) {
                scoreManagementDetails.forEach(
                        t -> {
                            ScoreManagementForm.Detail detail = new ScoreManagementForm.Detail();
                            detail.setExamOption(t.getExamOption());
                            if (Objects.nonNull(t.getExamResult())) {
                                detail.setExamResult(t.getExamResult());
                            }
                            details.add(detail);

                        }
                );
            }
            score.setDetails(details);
            scores.add(score);
        });
        vo.setScores(scores);
        return vo;

    }

    @Override
    public void export(HttpServletResponse response, String tag, Integer version, boolean isMulti) throws IOException {
        List<ScoreManagement> scoreManagements = scoreManagementService.findByTagAndIsMulti(tag, isMulti);
        if (CollectionUtils.isEmpty(scoreManagements)) {
            return;
        }
        List<Integer> scoreIds = scoreManagements.stream().map(ScoreManagement::getId).collect(Collectors.toList());
        List<ScoreManagementDetail> scoreManagementDetails = scoreManagementDetailService.listByScoreId(scoreIds);
        if (CollectionUtils.isEmpty(scoreManagementDetails)) {
            return;
        }

        // 2. 动态生成表头（基于所有可能出现的examOption）
        Set<String> headers = getAllHeaders(scoreManagementDetails);

        // 3. 准备Excel数据
        List<List<String>> exportData = prepareExportData(scoreManagements, scoreManagementDetails, headers);

        // 4. 设置响应头
        String fileName = URLEncoder.encode("数据详情_" + tag + ".xlsx", "UTF-8");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName);

        // 5. 写入Excel
        try (OutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 自动列宽
                    .head(generateHead(headers)) // 动态表头
                    .sheet("详情")
                    .doWrite(exportData);
        }
    }

    private List<List<String>> generateHead(Set<String> headers) {
        List<List<String>> head = new ArrayList<>();
        // 固定第一列为"考试编号"
        //head.add(Collections.singletonList("题号"));
        // 添加动态选项作为表头
        headers.forEach(header -> head.add(Collections.singletonList(header)));
        return head;
    }


    private Set<String> getAllHeaders(List<ScoreManagementDetail> details) {
        return details.stream()
                .map(ScoreManagementDetail::getExamOption)
                .sorted()
                .map(this::numberToLetter)
                .collect(Collectors.toCollection(LinkedHashSet::new));
    }

    private List<List<String>> prepareExportData(List<ScoreManagement> scoreManagements,
                                                 List<ScoreManagementDetail> scoreManagementDetails, Set<String> headers) {
        List<List<String>> exportData = new ArrayList<>();
        Map<Integer, List<ScoreManagementDetail>> detailMap = scoreManagementDetails.stream()
                .collect(Collectors.groupingBy(ScoreManagementDetail::getScoreId));

        scoreManagements.forEach(scoreManagement -> {
            List<String> rowData = new ArrayList<>();
            // 添加考试编号
            // rowData.add(String.valueOf(scoreManagement.getExamNo()));

            // 填充所有可能的考试选项，如果没有数据则填充空字符
            for (String option : headers) {
                List<ScoreManagementDetail> details = detailMap.getOrDefault(scoreManagement.getId(), Collections.emptyList());
                String result = details.stream()
                        .filter(detail -> numberToLetter(detail.getExamOption()).equals(option))
                        .map(ScoreManagementDetail::getExamResult)
                        .findFirst()
                        .orElse("");
                rowData.add(result);
            }
            exportData.add(rowData);
        });
        return exportData;
    }

    private String numberToLetter(int num) {
        StringBuilder sb = new StringBuilder();
        while (num > 0) {
            num--; // 因为A对应0而不是1
            char ch = (char) ('A' + (num % 26));
            sb.insert(0, ch);
            num /= 26;
        }
        return "选项" + sb.toString();
    }

}
