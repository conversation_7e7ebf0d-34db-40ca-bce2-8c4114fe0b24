package com.miaowen.bh1xlhw.service.good.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.GoodsCategoryMultilingual;
import com.miaowen.bh1xlhw.model.entity.GoodsCategoryTraditional;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsCategoryMultilingualVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsCategoryTraditionalVO;
import com.miaowen.bh1xlhw.repository.GoodsCategoryMultilingualService;
import com.miaowen.bh1xlhw.repository.GoodsCategoryTraditionalService;
import com.miaowen.bh1xlhw.service.good.GoodsCategoryService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * GoodTypeConfigService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09
 */
@Slf4j
@Service
@AllArgsConstructor
public class GoodsCategoryServiceImpl implements GoodsCategoryService {
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;

    @Override
    public ResultVO<Void> saveGoodsCategoryMultilingual(GoodsCategoryMultilingualForm goodsCategoryMultilingualForm) {
        if (goodsCategoryMultilingualService.getByType(goodsCategoryMultilingualForm.getType()) != null) {
            return ResultVO.fail(ResultEnum.DataCheckFail,"标签已存在");
        }
        GoodsCategoryMultilingual goodsCategoryMultilingual = BeanUtils.copy(goodsCategoryMultilingualForm, GoodsCategoryMultilingual.class);
        goodsCategoryMultilingualService.save(goodsCategoryMultilingual);
        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> updateGoodsCategoryMultilingual(Integer id, GoodsCategoryMultilingualForm goodsCategoryMultilingualForm) {
        GoodsCategoryMultilingual byType = goodsCategoryMultilingualService.getByType(goodsCategoryMultilingualForm.getType());
        if (byType != null && !byType.getId().equals(id)) {
            return ResultVO.fail(ResultEnum.DataCheckFail,"标签已存在");
        }
        GoodsCategoryMultilingual goodsCategoryMultilingual = BeanUtils.copy(goodsCategoryMultilingualForm, GoodsCategoryMultilingual.class);
        goodsCategoryMultilingual.setId(id);
        goodsCategoryMultilingualService.updateById(goodsCategoryMultilingual);
        return ResultVO.success();
    }

    @Override
    public PageVO<GoodsCategoryMultilingualVO> pageGoodsCategoryMultilingual(GoodsCategoryPageForm pageForm) {
        Page<GoodsCategoryMultilingual> goodsTypeEnPage = goodsCategoryMultilingualService.pageInfo(pageForm);
        return new PageVO<>(goodsTypeEnPage, GoodsCategoryMultilingualVO.class);
    }

    @Override
    public void deleteGoodTypeMultilingual(Integer id) {
        goodsCategoryMultilingualService.logicRemoveById(id);
    }

    @Override
    public List<GoodsCategoryMultilingualVO> listGoodTypeMultilingual() {
        List<GoodsCategoryMultilingual> goodsCategoryMultilingualPage = goodsCategoryMultilingualService.listAll();
        return BeanUtils.copyList(goodsCategoryMultilingualPage, GoodsCategoryMultilingualVO.class);
    }


    @Override
    public ResultVO<Void> saveGoodsCategoryTraditional(GoodsCategoryTraditionalForm goodsCategoryTraditionalForm) {
        if (goodsCategoryTraditionalService.getByType(goodsCategoryTraditionalForm.getType()) != null) {
            return ResultVO.fail(ResultEnum.DataCheckFail,"标签已存在");
        }
        GoodsCategoryTraditional goodsCategoryTraditional = BeanUtils.copy(goodsCategoryTraditionalForm, GoodsCategoryTraditional.class);
        goodsCategoryTraditionalService.save(goodsCategoryTraditional);
        return ResultVO.success();
    }

    @Override
    public ResultVO<Void> updateGoodsCategoryTraditional(Integer id, GoodsCategoryTraditionalForm goodsCategoryTraditionalForm) {
        GoodsCategoryTraditional byType = goodsCategoryTraditionalService.getByType(goodsCategoryTraditionalForm.getType());
        if (byType != null && !byType.getId().equals(id)) {
            return ResultVO.fail(ResultEnum.DataCheckFail,"标签已存在");
        }
        GoodsCategoryTraditional goodsCategoryTraditional = BeanUtils.copy(goodsCategoryTraditionalForm, GoodsCategoryTraditional.class);
        goodsCategoryTraditional.setId(id);
        goodsCategoryTraditionalService.updateById(goodsCategoryTraditional);
        return ResultVO.success();
    }

    @Override
    public PageVO<GoodsCategoryTraditionalVO> pageGoodsCategoryTraditional(GoodsCategoryPageForm pageForm) {
        Page<GoodsCategoryTraditional> goodsTypeEnPage = goodsCategoryTraditionalService.pageInfo(pageForm);
        return new PageVO<>(goodsTypeEnPage, GoodsCategoryTraditionalVO.class);
    }

    @Override
    public void deleteGoodsCategoryTraditional(Integer id) {
        goodsCategoryTraditionalService.logicRemoveById(id);
    }

    @Override
    public List<GoodsCategoryTraditionalVO> listGoodsCategoryTraditional() {
        List<GoodsCategoryTraditional> goodsCategoryTraditionalPage = goodsCategoryTraditionalService.listAll();
        return BeanUtils.copyList(goodsCategoryTraditionalPage, GoodsCategoryTraditionalVO.class);
    }
}
