package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.model.query.goods.ResultReadingForm;

import java.util.List;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 14:52
 */
public interface ResultReadingService {


    ResultReadingForm getByTag(String tag, boolean isMulti);

    void save(ResultReadingForm resultReadingForm, boolean isMulti);

}
