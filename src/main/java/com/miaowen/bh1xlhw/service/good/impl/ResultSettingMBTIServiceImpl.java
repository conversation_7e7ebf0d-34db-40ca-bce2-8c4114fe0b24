package com.miaowen.bh1xlhw.service.good.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ResultMBTIPrice;
import com.miaowen.bh1xlhw.model.entity.ResultMBTIWeidu;
import com.miaowen.bh1xlhw.model.query.goods.ResultMBTIPriceForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultMBTIWeiduForm;
import com.miaowen.bh1xlhw.repository.ResultMBTIPriceService;
import com.miaowen.bh1xlhw.repository.ResultMBTIWeiduService;
import com.miaowen.bh1xlhw.service.good.ResultSettingMBTIService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/6/5 15:48
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResultSettingMBTIServiceImpl implements ResultSettingMBTIService {

    private final ResultMBTIWeiduService weiduService;
    private final ResultMBTIPriceService priceService;


    @Override
    public void saveOrUpdate(ResultMBTIWeiduForm form) {
        //目前只针对MBTI繁体 后面如果有需要再进行扩展
        form.setTag("MBTI");
        form.setType(GoodsTypeEnum.TRADITIONAL.getValue());
        ResultMBTIWeidu entity = BeanUtils.copy(form, ResultMBTIWeidu.class);
        if (form.getId() == null) {
            weiduService.save(entity);
        } else {
            weiduService.updateById(entity);
        }
    }

    @Override
    public List<ResultMBTIWeiduForm> listAll(Integer version) {
        QueryWrapper<ResultMBTIWeidu> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("version", version);
        List<ResultMBTIWeidu> list = weiduService.list(queryWrapper);
        return BeanUtils.copyList(list, ResultMBTIWeiduForm.class);
    }

    @Override
    public void delete(Integer id) {
        weiduService.removeById(id);
    }

    @Override
    public void saveOrUpdatePrice(ResultMBTIPriceForm form) {
        //目前只针对MBTI繁体 后面如果有需要再进行扩展
        form.setTag("MBTI");
        form.setType(GoodsTypeEnum.TRADITIONAL.getValue());
        ResultMBTIPrice entity = BeanUtils.copy(form, ResultMBTIPrice.class);
        entity.setCareerAdvantagesPrice(multiplyBy100ToInteger(form.getCareerAdvantagesPrice()));
        entity.setLoveReportPrice(multiplyBy100ToInteger(form.getLoveReportPrice()));
        entity.setEightDimensionalReportPrice(multiplyBy100ToInteger(form.getEightDimensionalReportPrice()));
        priceService.saveOrUpdate(entity);
    }

    @Override
    public ResultMBTIPriceForm listAllPrice() {
        //目前只有一条数据
        List<ResultMBTIPrice> list = priceService.list();
        if (CollectionUtils.isEmpty(list)) {
            return new ResultMBTIPriceForm();
        }
        ResultMBTIPrice resultMBTIPrice = list.get(0);
        ResultMBTIPriceForm form = BeanUtils.copy(resultMBTIPrice, ResultMBTIPriceForm.class);
        form.setCareerAdvantagesPrice(new BigDecimal(resultMBTIPrice.getCareerAdvantagesPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        form.setLoveReportPrice(new BigDecimal(resultMBTIPrice.getLoveReportPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        form.setEightDimensionalReportPrice(new BigDecimal(resultMBTIPrice.getEightDimensionalReportPrice()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        return form;
    }


    public Integer multiplyBy100ToInteger(BigDecimal value) {
        if (value == null) return 0;
        return value.multiply(new BigDecimal(100))
                .intValueExact(); // 如果结果超出long范围或非整数，会抛出异常
    }


}
