package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.AdvertiseAccountForm;
import com.miaowen.bh1xlhw.model.query.goods.AdvertisePageForm;
import com.miaowen.bh1xlhw.model.vo.goods.AdvertiseAccountGoodLinkVO;
import com.miaowen.bh1xlhw.model.vo.goods.AdvertiseAccountVO;

import java.util.List;

/**
 * AdvertiseAccountInfoService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
public interface AdvertiseAccountInfoService {

    /**
     * 保存广告账户表信息
     */
    void saveAdvertiseAccount(AdvertiseAccountForm advertiseAccountForm);

    /**
     * 修改广告账户表信息
     */
    void updateAdvertiseAccount(Integer id, AdvertiseAccountForm advertiseAccountForm);

    /**
     * 获取广告账户表信息列表
     */
    PageVO<AdvertiseAccountVO> pageAdvertiseAccount(AdvertisePageForm pageForm);

    /**
     * 获取广告账户表信息列表全部
     */
    List<AdvertiseAccountVO> allAdvertiseAccount();

    /**
     * 删除广告账户表信息
     */
    void deleteAdvertiseAccount(Integer id);

    /**
     * 获取广告账户商品推广链接
     */
    AdvertiseAccountGoodLinkVO listAdvertiseAccountTgLink(Integer id);

    /**
     * 查询运营人员管理的账户
     */
    List<AdvertiseAccountVO> getListByOperationUser(String workNo);
    List<AdvertiseAccountVO> getListByOperationManager(String workNo);

}
