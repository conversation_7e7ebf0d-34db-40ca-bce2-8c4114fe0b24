package com.miaowen.bh1xlhw.service.good.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ResultWeidu;
import com.miaowen.bh1xlhw.model.entity.ResultWeiduLang;
import com.miaowen.bh1xlhw.model.entity.ResultWeiduMap;
import com.miaowen.bh1xlhw.model.entity.ResultWeiduMapLang;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduBaseForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduDetailForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduMapForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduMapLangForm;
import com.miaowen.bh1xlhw.repository.ResultWeiduLangService;
import com.miaowen.bh1xlhw.repository.ResultWeiduMapLangService;
import com.miaowen.bh1xlhw.repository.ResultWeiduMapService;
import com.miaowen.bh1xlhw.repository.ResultWeiduService;
import com.miaowen.bh1xlhw.service.good.ResultWeiduAnalysisServie;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/19 16:57
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResultWeiduAnalysisServieImpl implements ResultWeiduAnalysisServie {
    private final ResultWeiduService weiduService;
    private final ResultWeiduLangService weiduLangService;
    private final ResultWeiduMapService weiduMapService;
    private final ResultWeiduMapLangService weiduMapLangService;


    @Override
    public PageVO<ResultWeiduMapForm> getByWeiduId(Integer pageInt, Integer pageSize, Integer weiduId) {
        PageVO<ResultWeiduMapForm> result = new PageVO<>();
        PageForm page = new PageForm(pageInt, pageSize);
        Page<ResultWeiduMap> byWeiduId = weiduMapService.getByWeiduId(page, weiduId);
        result.setPageInt(pageInt);
        result.setPageSize(pageSize);
        result.setTotalCount(byWeiduId.getTotal());
        result.setRecords(BeanUtils.copyList(byWeiduId.getRecords(), ResultWeiduMapForm.class));
        return result;
    }

    @Override
    public List<ResultWeiduMapLangForm.WeiduMapLang> getByWeiduMapIdLang(Integer weiduMapId) {
        List<ResultWeiduMapLang> entityList = weiduMapLangService.findByWeiduMapId(weiduMapId);
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return BeanUtils.copyList(entityList, ResultWeiduMapLangForm.WeiduMapLang.class);

    }

    @Override
    public void deletByWeiduMapId(List<Integer> ids) {
        weiduMapService.delete(ids);
        weiduMapLangService.deleteByWeiduMapIds(ids);
    }

    @Override
    public void saveOrUpdate(ResultWeiduMapLangForm form) {

        ResultWeiduMap resultWeiduMap = new ResultWeiduMap();
        resultWeiduMap.setWeiduId(form.getWeiduId());
        resultWeiduMap.setName(form.getName());
        resultWeiduMap.setScore(form.getScore());
        if (form.getId() != null) {
            resultWeiduMap.setId(form.getId());
        }
        //新增或者修改
        Integer weiduMapId = weiduMapService.editOrSave(resultWeiduMap);

        List<ResultWeiduMapLangForm.WeiduMapLang> details = form.getDetails();
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        weiduMapLangService.deleteByWeiduMapId(weiduMapId);
        details.forEach(item -> {
            ResultWeiduMapLang resultWeiduMapLang = new ResultWeiduMapLang();
            resultWeiduMapLang.setWeiduMapId(weiduMapId);
            resultWeiduMapLang.setLang(item.getLang());
            resultWeiduMapLang.setName(item.getName());
            weiduMapLangService.saveWeiduMapLang(resultWeiduMapLang);
        });

    }

    @Override
    public PageVO<ResultWeiduBaseForm> getByTag(String tag, Integer pageInt, Integer pageSize, boolean isMulti) {
        PageVO<ResultWeiduBaseForm> result = new PageVO<>();
        PageForm page = new PageForm(pageInt, pageSize);
        Page<ResultWeidu> byWeiduId = weiduService.getByTag(page, tag, isMulti);
        result.setPageInt(pageInt);
        result.setPageSize(pageSize);
        result.setTotalCount(byWeiduId.getTotal());
        result.setRecords(BeanUtils.copyList(byWeiduId.getRecords(), ResultWeiduBaseForm.class));
        return result;
    }

    @Override
    public void deleteByWeiduId(List<Integer> ids) {
        weiduService.deleteResultWeidu(ids);
        weiduLangService.deleteByWeiduIds(ids);
    }

    @Override
    public void saveOrUpdate(ResultWeiduDetailForm form, boolean isMulti) {
        ResultWeidu resultWeidu = new ResultWeidu();
        resultWeidu.setTag(form.getTag());
        resultWeidu.setName(form.getName());
        resultWeidu.setScore(form.getScore());
        resultWeidu.setQuestionNums(form.getQuestionNums());
        resultWeidu.setWeiduType(form.getWeiduType());
        resultWeidu.setType(isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        if (form.getId() != null) {
            resultWeidu.setId(form.getId());
        }
        Integer weiduId = weiduService.saveOrUpdateResultWeidu(resultWeidu);
        if (form.getId() != null) {
            weiduLangService.deleteByWeiduId(weiduId);
        }
        List<ResultWeiduDetailForm.WeiduLang> details = form.getDetails();
        if (!CollectionUtils.isEmpty(details)) {
            details.forEach(item -> {
                ResultWeiduLang resultWeiduLang = new ResultWeiduLang();
                resultWeiduLang.setName(item.getName());
                resultWeiduLang.setWeiduId(weiduId);
                resultWeiduLang.setLang(item.getLang());
                resultWeiduLang.setContent(item.getContent());
                resultWeiduLang.setProposal(item.getProposal());
                weiduLangService.saveWeiduLang(resultWeiduLang);
            });
        }
    }

    @Override
    public List<ResultWeiduDetailForm.WeiduLang> getByWeiduIdLang(Integer weiduId) {
        List<ResultWeiduLang> entityList = weiduLangService.findByWeiduId(weiduId);
        return BeanUtils.copyList(entityList, ResultWeiduDetailForm.WeiduLang.class);
    }
}
