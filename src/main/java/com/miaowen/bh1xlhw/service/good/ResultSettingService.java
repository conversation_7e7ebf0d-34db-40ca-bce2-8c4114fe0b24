package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.goods.ResultSettingWrongBaseForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultSettingWrongDetailForm;
import com.miaowen.bh1xlhw.model.vo.goods.ResultWrongVo;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 10:29
 */
public interface ResultSettingService {

    /**
     * 获取题目设置
     */
    public List<ResultWrongVo> getResultWrongVo(String tag, boolean isMultiLanguage);


    /**
     * 添加题号及答案
     */
    ResultVO<Void> addResultWrongVo(ResultSettingWrongDetailForm form, boolean isMultiLanguage);

    /**
     * 修改题号及答案
     */
    Void editResultWrongVo(ResultSettingWrongBaseForm form, boolean isMultiLanguage);

    /**
     * 删除题号及答案
     */
    Void deleteResultWrongVo(ResultSettingWrongBaseForm form, boolean isMultiLanguage);

    /**
     * 修改题号及答案解析（包含答案解析）
     */
    Void editResultWrongDetailVo(ResultSettingWrongDetailForm form, boolean isMultiLanguage);

    /**
     * 获取题号及答案解析（包含答案解析）
     */
    ResultSettingWrongDetailForm getResultWrongDetailVo(ResultSettingWrongBaseForm form, boolean isMultiLanguage);
}
