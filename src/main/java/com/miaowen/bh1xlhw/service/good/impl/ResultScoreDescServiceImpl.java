package com.miaowen.bh1xlhw.service.good.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ResultScore;
import com.miaowen.bh1xlhw.model.entity.ResultScoreLang;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultScoreDetailForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultScoreForm;
import com.miaowen.bh1xlhw.repository.ResultScoreLangService;
import com.miaowen.bh1xlhw.repository.ResultScoreService;
import com.miaowen.bh1xlhw.service.good.ResultScoreDescService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 18:01
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResultScoreDescServiceImpl implements ResultScoreDescService {

    private final ResultScoreService resultScoreService;
    private final ResultScoreLangService resultScoreLangService;

    @Override
    public PageVO<ResultScoreForm> getResultScoreForm(String tag, Integer pageInt, Integer pageSize, boolean isMultiLanguage) {
        PageVO<ResultScoreForm> resultScoreFormPageVO = new PageVO<>();
        PageForm page = new PageForm(pageInt, pageSize);
        Page<ResultScore> resultScoreByTag = resultScoreService.getResultScoreByTag(page, tag, isMultiLanguage);
        resultScoreFormPageVO.setPageInt(pageInt);
        resultScoreFormPageVO.setPageSize(pageSize);
        resultScoreFormPageVO.setRecords(BeanUtils.copyList(resultScoreByTag.getRecords(), ResultScoreForm.class));
        resultScoreFormPageVO.setTotalCount(resultScoreByTag.getTotal());
        return resultScoreFormPageVO;
    }

    @Override
    public ResultScoreDetailForm getResultScoreDetailForm(String tag, Integer scoreId, boolean isMultiLanguage) {
        ResultScoreDetailForm result = new ResultScoreDetailForm();
        ResultScore resultScore = resultScoreService.getResultScoreById(scoreId);
        if (resultScore == null) {
            return result;
        }
        result = BeanUtils.copy(resultScore, ResultScoreDetailForm.class);
        List<ResultScoreLang> byScoreId = resultScoreLangService.findByScoreId(scoreId);
        List<ResultScoreDetailForm.Score> details = new ArrayList<>();
        byScoreId.forEach(t -> {
            details.add(BeanUtils.copy(t, ResultScoreDetailForm.Score.class));
        });
        result.setDetails(details);
        return result;
    }


    @Override
    public void saveResultScoreForm(ResultScoreDetailForm form, boolean isMultiLanguage) {
        Integer type = isMultiLanguage ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue();
        ResultScore resultScore = BeanUtils.copy(form, ResultScore.class);
        resultScore.setType(type);
        //无id表示新增
        if (resultScore.getId() == null) {
            Integer id = resultScoreService.saveResultScore(resultScore);
            form.getDetails().forEach(t -> {
                ResultScoreLang resultScoreLang = BeanUtils.copy(t, ResultScoreLang.class);
                resultScoreLang.setResultScoreId(id);
                resultScoreLangService.add(resultScoreLang);
            });
        } else {
            //有id表示修改
            resultScoreService.updateResultScore(resultScore);
            //先删后增
            resultScoreLangService.deleteByScoreId(resultScore.getId());
            form.getDetails().forEach(t -> {
                ResultScoreLang resultScoreLang = BeanUtils.copy(t, ResultScoreLang.class);
                resultScoreLang.setResultScoreId(resultScore.getId());
                resultScoreLangService.add(resultScoreLang);
            });
        }
    }

    @Override
    public void deleteResultScoreForm(List<Integer> ids, boolean isMultiLanguage) {
        resultScoreService.deleteResultScore(ids);
        resultScoreLangService.deleteByScoreIds(ids);
    }
}
