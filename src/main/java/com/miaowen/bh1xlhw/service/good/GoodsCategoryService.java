package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsCategoryMultilingualVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsCategoryTraditionalVO;

import java.util.List;

/**
 * GoodTypeConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09
 */
public interface GoodsCategoryService {
    /**
     * 保存多语言商品类型
     */
    ResultVO<Void> saveGoodsCategoryMultilingual(GoodsCategoryMultilingualForm goodsCategoryMultilingualForm);

    /**
     * 修改多语言商品类型
     */
    ResultVO<Void> updateGoodsCategoryMultilingual(Integer id, GoodsCategoryMultilingualForm goodsCategoryMultilingualForm);

    /**
     * 获取多语言商品类型列表
     */
    PageVO<GoodsCategoryMultilingualVO> pageGoodsCategoryMultilingual(GoodsCategoryPageForm pageForm);

    /**
     * 删除多语言商品类型
     */
    void deleteGoodTypeMultilingual(Integer id);
    /**
     * 获取多语言商品类型所有数据列表
     */
    List<GoodsCategoryMultilingualVO> listGoodTypeMultilingual();


    /**
     * 保存繁体商品类型
     */
    ResultVO<Void> saveGoodsCategoryTraditional(GoodsCategoryTraditionalForm goodsCategoryTraditionalForm);

    /**
     * 修改繁体商品类型
     */
    ResultVO<Void> updateGoodsCategoryTraditional(Integer id, GoodsCategoryTraditionalForm goodsCategoryTraditionalForm);

    /**
     * 获取繁体商品类型列表
     */
    PageVO<GoodsCategoryTraditionalVO> pageGoodsCategoryTraditional(GoodsCategoryPageForm pageForm);

    /**
     * 删除繁体商品类型
     */
    void deleteGoodsCategoryTraditional(Integer id);
    /**
     * 获取繁体商品类型所有数据列表
     */
    List<GoodsCategoryTraditionalVO> listGoodsCategoryTraditional();

}
