package com.miaowen.bh1xlhw.service.good.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ResultJob;
import com.miaowen.bh1xlhw.model.entity.ResultJobLang;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultJobDetailForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultJobForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultScoreForm;
import com.miaowen.bh1xlhw.repository.ResultJobLangService;
import com.miaowen.bh1xlhw.repository.ResultJobService;
import com.miaowen.bh1xlhw.service.good.ResultJobScoreService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 11:10
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResultJobScoreServiceImpl implements ResultJobScoreService {

    private final ResultJobService resultJobService;
    private final ResultJobLangService resultJobLangService;

    @Override
    public PageVO<ResultJobForm> getResultJobForm(String tag,Integer pageInt,Integer pageSize, boolean isMultiLanguage) {
        PageVO<ResultJobForm> resultJobFormPageVO = new PageVO<>();
        PageForm page = new PageForm(pageInt, pageSize);
        Page<ResultJob> resultJobByTag = resultJobService.getResultJobByTag(page, tag, isMultiLanguage);
        resultJobFormPageVO.setPageInt(pageInt);
        resultJobFormPageVO.setPageSize(pageSize);
        resultJobFormPageVO.setRecords(BeanUtils.copyList(resultJobByTag.getRecords(), ResultJobForm.class));
        resultJobFormPageVO.setTotalCount(resultJobByTag.getTotal());
        return resultJobFormPageVO;
    }

    @Override
    public ResultJobDetailForm getResultJobDetailForm(String tag, Integer id, boolean isMultiLanguage) {
        ResultJobDetailForm result = new ResultJobDetailForm();
        ResultJob resultJobById = resultJobService.getResultJobById(id);
        if (resultJobById == null) {
            return result;
        }
        result = BeanUtils.copy(resultJobById, ResultJobDetailForm.class);
        List<ResultJobDetailForm.JobScore> details = new ArrayList<>();
        resultJobLangService.findByJobId(id).forEach(resultJobLang -> {
            details.add(BeanUtils.copy(resultJobLang, ResultJobDetailForm.JobScore.class));
        });
        result.setDetails(details);
        return result;
    }

    @Override
    public void saveResultJobForm(ResultJobDetailForm form, boolean isMultiLanguage) {
        Integer type = isMultiLanguage ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue();
        ResultJob resultJob = BeanUtils.copy(form, ResultJob.class);
        resultJob.setType(type);
        if (form.getId() == null) {
            //新增
            Integer id = resultJobService.saveResultJob(resultJob);
            form.getDetails().forEach(t -> {
                ResultJobLang resultJobLang = BeanUtils.copy(t, ResultJobLang.class);
                resultJobLang.setResultJobId(id);
                resultJobLangService.add(resultJobLang);
            });
        } else {
            //修改
            resultJobService.updateResultJob(resultJob);
            resultJobLangService.deleteByJobId(form.getId());
            form.getDetails().forEach(t -> {
                ResultJobLang resultJobLang = BeanUtils.copy(t, ResultJobLang.class);
                resultJobLang.setResultJobId(form.getId());
                resultJobLangService.add(resultJobLang);
            });
        }

    }

    @Override
    public void deleteResultJobForm(List<Integer> ids, boolean isMultiLanguage) {
        resultJobService.deleteResultJob(ids);
        resultJobLangService.deleteByJobIds(ids);
    }
}
