package com.miaowen.bh1xlhw.service.good.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsBenefitInfoBO;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsInfoBo;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.goods.*;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsMultilingualVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsTraditionalVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * GoodServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09
 */
@Slf4j
@Service
@AllArgsConstructor
public class GoodsServiceImpl implements GoodsService {
    private final LanguageContextConfigService languageContextConfigService;
    private final IPriceService priceService;

    private final IArticleService articleService;

    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;

    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveGoodsMultilingual(GoodsMultilingualForm goodsMultilingualForm) {
        GoodsMultilingual goodsMultilingual = BeanUtils.copy(goodsMultilingualForm, GoodsMultilingual.class);
        List<GoodsLanguageForm> goodsLanguageList = goodsMultilingualForm.getGoodsLanguageList();
        goodsMultilingualService.save(goodsMultilingual);
        languageContextConfigService.saveBatchGoodsName(goodsLanguageList, goodsMultilingual.getId(), LanguageContextConfig.RelationType.GOODS_MULTILINGUAL);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateGoodsMultilingual(Integer id, GoodsMultilingualForm goodsMultilingualForm) {
        GoodsMultilingual goodsMultilingual = BeanUtils.copy(goodsMultilingualForm, GoodsMultilingual.class);
        goodsMultilingual.setId(id);
        List<GoodsLanguageForm> goodsLanguageList = goodsMultilingualForm.getGoodsLanguageList();
        List<GoodsBenefitInfoBO> goodsBenefitInfoBos = goodsMultilingualForm.getGoodsBenefitInfoList();
        //权益信息json处理
        goodsMultilingual.setBenefitInfo(JSONObject.toJSONString(goodsBenefitInfoBos));
        goodsMultilingual.setPriceId(goodsMultilingualForm.getPriceId());
        goodsMultilingualService.updateById(goodsMultilingual);

        languageContextConfigService.logicRemoveByRelationId(id, LanguageContextConfig.RelationType.GOODS_MULTILINGUAL);
        languageContextConfigService.saveBatchGoodsName(goodsLanguageList, id, LanguageContextConfig.RelationType.GOODS_MULTILINGUAL);
    }


    @Override
    public PageVO<GoodsMultilingualVO> pageGoodsMultilingual(GoodsPageForm pageForm) {
        Page<GoodsMultilingual> goodsEnPage = goodsMultilingualService.pageInfo(pageForm);
        List<GoodsMultilingual> records = goodsEnPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>();
        }
        List<GoodsMultilingualVO> goodsMultilingualVos = getGoodsMultilingualVos(records);
        return new PageVO<GoodsMultilingualVO>(goodsEnPage).convert(goodsMultilingualVos);
    }

    private List<GoodsMultilingualVO> getGoodsMultilingualVos(List<GoodsMultilingual> records) {
        List<Integer> goodsTypeIds = new ArrayList<>();
        List<Integer> priceIds = new ArrayList<>();
        List<Integer> goodsIds = new ArrayList<>();
        List<Integer> articleIds = new ArrayList<>();

        records.forEach(record -> {
            goodsTypeIds.add(record.getGoodsCategoryId());
            priceIds.add(record.getPriceId());
            goodsIds.add(record.getId());
            if (!StringUtils.isEmpty(record.getArticleId())) {
                articleIds.addAll(Arrays.stream(record.getArticleId().split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            }
        });
        Map<Integer, Price> priceMap = priceService.mapByIds(priceIds);
        Map<Integer, GoodsCategoryMultilingual> goodsTypeEnMap = goodsCategoryMultilingualService.mapByIds(goodsTypeIds);
        Map<Integer, List<LanguageContextConfig>> languageMap =
                languageContextConfigService.mapByRelationIds(LanguageContextConfig.RelationType.GOODS_MULTILINGUAL, goodsIds);
        Map<Integer, Article> articleMap = articleService.mapByIds(articleIds);
        return records.stream().map(record -> {
            Price price = priceMap.get(record.getPriceId());
            List<LanguageContextConfig> languageContextConfigs = languageMap.getOrDefault(record.getId(), Collections.emptyList());
            GoodsCategoryMultilingual goodsCategoryMultilingual = goodsTypeEnMap.get(record.getGoodsCategoryId());
            GoodsMultilingualVO goodsMultilingualVO = GoodsMultilingualVO.transformVo(record, languageContextConfigs, price, goodsCategoryMultilingual);
            goodsMultilingualVO.setArticle(getArticleIds(record.getArticleId(), articleMap));
            return goodsMultilingualVO;
        }).collect(Collectors.toList());
    }

    List<Article> getArticleIds(String articleIds, Map<Integer, Article> articleMap) {
        if (StringUtils.isEmpty(articleIds)) {
            return Collections.emptyList();
        }
        List<Integer> collect = Arrays.stream(articleIds.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        return collect.stream().map(articleMap::get).collect(Collectors.toList());
    }


    @Override
    public List<GoodsMultilingualVO> listGoodsMultilingual() {
        List<GoodsMultilingual> goodsList = goodsMultilingualService.listAll();
        if (CollectionUtils.isEmpty(goodsList)) {
            return Collections.emptyList();
        }
        return getGoodsMultilingualVos(goodsList);
    }

    @Override
    public void deleteGoodsMultilingual(Integer id) {
        goodsMultilingualService.logicRemoveById(id);
    }

    @Override
    public void saveGoodsTraditional(GoodsTraditionalForm goodsTraditionalForm) {
        GoodsTraditional goodsTraditional = BeanUtils.copy(goodsTraditionalForm, GoodsTraditional.class);
        goodsTraditionalService.save(goodsTraditional);
    }

    @Override
    public void updateGoodsTraditional(Integer id, GoodsTraditionalForm goodsTraditionalForm) {
        GoodsTraditional goodsTraditional = BeanUtils.copy(goodsTraditionalForm, GoodsTraditional.class);
        goodsTraditional.setId(id);
        //权益信息json处理
        List<GoodsBenefitInfoBO> goodsBenefitInfoBos = goodsTraditionalForm.getGoodsBenefitInfoList();
        goodsTraditional.setBenefitInfo(JSONObject.toJSONString(goodsBenefitInfoBos));
        goodsTraditional.setPriceId(goodsTraditionalForm.getPriceId());
        goodsTraditionalService.updateById(goodsTraditional);
    }

    @Override
    public PageVO<GoodsTraditionalVO> pageGoodsTraditional(GoodsPageForm pageForm) {
        Page<GoodsTraditional> goodsTraditionalPage = goodsTraditionalService.pageInfo(pageForm);
        List<GoodsTraditional> records = goodsTraditionalPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>();
        }
        List<GoodsTraditionalVO> goodsTraditionalVos = getGoodsTraditionalVos(records);
        return new PageVO<GoodsTraditionalVO>(goodsTraditionalPage).convert(goodsTraditionalVos);
    }

    private List<GoodsTraditionalVO> getGoodsTraditionalVos(List<GoodsTraditional> records) {
        List<Integer> goodsTypeIds = new ArrayList<>();
        List<Integer> priceIds = new ArrayList<>();
        List<Integer> articleIds = new ArrayList<>();

        records.forEach(record -> {
            goodsTypeIds.add(record.getGoodsCategoryId());
            priceIds.add(record.getPriceId());
            if (!StringUtils.isEmpty(record.getArticleId())) {
                articleIds.addAll(Arrays.stream(record.getArticleId().split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            }
        });
        Map<Integer, Price> priceMap = priceService.mapByIds(priceIds);
        Map<Integer, GoodsCategoryTraditional> goodsTypeEnMap = goodsCategoryTraditionalService.mapByIds(goodsTypeIds);
        Map<Integer, Article> articleMap = articleService.mapByIds(articleIds);
        return records.stream().map(record -> {
            Price price = priceMap.get(record.getPriceId());
            GoodsCategoryTraditional goodsTypeMultilingual = goodsTypeEnMap.get(record.getGoodsCategoryId());

            GoodsTraditionalVO goodsTraditionalVO = GoodsTraditionalVO.transformVo(record, price,
                    goodsTypeMultilingual);
            goodsTraditionalVO.setArticle(getArticleIds(record.getArticleId(), articleMap));
            return goodsTraditionalVO;
        }).collect(Collectors.toList());
    }


    @Override
    public List<GoodsTraditionalVO> listGoodsTraditional() {
        List<GoodsTraditional> records = goodsTraditionalService.listAll();
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }
        return getGoodsTraditionalVos(records);
    }

    @Override
    public void deleteGoodsTraditional(Integer id) {
        goodsTraditionalService.logicRemoveById(id);

    }
    @Override
    public GoodsInfoBo getGoodsInfo(Integer goodsId, Integer goodsType) {
        if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)){
            GoodsMultilingual goodsMultilingual = goodsMultilingualService.getById(goodsId);
            return BeanUtils.copy(goodsMultilingual, GoodsInfoBo.class);
        }else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)){
            GoodsTraditional goodsTraditional = goodsTraditionalService.getById(goodsId);
            return BeanUtils.copy(goodsTraditional, GoodsInfoBo.class);
        }
        return new GoodsInfoBo();
    }

    @Override
    public String getGoodsCategory(Integer goodsTypeId, Integer goodsType) {
        if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)){
            GoodsCategoryMultilingual goodsCategoryMultilingual = goodsCategoryMultilingualService.getById(goodsTypeId);
            if (Objects.nonNull(goodsCategoryMultilingual)){
                return goodsCategoryMultilingual.getType();
            }
        }else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(goodsType)){
            GoodsCategoryTraditional goodsCategoryTraditional = goodsCategoryTraditionalService.getById(goodsTypeId);
            if (Objects.nonNull(goodsCategoryTraditional)){
                return goodsCategoryTraditional.getType();
            }
        }
        return "";
    }

    @Override
    public String getGoodsName(Integer goodsId, Integer goodsType, String languageCode) {
        if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)){
            return getMutiGoodsName(goodsId, goodsType, languageCode);
        }else {
            GoodsTraditional goodsTraditional = goodsTraditionalService.getById(goodsId);
            if (Objects.nonNull(goodsTraditional)){
                return goodsTraditional.getName();
            }
            return "";
        }
    }

    private String getMutiGoodsName(Integer goodsId, Integer goodsType, String languageCode) {
        Map<String, LanguageContextConfig> goodsNameMap = languageContextConfigService.mapByRelationIds(goodsType, goodsId);
        LanguageContextConfig languageContextConfig = goodsNameMap.get(languageCode);
        if (Objects.nonNull(languageContextConfig)){
            return languageContextConfig.getContext();
        }
        LanguageContextConfig defaultContent = goodsNameMap.get(GoodsTypeEnum.MULTILINGUAL.getDefaultLanguage());
        if (Objects.nonNull(defaultContent)){
            return defaultContent.getContext();
        }
        return "";
    }

    @Override
    public List<Integer> getIdsByGoodsName(String productName, Integer productType, Integer productId) {
        if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(productType)) {
            return goodsMultilingualService.listIdByName(productName, productId);
        } else {
            return goodsTraditionalService.listIdByName(productName, productId);
        }
    }

    @Override
    public void recoverRecyclesGoodsMultilingual(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        goodsMultilingualService.recoverBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void deleteRecyclesGoodsMultilingual(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        goodsMultilingualService.removeBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void deleteRecyclesGoodsTraditional(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        goodsTraditionalService.removeBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void recoverRecyclesGoodsTraditional(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        goodsTraditionalService.recoverBatchByIds(deleteBatchForm.getIds());
    }

}
