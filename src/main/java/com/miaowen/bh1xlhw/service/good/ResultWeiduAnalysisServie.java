package com.miaowen.bh1xlhw.service.good;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduBaseForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduDetailForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduMapForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultWeiduMapLangForm;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/19 16:57
 */
public interface ResultWeiduAnalysisServie {

    //根据维度表id获取图表配置(不含图表配置详情）
    PageVO<ResultWeiduMapForm> getByWeiduId(Integer pageInt, Integer pageSize, Integer weiduId);

    //根据图表配置id获取图表配置详情
    List<ResultWeiduMapLangForm.WeiduMapLang> getByWeiduMapIdLang(Integer weiduMapId);

    //根据图表配置id删除图表配置
    void deletByWeiduMapId(List<Integer> ids);

    //新增或修改图表配置
    void saveOrUpdate(ResultWeiduMapLangForm form);

    PageVO<ResultWeiduBaseForm> getByTag(String tag, Integer pageInt, Integer pageSize, boolean isMulti);

    void deleteByWeiduId(List<Integer> ids);

    void saveOrUpdate(ResultWeiduDetailForm form, boolean isMulti);

    List<ResultWeiduDetailForm.WeiduLang> getByWeiduIdLang(Integer weiduId);
}
