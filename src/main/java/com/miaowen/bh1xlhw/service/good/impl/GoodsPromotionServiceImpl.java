package com.miaowen.bh1xlhw.service.good.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.PlatformTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.RedisKeyEnum;
import com.miaowen.bh1xlhw.model.bo.ad.FacebookConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.GoogleConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.TikTokConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.goods.PromotionGoodsInfoBo;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionMultilingualForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionPageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionTraditionalForm;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsPromotionMultilingualVO;
import com.miaowen.bh1xlhw.model.vo.goods.GoodsPromotionTraditionalVO;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePageVO;
import com.miaowen.bh1xlhw.model.vo.operation.PlatformVO;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.good.GoodsPromotionService;
import com.miaowen.bh1xlhw.service.language.LanguageService;
import com.miaowen.bh1xlhw.service.operation.OperationService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.IdUtil;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * GoodsPromotionServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
@Slf4j
@Service
@AllArgsConstructor
public class GoodsPromotionServiceImpl implements GoodsPromotionService {
    private final IPriceService priceService;
    private final OperationUserService operationUserService;
    private final OperationService operationService;
    private final DomainService domainService;
    private final LanguageContextConfigService languageContextConfigService;
    private final GoodsPromotionMultilingualService goodsPromotionMultilingualService;
    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsPromotionTraditionalService goodsPromotionTraditionalService;
    private final IGoodsTraditionalService goodsTraditionalService;
    private final IArticleService iArticleService;
    private final LanguageService languageService;
    private final StringRedisTemplate stringRedisTemplate;


    @Override
    public void saveGoodPromotionMultilingual(GoodsPromotionMultilingualForm goodsPromotionMultilingualForm) {
        GoodsPromotionMultilingual goodsPromotionMultilingual = getGoodsPromotionMultilingual(goodsPromotionMultilingualForm);
        String mtgId = IdUtil.generateMtgId();
        goodsPromotionMultilingual.setTgId(mtgId);
        goodsPromotionMultilingualService.save(goodsPromotionMultilingual);
    }


    @Override
    public void updateGoodPromotionMultilingual(Integer id, GoodsPromotionMultilingualForm goodsPromotionMultilingualForm) {
        GoodsPromotionMultilingual goodsPromotionMultilingual = getGoodsPromotionMultilingual(goodsPromotionMultilingualForm);
        goodsPromotionMultilingual.setId(id);
        goodsPromotionMultilingualService.updateById(goodsPromotionMultilingual);
        GoodsPromotionMultilingual newGoodsPromotionMultilingual = goodsPromotionMultilingualService.getById(id);
        cleanRedisCache(GoodsTypeEnum.MULTILINGUAL, newGoodsPromotionMultilingual.getGoodsId(), newGoodsPromotionMultilingual.getTgId());
    }


    private static GoodsPromotionMultilingual getGoodsPromotionMultilingual(GoodsPromotionMultilingualForm goodsPromotionMultilingualForm) {
        GoodsPromotionMultilingual goodsPromotionMultilingual = BeanUtils.copy(goodsPromotionMultilingualForm, GoodsPromotionMultilingual.class);
        String platformType = goodsPromotionMultilingualForm.getPlatformType();

        //广告配置json
        String advertiseConfigInfo = "";
        if (PlatformTypeEnum.Google.getValue().equals(platformType)) {
            GoogleConfigInfoBO googleConfigInfo = goodsPromotionMultilingualForm.getGoogleConfigInfo();
            advertiseConfigInfo = JSONObject.toJSONString(googleConfigInfo);
        } else if (PlatformTypeEnum.Facebook.getValue().equals(platformType)) {
            FacebookConfigInfoBO facebookConfigInfo = goodsPromotionMultilingualForm.getFacebookConfigInfo();
            advertiseConfigInfo = JSONObject.toJSONString(facebookConfigInfo);
        } else if (PlatformTypeEnum.TikTok.getValue().equals(platformType)) {
            TikTokConfigInfoBO tikTokConfigInfo = goodsPromotionMultilingualForm.getTikTokConfigInfo();
            advertiseConfigInfo = JSONObject.toJSONString(tikTokConfigInfo);
        }
        goodsPromotionMultilingual.setAdvertiseConfigInfo(advertiseConfigInfo);
        return goodsPromotionMultilingual;
    }

    @Override
    public PageVO<GoodsPromotionMultilingualVO> pageGoodPromotionMultilingual(GoodsPromotionPageForm pageForm) {
        if (Objects.nonNull(pageForm.getGoodsName())) {
            List<Integer> goodsIds = goodsMultilingualService.listIdsByName(pageForm.getGoodsName());
            if (CollectionUtils.isEmpty(goodsIds)) {
                return new PageVO<>();
            }
            pageForm.setGoodsIds(goodsIds);
        }
        Page<GoodsPromotionMultilingual> pages = goodsPromotionMultilingualService.pageInfo(pageForm);
        List<GoodsPromotionMultilingual> records = pages.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>();
        }
        List<Integer> priceIds = new ArrayList<>();
        List<Integer> goodsIds = new ArrayList<>();
        List<Integer> domainIds = new ArrayList<>();
        List<Integer> operationIds = new ArrayList<>();
        List<Integer> articleIds = new ArrayList<>();
        records.forEach(record -> {
            goodsIds.add(record.getGoodsId());
            priceIds.add(record.getPriceId());
            domainIds.add(record.getDomainId());
            operationIds.add(record.getOperationId());
            if (!StringUtils.isEmpty(record.getArticleId())) {
                articleIds.addAll(Arrays.stream(record.getArticleId().split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            }
        });
        Map<Integer, Price> priceMap = priceService.mapByIds(priceIds);
        Map<Integer, GoodsMultilingual> goodsMultilingualMap = goodsMultilingualService.mapNameByIds(goodsIds);
        List<PlatformVO> platformVos = operationService.allPlatform();
        Map<Integer, PlatformVO> platformMap = StreamUtil.map(platformVos, PlatformVO::getId);
        Map<Integer, Domain> domainMap = domainService.mapNameByIds(domainIds);
        Map<Integer, OperationUser> operationUserMap = operationUserService.mapNameByIds(operationIds);
        Set<Integer> pUserIds = StreamUtil.fetchSet(operationUserMap.values(), OperationUser::getPid);
        Map<Integer, OperationUser> operationManagerMap = operationUserService.mapNameByIds(pUserIds);
        Map<Integer, List<LanguageContextConfig>> languageContextMap = languageContextConfigService.mapByRelationIds(LanguageContextConfig.RelationType.GOODS_MULTILINGUAL, goodsIds);
        Map<Integer, Article> articleMap = iArticleService.mapByIds(articleIds);
        List<GoodsPromotionMultilingualVO> multilingualVos = records.stream().map(goodsPromotionMultilingual -> {
            GoodsMultilingual goodsMultilingual = goodsMultilingualMap.get(goodsPromotionMultilingual.getGoodsId());
            Price price = priceMap.get(goodsPromotionMultilingual.getPriceId());
            PlatformVO platformVO = platformMap.get(goodsPromotionMultilingual.getPlatformId());
            Domain domain = domainMap.get(goodsPromotionMultilingual.getDomainId());
            OperationUser operationUser = operationUserMap.get(goodsPromotionMultilingual.getOperationId());
            List<LanguageContextConfig> languageContextConfigs = languageContextMap.get(goodsPromotionMultilingual.getGoodsId());
            GoodsPromotionMultilingualVO goodsPromotionMultilingualVO = GoodsPromotionMultilingualVO.transformVo(goodsPromotionMultilingual, goodsMultilingual, price,
                platformVO, domain, operationUser, operationManagerMap, languageContextConfigs);
            goodsPromotionMultilingualVO.setArticle(getArticleByIds(goodsPromotionMultilingual.getArticleId(), articleMap));
            return goodsPromotionMultilingualVO;
        }).collect(Collectors.toList());
        return new PageVO<GoodsPromotionMultilingualVO>(pages).convert(multilingualVos);
    }


    List<Article> getArticleByIds(String articleIds, Map<Integer, Article> articleMap) {
        if (StringUtils.isEmpty(articleIds)) {
            return Collections.emptyList();
        }
        List<Integer> collect = Arrays.stream(articleIds.split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        return collect.stream().map(articleMap::get).collect(Collectors.toList());
    }


    @Override
    public void deleteGoodPromotionMultilingual(Integer id) {
        goodsPromotionMultilingualService.logicRemoveById(id);

    }

    @Override
    public void copyGoodPromotionMultilingual(Integer id) {
        GoodsPromotionMultilingual goodsPromotionMultilingual = goodsPromotionMultilingualService.getExistById(id);
        if (Objects.isNull(goodsPromotionMultilingual)) {
            throw new BizException(ResultEnum.GOODS_PROMOTION_NOT_EXIST);
        }
        goodsPromotionMultilingual.setId(null);
        String mtgId = IdUtil.generateMtgId();
        goodsPromotionMultilingual.setTgId(mtgId);
        goodsPromotionMultilingual.setCreateTime(null);
        goodsPromotionMultilingual.setUpdateTime(null);
        goodsPromotionMultilingualService.save(goodsPromotionMultilingual);
    }


    @Override
    public void saveGoodPromotionTraditional(GoodsPromotionTraditionalForm goodsPromotionTraditionalForm) {
        GoodsPromotionTraditional goodsPromotionTraditional = getGoodsPromotionTraditional(goodsPromotionTraditionalForm);
        String ttgId = IdUtil.generateTtgId();
        goodsPromotionTraditional.setTgId(ttgId);
        goodsPromotionTraditionalService.save(goodsPromotionTraditional);
    }


    @Override
    public void updateGoodPromotionTraditional(Integer id, GoodsPromotionTraditionalForm goodsPromotionTraditionalForm) {
        GoodsPromotionTraditional goodsPromotionTraditional = getGoodsPromotionTraditional(goodsPromotionTraditionalForm);
        goodsPromotionTraditional.setId(id);
        goodsPromotionTraditionalService.updateById(goodsPromotionTraditional);
        GoodsPromotionTraditional newGoodsPromotionTraditional = goodsPromotionTraditionalService.getById(id);
        cleanRedisCache(GoodsTypeEnum.TRADITIONAL, newGoodsPromotionTraditional.getGoodsId(), newGoodsPromotionTraditional.getTgId());
    }


    private static GoodsPromotionTraditional getGoodsPromotionTraditional(GoodsPromotionTraditionalForm goodsPromotionTraditionalForm) {
        GoodsPromotionTraditional goodsPromotionTraditional = BeanUtils.copy(goodsPromotionTraditionalForm, GoodsPromotionTraditional.class);
        String platformType = goodsPromotionTraditionalForm.getPlatformType();

        //广告配置json
        String advertiseConfigInfo = "";
        if (PlatformTypeEnum.Google.getValue().equals(platformType)) {
            GoogleConfigInfoBO googleConfigInfo = goodsPromotionTraditionalForm.getGoogleConfigInfo();
            advertiseConfigInfo = JSONObject.toJSONString(googleConfigInfo);
        } else if (PlatformTypeEnum.Facebook.getValue().equals(platformType)) {
            FacebookConfigInfoBO facebookConfigInfo = goodsPromotionTraditionalForm.getFacebookConfigInfo();
            advertiseConfigInfo = JSONObject.toJSONString(facebookConfigInfo);
        } else if (PlatformTypeEnum.TikTok.getValue().equals(platformType)) {
            TikTokConfigInfoBO tikTokConfigInfo = goodsPromotionTraditionalForm.getTikTokConfigInfo();
            advertiseConfigInfo = JSONObject.toJSONString(tikTokConfigInfo);
        }
        goodsPromotionTraditional.setAdvertiseConfigInfo(advertiseConfigInfo);
        return goodsPromotionTraditional;
    }

    @Override
    public PageVO<GoodsPromotionTraditionalVO> pageGoodPromotionTraditional(GoodsPromotionPageForm pageForm) {
        if (Objects.nonNull(pageForm.getGoodsName())) {
            List<Integer> goodsIds = goodsTraditionalService.listIdsByName(pageForm.getGoodsName());
            if (CollectionUtils.isEmpty(goodsIds)) {
                return new PageVO<>();
            }
            pageForm.setGoodsIds(goodsIds);
        }
        Page<GoodsPromotionTraditional> pages = goodsPromotionTraditionalService.pageInfo(pageForm);
        List<GoodsPromotionTraditional> records = pages.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageVO<>();
        }
        List<Integer> priceIds = new ArrayList<>();
        List<Integer> goodsIds = new ArrayList<>();
        List<Integer> domainIds = new ArrayList<>();
        List<Integer> operationIds = new ArrayList<>();
        List<Integer> articleIds = new ArrayList<>();
        records.forEach(record -> {
            goodsIds.add(record.getGoodsId());
            priceIds.add(record.getPriceId());
            domainIds.add(record.getDomainId());
            operationIds.add(record.getOperationId());
            if (!StringUtils.isEmpty(record.getArticleId())) {
                articleIds.addAll(Arrays.stream(record.getArticleId().split(","))
                        .map(String::trim)
                        .map(Integer::parseInt)
                        .collect(Collectors.toList()));
            }
        });
        Map<Integer, Price> priceMap = priceService.mapByIds(priceIds);
        Map<Integer, GoodsTraditional> goodsTraditionalMap = goodsTraditionalService.mapNameByIds(goodsIds);
        List<PlatformVO> platformVos = operationService.allPlatform();
        Map<Integer, PlatformVO> platformMap = StreamUtil.map(platformVos, PlatformVO::getId);        Map<Integer, Domain> domainMap = domainService.mapNameByIds(domainIds);
        Map<Integer, OperationUser> operationUserMap = operationUserService.mapNameByIds(operationIds);
        Set<Integer> pUserIds = StreamUtil.fetchSet(operationUserMap.values(), OperationUser::getPid);
        Map<Integer, OperationUser> operationManagerMap = operationUserService.mapNameByIds(pUserIds);
        Map<Integer, Article> articleMap = iArticleService.mapByIds(articleIds);

        List<GoodsPromotionTraditionalVO> traditionalVos = records.stream().map(goodsPromotionTraditional -> {
            GoodsTraditional goodsTraditional = goodsTraditionalMap.get(goodsPromotionTraditional.getGoodsId());
            Price price = priceMap.get(goodsPromotionTraditional.getPriceId());
            PlatformVO platformVO = platformMap.get(goodsPromotionTraditional.getPlatformId());
            Domain domain = domainMap.get(goodsPromotionTraditional.getDomainId());
            OperationUser operationUser = operationUserMap.get(goodsPromotionTraditional.getOperationId());
            GoodsPromotionTraditionalVO goodsPromotionTraditionalVO = GoodsPromotionTraditionalVO.transformVo(goodsPromotionTraditional, goodsTraditional, price,
                platformVO, domain, operationUser, operationManagerMap);
            goodsPromotionTraditionalVO.setArticle(getArticleByIds(goodsPromotionTraditional.getArticleId(), articleMap));
            return goodsPromotionTraditionalVO;
        }).collect(Collectors.toList());
        return new PageVO<GoodsPromotionTraditionalVO>(pages).convert(traditionalVos);
    }


    @Override
    public void deleteGoodPromotionTraditional(Integer id) {
        goodsPromotionTraditionalService.logicRemoveById(id);

    }

    @Override
    public void copyGoodPromotionTraditional(Integer id) {
        GoodsPromotionTraditional goodsPromotionTraditional = goodsPromotionTraditionalService.getExistById(id);
        if (Objects.isNull(goodsPromotionTraditional)) {
            throw new BizException(ResultEnum.GOODS_PROMOTION_NOT_EXIST);
        }
        goodsPromotionTraditional.setId(null);
        String ttgId = IdUtil.generateTtgId();
        goodsPromotionTraditional.setTgId(ttgId);
        goodsPromotionTraditional.setCreateTime(null);
        goodsPromotionTraditional.setUpdateTime(null);
        goodsPromotionTraditionalService.save(goodsPromotionTraditional);
    }


    @Override
    public PromotionGoodsInfoBo getPromotionGoodsInfo(String tgId, Integer goodsType) {
        if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
            GoodsPromotionMultilingual goodsPromotionMultilingual = goodsPromotionMultilingualService.getByTgId(tgId);
            return BeanUtils.copy(goodsPromotionMultilingual, PromotionGoodsInfoBo.class);
        } else {
            GoodsPromotionTraditional goodsPromotionTraditional = goodsPromotionTraditionalService.getByTgId(tgId);
            return BeanUtils.copy(goodsPromotionTraditional, PromotionGoodsInfoBo.class);
        }
    }

    @Override
    public List<PromotionGoodsInfoBo> getPromotionGoodsInfos(Integer goodsType) {
        if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(goodsType)) {
            List<GoodsPromotionMultilingual> list = goodsPromotionMultilingualService.listAll();
            return BeanUtils.copyList(list, PromotionGoodsInfoBo.class);
        } else {
            List<GoodsPromotionTraditional> list = goodsPromotionTraditionalService.listAll();
            return BeanUtils.copyList(list, PromotionGoodsInfoBo.class);
        }
    }


    @Override
    public void deleteRecyclesGoodsPromotionMultilingual(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        goodsPromotionMultilingualService.removeBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void recoverRecyclesGoodsPromotionMultilingual(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        goodsPromotionMultilingualService.recoverBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void recoverRecyclesGoodsPromotionTraditional(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        goodsPromotionTraditionalService.recoverBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void deleteRecyclesGoodsPromotionTraditional(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        goodsPromotionTraditionalService.removeBatchByIds(deleteBatchForm.getIds());
    }


    public void cleanRedisCache(GoodsTypeEnum goodsTypeEnum,Integer goodsId, String tgId) {
        List<LanguagePageVO> languageList = languageService.listAll();
        Set<String> keys = new HashSet<>();
        //删除文章缓存
        for (LanguagePageVO languagePageVO : languageList) {
            String key = RedisKeyEnum.ARTICLE_DETAIL_LANGUAGE.getKey(
                goodsTypeEnum.getValue(),
                goodsId,
                languagePageVO.getCode(),
                tgId);
            keys.add(key);
        }
        //删除广告参数缓存
        String key = RedisKeyEnum.PLATFORM_ADVERTISE.getKey(tgId);
        keys.add(key);
        log.debug("文章删除redis key:{}", JSONObject.toJSONString(keys));
        stringRedisTemplate.delete(keys);
    }


}
