package com.miaowen.bh1xlhw.service.good.impl;

import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.LangTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysis;
import com.miaowen.bh1xlhw.model.query.goods.ResultSettingWrongBaseForm;
import com.miaowen.bh1xlhw.model.query.goods.ResultSettingWrongDetailForm;
import com.miaowen.bh1xlhw.model.vo.goods.ResultWrongVo;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePageVO;
import com.miaowen.bh1xlhw.repository.ResultWrongAnalysisService;
import com.miaowen.bh1xlhw.service.good.ResultSettingService;
import com.miaowen.bh1xlhw.service.language.LanguageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品结果设置
 *
 * @Author：huanglong
 * @Date：2025/5/16 10:29
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResultSettingServiceImpl implements ResultSettingService {

    private final ResultWrongAnalysisService resultWrongAnalysisService;
    private final LanguageService languageService;

    public List<ResultWrongVo> getResultWrongVo(String tag, boolean isMultiLanguage) {
        List<ResultWrongVo> result = new ArrayList<>();
        //入参 分类标签  是否是多语言类型
        List<ResultWrongAnalysis> wrongAnalyses = resultWrongAnalysisService.getListByTag(tag, isMultiLanguage);
        if (CollectionUtils.isEmpty(wrongAnalyses)) {
            return result;
        }
        // 分组放入数据
        Map<String, List<ResultWrongAnalysis>> groupedByExamNum = wrongAnalyses.stream()
                .collect(Collectors.groupingBy(ResultWrongAnalysis::getExamNum));
        groupedByExamNum.forEach((examNum, analysisList) -> {
            ResultWrongVo vo = new ResultWrongVo();
            vo.setExamNum(examNum);
            vo.setExamAnswer(analysisList.get(0).getExamAnswer());
            vo.setScore(analysisList.get(0).getScore());
            result.add(vo);
        });
        result.sort(Comparator.comparingInt(
                r -> Integer.parseInt(r.getExamNum())
        ));

        return result;
    }

    @Override
    public ResultVO<Void> addResultWrongVo(ResultSettingWrongDetailForm form, boolean isMultiLanguage) {
        List<ResultWrongAnalysis> byExamNum = resultWrongAnalysisService.findByExamNum(form.getTag(), form.getExamNum(), isMultiLanguage);
        if (!CollectionUtils.isEmpty(byExamNum)) {
            //题号重复
            return ResultVO.fail(ResultEnum.DataCheckFail,"题号重复");
        }
        List<ResultWrongAnalysis> entities = new ArrayList<>();
        for (ResultSettingWrongDetailForm.WrongDetail detail : form.getDetails()) {
            ResultWrongAnalysis analysis = new ResultWrongAnalysis();
            analysis.setExamNum(form.getExamNum());
            analysis.setExamAnswer(form.getExamAnswer());
            analysis.setCategoryTag(form.getTag());
            analysis.setLangType(detail.getLangType());
            analysis.setExamExplain(detail.getExamExplain());
            analysis.setScore(form.getScore());
            analysis.setType(isMultiLanguage ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
            entities.add(analysis);
        }
        resultWrongAnalysisService.saveBatch(entities);
        return ResultVO.success();
    }

    @Override
    public Void editResultWrongVo(ResultSettingWrongBaseForm form, boolean isMultiLanguage) {
        resultWrongAnalysisService.editExamAnswer(form.getTag(), form.getExamNum(), form.getScore(), form.getExamAnswer(), isMultiLanguage);
        return null;
    }

    @Override
    public Void deleteResultWrongVo(ResultSettingWrongBaseForm form, boolean isMultiLanguage) {
        resultWrongAnalysisService.deleteByExamNum(form.getTag(), form.getExamNum(), isMultiLanguage);
        return null;
    }

    @Override
    public Void editResultWrongDetailVo(ResultSettingWrongDetailForm form, boolean isMultiLanguage) {
        if (!CollectionUtils.isEmpty(form.getDetails())) {
            resultWrongAnalysisService.deleteByExamNum(form.getTag(), form.getExamNum(), isMultiLanguage);
            List<ResultWrongAnalysis> entities = new ArrayList<>();
            for (ResultSettingWrongDetailForm.WrongDetail detail : form.getDetails()) {
                ResultWrongAnalysis analysis = new ResultWrongAnalysis();
                analysis.setExamNum(form.getExamNum());
                analysis.setExamAnswer(form.getExamAnswer());
                analysis.setCategoryTag(form.getTag());
                analysis.setLangType(detail.getLangType());
                analysis.setExamExplain(detail.getExamExplain());
                analysis.setScore(form.getScore());
                analysis.setType(isMultiLanguage ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
                entities.add(analysis);
            }
            resultWrongAnalysisService.saveBatch(entities);
        }
        return null;
    }

    @Override
    public ResultSettingWrongDetailForm getResultWrongDetailVo(ResultSettingWrongBaseForm form, boolean isMultiLanguage) {
        ResultSettingWrongDetailForm result = new ResultSettingWrongDetailForm();
        List<ResultWrongAnalysis> byExamNum = resultWrongAnalysisService.findByExamNum(form.getTag(), form.getExamNum(), isMultiLanguage);
        if (CollectionUtils.isEmpty(byExamNum)) {
            return result;
        }
        List<ResultSettingWrongDetailForm.WrongDetail> details = new ArrayList<>();
        result.setExamNum(form.getExamNum());
        result.setTag(form.getTag());
        for (ResultWrongAnalysis resultWrongAnalysis : byExamNum) {
            result.setScore(resultWrongAnalysis.getScore());
            result.setExamAnswer(resultWrongAnalysis.getExamAnswer());
            ResultSettingWrongDetailForm.WrongDetail detail = new ResultSettingWrongDetailForm.WrongDetail();
            detail.setExamExplain(resultWrongAnalysis.getExamExplain());
            detail.setLangType(resultWrongAnalysis.getLangType());
            details.add(detail);
        }
        result.setDetails(details);
        return result;
    }
}
