package com.miaowen.bh1xlhw.service.good.impl;

import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.ResultFurtherReading;
import com.miaowen.bh1xlhw.model.query.goods.ResultReadingForm;
import com.miaowen.bh1xlhw.repository.ResultFurtherReadingService;
import com.miaowen.bh1xlhw.service.good.ResultReadingService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/19 14:52
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResultReadingServiceImpl implements ResultReadingService {

    private final ResultFurtherReadingService readingService;

    @Override
    public ResultReadingForm getByTag(String tag, boolean isMulti) {
        ResultReadingForm result = new ResultReadingForm();
        result.setTag(tag);
        List<ResultFurtherReading> readings = readingService.getByTag(tag, isMulti);
        if (CollectionUtils.isEmpty(readings)) {
            return result;
        }
        List<ResultReadingForm.Read> details = new ArrayList<>();
        readings.forEach(
                t -> {
                    details.add(BeanUtils.copy(t, ResultReadingForm.Read.class));
                }
        );
        result.setDetails(details);
        return result;
    }

    @Override
    public void save(ResultReadingForm resultReadingForm, boolean isMulti) {
        readingService.deleteByTag(resultReadingForm.getTag(), isMulti);
        if (!CollectionUtils.isEmpty(resultReadingForm.getDetails())) {
            for (ResultReadingForm.Read detail : resultReadingForm.getDetails()) {
                ResultFurtherReading entity = BeanUtils.copy(detail, ResultFurtherReading.class);
                entity.setTag(resultReadingForm.getTag());
                entity.setType(isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
                readingService.saveEntity(entity);
            }
        }
    }
}
