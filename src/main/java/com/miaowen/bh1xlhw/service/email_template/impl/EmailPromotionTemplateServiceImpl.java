package com.miaowen.bh1xlhw.service.email_template.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsInfoBo;
import com.miaowen.bh1xlhw.model.dto.EmailSendDto;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.entity.order.OrderEnvironmental;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email_template.DetailsForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionSendForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplateAddForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplatePageForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailPromotionTemplateDetailsVO;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailPromotionTemplateVo;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email.EmailSendService;
import com.miaowen.bh1xlhw.service.email.EmailService;
import com.miaowen.bh1xlhw.service.email_template.EmailPromotionTemplateService;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.service.mq.EmailTaskProducer;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.SecurityUtil;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import com.mysql.cj.util.StringUtils;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;
import static com.miaowen.bh1xlhw.constant.CommonConstant.ID;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@AllArgsConstructor
@Service
public class EmailPromotionTemplateServiceImpl implements EmailPromotionTemplateService {


    private static final Logger log = LoggerFactory.getLogger(EmailPromotionTemplateServiceImpl.class);
    @Resource
    IEmailPromotionTemplateContentService iEmailPromotionTemplateContentService;

    @Resource
    IEmailPromotionTemplateService iEmailPromotionTemplateService;

    @Resource
    IEmailPromotionSendRecordService iEmailPromotionSendRecordService;

    @Resource
    IEmailOrderSuccessService emailOrderSuccessService;

    @Resource
    IEmailOrderUnpaidService emailOrderUnpaidService;

    @Resource
    IEmailService iEmailService;

    @Resource
    EmailService emailService;

    @Resource
    EmailSendService emailSendService;
    @Resource
    DomainService domainService;

    @Resource
    private EmailTaskProducer producer;

    @Resource
    ILanguageService iLanguageService;

    private final GoodsService goodsService;
    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;

    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;

    private final IOrderEnvironmentalService orderEnvironmentalService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(EmailPromotionTemplateAddForm form) {
        EmailPromotionTemplate emailPromotionTemplate = form.getEmailPromotionTemplate();
        iEmailPromotionTemplateService.save(emailPromotionTemplate);
        Integer emailPromotionTemplateId = emailPromotionTemplate.getId();
        List<EmailPromotionTemplateContent> emailPromotionTemplateContents = form.getEmailPromotionTemplateContents();
        for (EmailPromotionTemplateContent emailPromotionTemplateContent : emailPromotionTemplateContents) {
            emailPromotionTemplateContent.setEmailPromotionTemplateId(emailPromotionTemplateId);
        }
        iEmailPromotionTemplateContentService.saveBatch(emailPromotionTemplateContents);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(EmailPromotionTemplateAddForm form) {
        EmailPromotionTemplate emailPromotionTemplate = form.getEmailPromotionTemplate();
        iEmailPromotionTemplateService.updateById(emailPromotionTemplate);

        Integer emailPromotionTemplateId = emailPromotionTemplate.getId();
        List<EmailPromotionTemplateContent> emailPromotionTemplateContents = form.getEmailPromotionTemplateContents();
        for (EmailPromotionTemplateContent emailPromotionTemplateContent : emailPromotionTemplateContents) {
            emailPromotionTemplateContent.setId(null);
            emailPromotionTemplateContent.setEmailPromotionTemplateId(emailPromotionTemplateId);
        }
        //先删内容表
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<EmailPromotionTemplateContent> wrapper =
                new UpdateWrapper<EmailPromotionTemplateContent>().in(CommonConstant.EMAIL_PROMOTION_TEMPLATE_ID, emailPromotionTemplate.getId()).set(DELETE_TIME, System.currentTimeMillis() / 1000);
        iEmailPromotionTemplateContentService.update(wrapper);
        //再重新写入内容表
        iEmailPromotionTemplateContentService.saveBatch(emailPromotionTemplateContents);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<EmailPromotionTemplate> wrapper = new UpdateWrapper<EmailPromotionTemplate>().in(ID, deleteBatchForm.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        iEmailPromotionTemplateService.update(wrapper);
    }

    @Override
    public ResultVO<?> page(EmailPromotionTemplatePageForm form) {

        IPage<EmailPromotionTemplate> page = new Page<>(form.getPageInt(), form.getPageSize());
        LambdaQueryWrapper<EmailPromotionTemplate> qw = new LambdaQueryWrapper<>();
        qw.like(!StringUtils.isNullOrEmpty(form.getName()), EmailPromotionTemplate::getName, form.getName());
        qw.eq(form.getGoodsType() != null, EmailPromotionTemplate::getGoodsType, form.getGoodsType());
        qw.eq(form.getGoodsTag() != null, EmailPromotionTemplate::getGoodsTagId, form.getGoodsTag());
        qw.eq(EmailPromotionTemplate::getDeleteTime, 0);
        if (form.getStartTime() != null && form.getEndTime() != null) {
            if (!DateUtils.isSameMonth(form.getStartTime(), form.getEndTime())) {
                //跨月 则取开始时间当月
                qw.between(EmailPromotionTemplate::getCreateTime, form.getStartTime(), DateUtils.getLastDayOfMonth(form.getStartTime()));
            } else {
                qw.between(EmailPromotionTemplate::getCreateTime, form.getStartTime(), form.getEndTime());
            }
        } else {
            //没有的话则需要放入时间范围 默认查当月
            qw.between(EmailPromotionTemplate::getCreateTime, DateUtils.getFirstDayOfMonth(), new Date());
        }
        qw.orderByDesc(Arrays.asList(EmailPromotionTemplate::getSort, EmailPromotionTemplate::getId));

        page = iEmailPromotionTemplateService.page(page, qw);

        List<EmailPromotionTemplate> list = page.getRecords();
        List<EmailPromotionTemplateVo> emailPromotionTemplateVos = changeVo(list);

        return ResultVO.successForPage(emailPromotionTemplateVos, page.getTotal(), form.getPageInt(), form.getPageSize());
    }

    private List<EmailPromotionTemplateVo> changeVo(List<EmailPromotionTemplate> list) {
        List<EmailPromotionTemplateVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<Integer> goodsIds = list.stream().map(EmailPromotionTemplate::getGoodsId).collect(Collectors.toList());
        List<Integer> goodsTagIds = list.stream().map(EmailPromotionTemplate::getGoodsTagId).collect(Collectors.toList());
        Map<Integer, GoodsMultilingual> integerGoodsMultilingualMap = goodsMultilingualService.mapNameByIds(goodsIds);
        Map<Integer, GoodsTraditional> integerGoodsTraditionalMap = goodsTraditionalService.mapNameByIds(goodsIds);
        Map<Integer, GoodsCategoryMultilingual> integerGoodsTypeMultilingualMap = goodsCategoryMultilingualService.mapNameByIds(goodsTagIds);
        Map<Integer, GoodsCategoryTraditional> integerGoodsTypeTraditionalMap = goodsCategoryTraditionalService.mapNameByIds(goodsTagIds);
        for (EmailPromotionTemplate emailPromotionTemplate : list) {
            EmailPromotionTemplateVo vo = new EmailPromotionTemplateVo();
            BeanUtils.copyProperties(emailPromotionTemplate, vo);
            vo.setDomain(Integer.valueOf(emailPromotionTemplate.getDomain()));
            if (Objects.equals(emailPromotionTemplate.getGoodsType(), GoodsTypeEnum.MULTILINGUAL.getValue())) {
                vo.setGoodsName(integerGoodsMultilingualMap.get(emailPromotionTemplate.getGoodsId()).getName());
                vo.setGoodsTag(integerGoodsTypeMultilingualMap.get(emailPromotionTemplate.getGoodsTagId()).getName());
            } else {
                vo.setGoodsName(integerGoodsTraditionalMap.get(emailPromotionTemplate.getGoodsId()).getName());
                vo.setGoodsTag(integerGoodsTypeTraditionalMap.get(emailPromotionTemplate.getGoodsTagId()).getName());
            }
            result.add(vo);
        }
        return result;
    }

    @Override
    public void updateState(UpdateStateForm form) {
        EmailPromotionTemplate updateParam = EmailPromotionTemplate.builder().status(form.getStatus()).build();
        updateParam.setId(form.getId());
        iEmailPromotionTemplateService.updateById(updateParam);
    }

    @Override
    public EmailPromotionTemplateDetailsVO getDetails(DetailsForm form) {
        EmailPromotionTemplateDetailsVO vo = new EmailPromotionTemplateDetailsVO();

        EmailPromotionTemplate emailPromotionTemplate = iEmailPromotionTemplateService.getById(form.getId());
        List<EmailPromotionTemplate> list = new ArrayList<>();
        list.add(emailPromotionTemplate);
        List<EmailPromotionTemplateVo> emailPromotionTemplateVos = changeVo(list);
        vo.setEmailPromotionTemplate(emailPromotionTemplateVos.get(0));
        QueryWrapper<EmailPromotionTemplateContent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CommonConstant.EMAIL_PROMOTION_TEMPLATE_ID, form.getId());
        queryWrapper.eq(CommonConstant.DELETE_TIME, 0);

        List<EmailPromotionTemplateContent> emailPromotionTemplateContents =
                iEmailPromotionTemplateContentService.list(queryWrapper);

        vo.setEmailPromotionTemplateContents(emailPromotionTemplateContents);

        return vo;
    }

    @Override
    public void send(EmailPromotionSendForm form) {
        //获取邮箱模板信息
        EmailPromotionTemplateDetailsVO details = this.getDetails(new DetailsForm(form.getId()));
        EmailPromotionTemplateVo emailPromotionTemplate = details.getEmailPromotionTemplate();
        if (emailPromotionTemplate.getDomain() == null) {
            return;
        }
        //根据推送对象获取邮箱  需要筛选是否同分类 根据月份定位表单数据
        Domain domainEntity = domainService.getById(emailPromotionTemplate.getDomain());
        if (domainEntity == null || domainEntity.getEmailIds() == null) {
            return;
        }
        List<Email> emailList = iEmailService.getEmailByIds(domainEntity.getEmailIds());
        if (CollectionUtils.isEmpty(emailList)) {
            return;
        }
        List<Integer> orderIds = new ArrayList<>();
        Map<String, Integer> orderEmailMap = new HashMap<>();
        Map<String, String> emails = getEmails(form, emailPromotionTemplate.getGoodsType(), emailPromotionTemplate.getGoodsTag(), orderEmailMap, orderIds);

        if (CollectionUtils.isEmpty(emails)) {
            return;
        }
        Map<String, EmailPromotionTemplateContent> map =
                StreamUtil.map(details.getEmailPromotionTemplateContents(), EmailPromotionTemplateContent::getLanguageCode);
        Map<Long, OrderEnvironmental> environmentalMap;
        if (!orderIds.isEmpty()) {
            List<OrderEnvironmental> orderEnvironmentals = orderEnvironmentalService.listByIds(orderIds);
            environmentalMap = StreamUtil.map(orderEnvironmentals, OrderEnvironmental::getId);
        } else {
            environmentalMap = new HashMap<>();
        }
        GoodsInfoBo goodsInfo = goodsService.getGoodsInfo(emailPromotionTemplate.getGoodsId(), emailPromotionTemplate.getGoodsType());
        EmailPromotionSendRecord record = new EmailPromotionSendRecord();
        record.setEmailPromotionTemplateId(emailPromotionTemplate.getId());
        record.setProObj(form.getProObj());
        record.setTotalNum(emails.size());
        record.setName(emailPromotionTemplate.getName());
        record.setProPerson(SecurityUtil.currentUser().getUsername());
        EmailPromotionTemplateContent emailPromotionTemplateContent = map.get(GoodsTypeEnum.getByValue(emailPromotionTemplate.getGoodsType()).getDefaultLanguage());
        if (Objects.isNull(emailPromotionTemplateContent)){
            throw new BizException("默认语言内容不存在,语言代码:"+GoodsTypeEnum.getByValue(emailPromotionTemplate.getGoodsType()).getDefaultLanguage());
        }
        record.setTitle(emailPromotionTemplateContent.getTitle());
        //记录消息数据
        iEmailPromotionSendRecordService.save(record);
        //使用消息队列发送
        emails.forEach((to, languageCode) -> {
            //发送邮件
            EmailPromotionTemplateContent contentEmail;
            if (map.containsKey(languageCode)) {
                contentEmail = map.get(languageCode);
            } else {
                if (emailPromotionTemplate.getGoodsType() == 1) {
                    contentEmail = map.get(SystemConstant.DEFAULT_LANGUAGE_MULTI);
                } else {
                    contentEmail = map.get(SystemConstant.DEFAULT_LANGUAGE);
                }
            }
            String title = contentEmail.getTitle();
            String content = contentEmail.getContent();
            EmailSendDto task = new EmailSendDto();
            task.setId(record.getId());
            task.setToEmail(to);
            task.setTitle(title);
            task.setContent(content);
            task.setEmailEntity(emailList.get(0));
            if (orderEmailMap.containsKey(to)) {
                Integer orderId = orderEmailMap.get(to);
                if (environmentalMap.containsKey(Long.valueOf(orderId))) {
                    OrderEnvironmental orderEnvironmental = environmentalMap.get(Long.valueOf(orderId));
                    if (Objects.nonNull(orderEnvironmental.getWebSite())
                            && Objects.nonNull(goodsInfo)
                            && Objects.nonNull(goodsInfo.getWebPackageName())
                            && Objects.nonNull(orderEnvironmental.getLanguageCode())) {
                        String unsubscribeUrl = emailService.getUnsubscribeUrl(orderEnvironmental.getWebSite(), to, goodsInfo.getWebPackageName(), orderEnvironmental.getLanguageCode());
                        task.setUnsubscribeUrl(unsubscribeUrl);
                    }
                }
            }
            log.info("发送邮件：{}", JSON.toJSON(task));
            producer.createEmailTask(task);
        });
    }


    Map<String, String> getEmails(EmailPromotionSendForm form, Integer goodsType, String goodsTag, Map<String, Integer> orderEmailMap, List<Integer> orderIds) {
        try {
            Map<String, String> result = new HashMap<>();
            MonthShardingTableNameHandler.setParams(parseYearMonthToDate(form.getSendMonth()));
            if (form.getProObj() == 1) {
                //已支付邮箱
                LambdaQueryWrapper<EmailOrderSuccess> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(EmailOrderSuccess::getGoodsType, goodsType);
                if (form.getFilterUser() == 1) {
                    //过滤同商品分类
                    wrapper.notIn(EmailOrderSuccess::getGoodsId, getGoodsIdByGoodsName(goodsType, goodsTag));
                }
                List<EmailOrderSuccess> list = emailOrderSuccessService.list(wrapper);
                list.forEach(t -> {
                    orderIds.add(Math.toIntExact(t.getOrderId()));
                    orderEmailMap.put(t.getEmail(), Math.toIntExact(t.getOrderId()));
                    result.put(t.getEmail(), t.getLanguageCode());
                });
            } else {
                //未支付邮箱
                LambdaQueryWrapper<EmailOrderUnpaid> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(EmailOrderUnpaid::getGoodsType, goodsType);
                if (form.getFilterUser() == 1) {
                    //过滤同商品分类
                    wrapper.notIn(EmailOrderUnpaid::getGoodsId, getGoodsIdByGoodsName(goodsType, goodsTag));
                }
                List<EmailOrderUnpaid> list = emailOrderUnpaidService.list(wrapper);
                list.forEach(t -> {
                    orderIds.add(Math.toIntExact(t.getOrderId()));
                    orderEmailMap.put(t.getEmail(), Math.toIntExact(t.getOrderId()));
                    result.put(t.getEmail(), t.getLanguageCode());
                });
            }
            return result;
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }

    }


    public Date parseYearMonthToDate(String yearMonthStr) {
        // 解析为 YearMonth 对象
        YearMonth yearMonth = YearMonth.parse(yearMonthStr);

        // 转换为当月的第一天（你也可以选择其他日期，如最后一天）
        // 并转换为 Date 类型
        return Date.from(
                yearMonth.atDay(1)          // 转为 LocalDate (当月第一天)
                        .atStartOfDay()             // 转为当天的开始时间 (00:00)
                        .atZone(ZoneId.systemDefault()) // 转为 ZonedDateTime
                        .toInstant()                // 转为 Instant
        );
    }

    private List<Integer> getGoodsIdByGoodsName(Integer goodsType, String goodsTag) {
        List<Integer> goodsIdList = new ArrayList<>();
        if (goodsType == 1) {
            List<GoodsMultilingual> multilingualList = goodsMultilingualService.list();
            if (!CollectionUtils.isEmpty(multilingualList)) {
                List<GoodsCategoryMultilingual> goodsCategoryMultilinguals =
                        goodsCategoryMultilingualService.listByIds(multilingualList.stream()
                                .map(GoodsMultilingual::getGoodsCategoryId)
                                .collect(Collectors.toList()));
                multilingualList.forEach(goodsMultilingual -> {
                    if (goodsCategoryMultilinguals.stream().anyMatch(goodsTypeMultilingual -> goodsTypeMultilingual.getType().equals(goodsTag))) {
                        goodsIdList.add(goodsMultilingual.getId());
                    }
                });
            }
        }
        if (goodsType == 2) {
            List<GoodsTraditional> traditions = goodsTraditionalService.list();
            if (!CollectionUtils.isEmpty(traditions)) {
                List<GoodsCategoryTraditional> goodsCategoryTraditionals =
                        goodsCategoryTraditionalService.listByIds(traditions.stream()
                                .map(GoodsTraditional::getGoodsCategoryId)
                                .collect(Collectors.toList()));
                traditions.forEach(goodsTraditional -> {
                    if (goodsCategoryTraditionals.stream().anyMatch(goodsTypeTraditional -> goodsTypeTraditional.getType().equals(goodsTag))) {
                        goodsIdList.add(goodsTraditional.getId());
                    }
                });
            }
        }
        return goodsIdList;
    }

}




