package com.miaowen.bh1xlhw.service.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.email_template.EmailSendRecordForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailSendRecordVo;

import java.util.Date;
import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 14:42
 */
public interface EmailSendRecordService {

    PageVO<EmailSendRecordVo> page(EmailSendRecordForm pageForm);

    List<EmailSendRecordVo> listByOrderId(Integer orderId);
}
