package com.miaowen.bh1xlhw.service.email_template.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionSendRecordForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailPromotionSendRecordVo;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailPromotionTemplateVo;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email_template.EmailPromotionSendRecordService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/21 14:44
 */
@Service
@AllArgsConstructor
public class EmailPromotionSendRecordServiceImpl implements EmailPromotionSendRecordService {

    private final IEmailPromotionTemplateService iEmailPromotionTemplateService;
    private final IEmailPromotionSendRecordService iEmailPromotionSendRecordService;

    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;

    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;

    @Override
    public PageVO<EmailPromotionSendRecordVo> page(EmailPromotionSendRecordForm pageForm) {
        try {
            LambdaQueryWrapper<EmailPromotionSendRecord> wrapper = new LambdaQueryWrapper<>();
            if (Objects.nonNull(pageForm.getGoodsName())
                    || Objects.nonNull(pageForm.getGoodsTag())
                    || pageForm.getGoodsType() != null) {
                List<Integer> templateIds = getTemplateIds(pageForm);
                if (CollectionUtils.isEmpty(templateIds)) {
                    return new PageVO<>();
                }
                wrapper.in(EmailPromotionSendRecord::getEmailPromotionTemplateId, templateIds);
            }
            if (!StringUtils.isEmpty(pageForm.getTitle())) {
                wrapper.like(EmailPromotionSendRecord::getTitle, pageForm.getTitle());
            }
            if (pageForm.getStartTime() != null && pageForm.getEndTime() != null) {
                MonthShardingTableNameHandler.setParams(DateUtils.getData(pageForm.getStartTime()));
                if (!DateUtils.isSameMonth(DateUtils.getData(pageForm.getStartTime()),DateUtils.getData( pageForm.getEndTime()))) {
                    //跨月 则取开始时间当月
                    wrapper.between(EmailPromotionSendRecord::getCreateTime, pageForm.getStartTime(), DateUtils.getLastDayOfMonth(DateUtils.getData(pageForm.getStartTime())));
                } else {
                    wrapper.between(EmailPromotionSendRecord::getCreateTime, pageForm.getStartTime(), pageForm.getEndTime());
                }
            } else {
                //没有的话则需要放入时间范围 默认查当月
                wrapper.between(EmailPromotionSendRecord::getCreateTime, DateUtils.getFirstDayOfMonth(), new Date());
            }
            wrapper.eq(EmailPromotionSendRecord::getDeleteTime, 0);
            long count = iEmailPromotionSendRecordService.count(wrapper);
            if (count == 0) {
                return new PageVO<>();
            }
            wrapper.orderByDesc(EmailPromotionSendRecord::getCreateTime);
            // 构建分页查询的SQL语句
            int offset = (pageForm.getPageInt() - 1) * pageForm.getPageSize();
            int limit = pageForm.getPageSize();
            // 添加LIMIT条件
            wrapper.last("LIMIT " + offset + "," + limit);
            List<EmailPromotionSendRecord> records = iEmailPromotionSendRecordService.list(wrapper);
            Page<EmailPromotionSendRecordVo> page = new Page<>(pageForm.getPageInt(), pageForm.getPageSize());
            page.setRecords(getVo(records));
            page.setTotal(count);
            return new PageVO<>(page, EmailPromotionSendRecordVo.class);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    private List<Integer> getTemplateIds(EmailPromotionSendRecordForm pageForm) {
        LambdaQueryWrapper<EmailPromotionTemplate> wrapper = Wrappers.lambdaQuery();
        if (!StringUtils.isEmpty(pageForm.getGoodsName())) {
            wrapper.eq(EmailPromotionTemplate::getGoodsId, pageForm.getGoodsName());
        }
        if (!StringUtils.isEmpty(pageForm.getGoodsTag())) {
            wrapper.eq(EmailPromotionTemplate::getGoodsTagId, pageForm.getGoodsTag());
        }
        if (pageForm.getGoodsType() != null) {
            wrapper.eq(EmailPromotionTemplate::getGoodsType, pageForm.getGoodsType());
        }
        List<EmailPromotionTemplate> list = iEmailPromotionTemplateService.list(wrapper);
        return list.stream().map(EmailPromotionTemplate::getId).collect(Collectors.toList());
    }

    private List<EmailPromotionSendRecordVo> getVo(List<EmailPromotionSendRecord> records) {
        List<EmailPromotionSendRecordVo> result = new ArrayList<>();
        List<Integer> collect = records.stream().map(EmailPromotionSendRecord::getEmailPromotionTemplateId).collect(Collectors.toList());
        List<EmailPromotionTemplate> emailPromotionTemplates = iEmailPromotionTemplateService.listByIds(collect);
        List<EmailPromotionTemplateVo> emailPromotionTemplateVos = changeVo(emailPromotionTemplates);
        records.forEach(record -> {
            EmailPromotionSendRecordVo copy = BeanUtils.copy(record, EmailPromotionSendRecordVo.class);
            emailPromotionTemplateVos.forEach(template -> {
                if (template.getId().equals(record.getEmailPromotionTemplateId())){
                    copy.setGoodsType(template.getGoodsType());
                    copy.setGoodsId(template.getGoodsId());
                    copy.setGoodsName(template.getGoodsName());
                    copy.setGoodsTagId(template.getGoodsTagId());
                    copy.setGoodsTag(template.getGoodsTag());
                }

            });
            copy.setFailNum(copy.getTotalNum()-copy.getSuccessNum());
            result.add(copy);

        });
        return result;
    }

    private List<EmailPromotionTemplateVo> changeVo(List<EmailPromotionTemplate> list) {
        List<EmailPromotionTemplateVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<Integer> goodsIds = list.stream().map(EmailPromotionTemplate::getGoodsId).collect(Collectors.toList());
        List<Integer> goodsTagIds = list.stream().map(EmailPromotionTemplate::getGoodsTagId).collect(Collectors.toList());
        Map<Integer, GoodsMultilingual> integerGoodsMultilingualMap = goodsMultilingualService.mapNameByIds(goodsIds);
        Map<Integer, GoodsTraditional> integerGoodsTraditionalMap = goodsTraditionalService.mapNameByIds(goodsIds);
        Map<Integer, GoodsCategoryMultilingual> integerGoodsTypeMultilingualMap = goodsCategoryMultilingualService.mapNameByIds(goodsTagIds);
        Map<Integer, GoodsCategoryTraditional> integerGoodsTypeTraditionalMap = goodsCategoryTraditionalService.mapNameByIds(goodsTagIds);
        for (EmailPromotionTemplate emailPromotionTemplate : list) {
            EmailPromotionTemplateVo vo = new EmailPromotionTemplateVo();
            org.springframework.beans.BeanUtils.copyProperties(emailPromotionTemplate, vo);
            if (Objects.equals(emailPromotionTemplate.getGoodsType(), GoodsTypeEnum.MULTILINGUAL.getValue())){
                vo.setGoodsName(integerGoodsMultilingualMap.get(emailPromotionTemplate.getGoodsId()).getName());
                vo.setGoodsTag(integerGoodsTypeMultilingualMap.get(emailPromotionTemplate.getGoodsTagId()).getName());
            }else {
                vo.setGoodsName(integerGoodsTraditionalMap.get(emailPromotionTemplate.getGoodsId()).getName());
                vo.setGoodsTag(integerGoodsTypeTraditionalMap.get(emailPromotionTemplate.getGoodsTagId()).getName());
            }
            result.add(vo);
        }
        return result;
    }


}
