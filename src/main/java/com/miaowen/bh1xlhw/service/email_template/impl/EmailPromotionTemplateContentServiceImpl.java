package com.miaowen.bh1xlhw.service.email_template.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.EmailPromotionTemplateContent;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplateContentForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplateContentPageForm;
import com.miaowen.bh1xlhw.repository.IEmailPromotionTemplateContentService;
import com.miaowen.bh1xlhw.service.email_template.EmailPromotionTemplateContentService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;
import static com.miaowen.bh1xlhw.constant.CommonConstant.ID;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
public class EmailPromotionTemplateContentServiceImpl implements EmailPromotionTemplateContentService {

    @Resource
    private IEmailPromotionTemplateContentService emailPromotionTemplateContentService;


    @Override
    public void add(EmailPromotionTemplateContentForm emailPromotionTemplateContent) {
        emailPromotionTemplateContentService.save(BeanUtils.copy(emailPromotionTemplateContent, EmailPromotionTemplateContent.class));
    }

    @Override
    public void update(EmailPromotionTemplateContentForm emailPromotionTemplateContent) {
        emailPromotionTemplateContentService.updateById(BeanUtils.copy(emailPromotionTemplateContent, EmailPromotionTemplateContent.class));
    }

    @Override
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<EmailPromotionTemplateContent> wrapper =
                new UpdateWrapper<EmailPromotionTemplateContent>().in(ID, deleteBatchForm.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        emailPromotionTemplateContentService.update(wrapper);
    }

    @Override
    public ResultVO<?> page(EmailPromotionTemplateContentPageForm form) {
        IPage<EmailPromotionTemplateContent> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<EmailPromotionTemplateContent> qw = new QueryWrapper<>();
        qw.eq(DELETE_TIME, 0);
        if (form.getEmailPromotionTemplateId() != null) {
            qw.eq("emailPromotion_template_id", form.getEmailPromotionTemplateId());
        }
        page = emailPromotionTemplateContentService.page(page, qw);

        List<EmailPromotionTemplateContent> list = page.getRecords();
        //数据处理

        return ResultVO.successForPage(list, page.getTotal(), form.getPageInt(), form.getPageSize());
    }

}




