package com.miaowen.bh1xlhw.service.email_template.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.email_template.EmailSendRecordForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailSendRecordVo;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailTemplateVo;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email_template.EmailSendRecordService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import com.miaowen.bh1xlhw.utils.ShardingUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.YearMonth;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 14:44
 */
@Service
@AllArgsConstructor
public class EmailSendRecordServiceImpl implements EmailSendRecordService {

    private final IEmailTemplateService iEmailTemplateService;
    private final IEmailSendRecordService iEmailSendRecordService;
    private final IOrderService orderService;
    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;

    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;

    @Override
    public PageVO<EmailSendRecordVo> page(EmailSendRecordForm pageForm) {
        List<Integer> templateIds = getTemplateIds(pageForm);
        if (CollectionUtils.isEmpty(templateIds)) {
            return new PageVO<>();
        }
        String month = YearMonth.now().format(DateUtils.MONTH_DATE_FORMAT);
        if (StringUtils.hasText(pageForm.getMonth())){
            month = pageForm.getMonth();
        }
        Page<EmailSendRecord> emailSendRecordPage = iEmailSendRecordService.pageInfo(month, pageForm, templateIds);
        List<EmailSendRecord> records = emailSendRecordPage.getRecords();
        List<EmailSendRecordVo> vo = getVo(records);
        return new PageVO<EmailSendRecordVo>(emailSendRecordPage).convert(vo);
    }

    @Override
    public List<EmailSendRecordVo> listByOrderId(Integer orderId) {
        Order order = orderService.getById(orderId);
        if (order == null || order.getEmail() == null) {
            return Collections.emptyList();
        }

        try {
            LambdaQueryWrapper<EmailSendRecord> wrapper = Wrappers.lambdaQuery();
            wrapper.eq(EmailSendRecord::getOrderId, order.getId());
            wrapper.eq(EmailSendRecord::getDeleteTime, 0);
            wrapper.orderByDesc(EmailSendRecord::getCreateTime);
            MonthShardingTableNameHandler.setParams(DateUtils.toDate(order.getCreateTime()));
            List<EmailSendRecord> records = iEmailSendRecordService.list(wrapper);
            if (CollectionUtils.isEmpty(records)) {
                return Collections.emptyList();
            }
            return getVo(records);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }

    }

    private List<Integer> getTemplateIds(EmailSendRecordForm pageForm) {
        LambdaQueryWrapper<EmailTemplate> wrapper = Wrappers.lambdaQuery();
        if (!StringUtils.isEmpty(pageForm.getTemplateType())) {
            wrapper.like(EmailTemplate::getTemplateType, pageForm.getTemplateType());
        }
        if (!StringUtils.isEmpty(pageForm.getGoodsTypeCode())) {
            wrapper.like(EmailTemplate::getEmailType, pageForm.getGoodsTypeCode());
        }
        List<EmailTemplate> list = iEmailTemplateService.list(wrapper);
        return list.stream().map(EmailTemplate::getId).collect(Collectors.toList());

    }



    /**
     * 通用方法：将 EmailSendRecord 转换为 EmailSendRecordVo
     *
     * @param records            原始记录列表
     * @return 转换后的 Vo 列表
     */
    private List<EmailSendRecordVo> getVo(List<EmailSendRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        // 1. 提取所有 EmailTemplateId
        List<Integer> templateIds = records.stream()
                .map(EmailSendRecord::getEmailTemplateId)
                .collect(Collectors.toList());

        // 2. 批量查询 EmailTemplate 并转换为 Vo
        List<EmailTemplate> emailTemplates = iEmailTemplateService.listByIds(templateIds);
        Map<Integer, EmailTemplateVo> templateVoMap = changeVo(emailTemplates).stream()
                .collect(Collectors.toMap(EmailTemplateVo::getId, Function.identity()));

        // 3. 转换 EmailSendRecord -> EmailSendRecordVo
        return records.stream()
                .map(record -> {
                    EmailSendRecordVo vo = BeanUtils.copy(record, EmailSendRecordVo.class);
                    EmailTemplateVo template = templateVoMap.get(record.getEmailTemplateId());

                    if (template != null) {
                        vo.setTemplateType(template.getTemplateType());
                        vo.setEmailType(template.getEmailType());
                        vo.setName(template.getName());
                        vo.setGoodsId(template.getGoodsId());
                        vo.setGoodsName(template.getGoodsName());
                        vo.setGoodsTagId(template.getGoodsTagId());
                        vo.setGoodsTag(template.getGoodsTag());
                        vo.setGoodsType(template.getGoodsType());
                    }
                    return vo;
                })
                .collect(Collectors.toList());
    }

    private List<EmailTemplateVo> changeVo(List<EmailTemplate> list) {
        List<EmailTemplateVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<Integer> goodsIds = list.stream().map(EmailTemplate::getGoodsId).collect(Collectors.toList());
        List<Integer> goodsTagIds = list.stream().map(EmailTemplate::getGoodsTagId).collect(Collectors.toList());
        Map<Integer, GoodsMultilingual> integerGoodsMultilingualMap = goodsMultilingualService.mapNameByIds(goodsIds);
        Map<Integer, GoodsTraditional> integerGoodsTraditionalMap = goodsTraditionalService.mapNameByIds(goodsIds);
        Map<Integer, GoodsCategoryMultilingual> integerGoodsTypeMultilingualMap = goodsCategoryMultilingualService.mapNameByIds(goodsTagIds);
        Map<Integer, GoodsCategoryTraditional> integerGoodsTypeTraditionalMap = goodsCategoryTraditionalService.mapNameByIds(goodsTagIds);
        for (EmailTemplate emailTemplate : list) {
            EmailTemplateVo vo = new EmailTemplateVo();
            org.springframework.beans.BeanUtils.copyProperties(emailTemplate, vo);
            if (Objects.equals(emailTemplate.getGoodsType(), GoodsTypeEnum.MULTILINGUAL.getValue())) {
                vo.setGoodsName(integerGoodsMultilingualMap.get(emailTemplate.getGoodsId()).getName());
                vo.setGoodsTag(integerGoodsTypeMultilingualMap.get(emailTemplate.getGoodsTagId()).getName());
            } else {
                vo.setGoodsName(integerGoodsTraditionalMap.get(emailTemplate.getGoodsId()).getName());
                vo.setGoodsTag(integerGoodsTypeTraditionalMap.get(emailTemplate.getGoodsTagId()).getName());
            }
            result.add(vo);
        }
        return result;
    }

}
