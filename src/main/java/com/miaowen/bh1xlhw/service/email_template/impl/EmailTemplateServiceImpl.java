package com.miaowen.bh1xlhw.service.email_template.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email_template.DetailsForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateAddForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplatePageForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailTemplateDetailsVO;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailTemplateVo;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email_template.EmailTemplateService;
import com.miaowen.bh1xlhw.service.good.GoodsService;
import com.miaowen.bh1xlhw.utils.DateUtils;

import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;
import static com.miaowen.bh1xlhw.constant.CommonConstant.ID;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
@AllArgsConstructor
public class EmailTemplateServiceImpl implements EmailTemplateService {
    private final IEmailTemplateContentService iEmailTemplateContentService;

    private final IEmailTemplateService iEmailTemplateService;

    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;

    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(EmailTemplateAddForm form) {
        EmailTemplate emailTemplate = form.getEmailTemplate();
        iEmailTemplateService.save(emailTemplate);
        Integer emailTemplateId = emailTemplate.getId();
        List<EmailTemplateContent> emailTemplateContents = form.getEmailTemplateContents();
        for (EmailTemplateContent emailTemplateContent : emailTemplateContents) {
            emailTemplateContent.setEmailTemplateId(emailTemplateId);
        }
        iEmailTemplateContentService.saveBatch(emailTemplateContents);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(EmailTemplateAddForm form) {
        EmailTemplate emailTemplate = form.getEmailTemplate();
        iEmailTemplateService.updateById(emailTemplate);

        Integer emailTemplateId = emailTemplate.getId();
        List<EmailTemplateContent> emailTemplateContents = form.getEmailTemplateContents();
        for (EmailTemplateContent emailTemplateContent : emailTemplateContents) {
            emailTemplateContent.setId(null);
            emailTemplateContent.setEmailTemplateId(emailTemplateId);
        }
        //先删内容表
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<EmailTemplateContent> wrapper = new UpdateWrapper<EmailTemplateContent>().in(CommonConstant.EMAIL_TEMPLATE_ID,
                emailTemplate.getId()).set(DELETE_TIME, System.currentTimeMillis() / 1000);
        iEmailTemplateContentService.update(wrapper);
        //再重新写入内容表
        iEmailTemplateContentService.saveBatch(emailTemplateContents);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<EmailTemplate> wrapper =
                new UpdateWrapper<EmailTemplate>().in(ID, deleteBatchForm.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        iEmailTemplateService.update(wrapper);
    }

    @Override
    public PageVO<EmailTemplateVo> page(EmailTemplatePageForm form) {

        LambdaQueryWrapper<EmailTemplate> qw = new LambdaQueryWrapper<>();

        // 基础查询条件
        qw.like(!StringUtils.isEmpty(form.getName()), EmailTemplate::getName, form.getName());
        qw.eq(!StringUtils.isEmpty(form.getTemplateType()), EmailTemplate::getTemplateType, form.getTemplateType());
        qw.eq(EmailTemplate::getDeleteTime, 0);
        qw.orderByDesc(EmailTemplate::getCreateTime);

        // 时间范围查询
        if (Objects.nonNull(form.getStartTime())) {
            qw.ge(EmailTemplate::getCreateTime, DateUtils.getData(form.getStartTime()));
        }
        if (Objects.nonNull(form.getEndTime())) {
            qw.le(EmailTemplate::getCreateTime, DateUtils.getData(form.getEndTime()));
        }

        // 根据商品相关条件查询商品ID列表，然后使用IN查询
        List<Integer> goodsIds = new ArrayList<>();

        // 如果有商品分类条件，需要根据商品类型来决定查询哪个表
        if (Objects.nonNull(form.getGoodsType())) {
            qw.eq(EmailTemplate::getGoodsType, form.getGoodsType());
            // 根据商品类型（1:多语言,2:繁体）来决定查询哪个商品表
            if (GoodsTypeEnum.MULTILINGUAL.getValue().equals(form.getGoodsType())) {
                // 查询多语言商品
                LambdaQueryWrapper<GoodsMultilingual> multilingualQw = new LambdaQueryWrapper<>();
                multilingualQw.eq(StringUtils.hasText(form.getGoodsTag()), GoodsMultilingual::getGoodsCategoryId,
                    form.getGoodsTag());
                multilingualQw.eq(GoodsMultilingual::getDeleteTime, 0);
                multilingualQw.select(GoodsMultilingual::getId);
                List<GoodsMultilingual> multilingualGoods = goodsMultilingualService.list(multilingualQw);
                if (CollectionUtils.isEmpty(multilingualGoods)){
                    return new PageVO<>();
                }
                goodsIds.addAll(multilingualGoods.stream().map(GoodsMultilingual::getId).collect(Collectors.toList()));

            } else if (GoodsTypeEnum.TRADITIONAL.getValue().equals(form.getGoodsType())) {
                // 查询繁体商品
                LambdaQueryWrapper<GoodsTraditional> traditionalQw = new LambdaQueryWrapper<>();
                traditionalQw.eq(StringUtils.hasText(form.getGoodsTag()), GoodsTraditional::getGoodsCategoryId,
                    form.getGoodsTag());
                traditionalQw.eq(GoodsTraditional::getDeleteTime, 0);
                traditionalQw.select(GoodsTraditional::getId);
                List<GoodsTraditional> traditionalGoods = goodsTraditionalService.list(traditionalQw);
                if (CollectionUtils.isEmpty(traditionalGoods)){
                    return new PageVO<>();
                }
                goodsIds.addAll(traditionalGoods.stream().map(GoodsTraditional::getId).collect(Collectors.toList()));

            } else {
                // 如果商品类型不是1或2，返回空结果
                return new PageVO<>();
            }
        }

        // 如果有商品标签条件，直接在EmailTemplate上查询
        if (!StringUtils.isEmpty(form.getGoodsTag())) {
            qw.eq(EmailTemplate::getGoodsTagId, form.getGoodsTag());
        }

        // 如果有商品ID条件，使用IN查询
        if (!CollectionUtils.isEmpty(goodsIds)) {
            qw.in(EmailTemplate::getGoodsId, goodsIds);
        }

        Page<EmailTemplate> page = iEmailTemplateService.page(form.qry(), qw);
        List<EmailTemplate> list = page.getRecords();
        return new PageVO<EmailTemplateVo>(page).convert(changeVo(list));
    }


    private List<EmailTemplateVo> changeVo(List<EmailTemplate> list) {
        List<EmailTemplateVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        List<Integer> goodsIds = list.stream().map(EmailTemplate::getGoodsId).collect(Collectors.toList());
        List<Integer> goodsTagIds = list.stream().map(EmailTemplate::getGoodsTagId).collect(Collectors.toList());
        Map<Integer, GoodsMultilingual> integerGoodsMultilingualMap = goodsMultilingualService.mapNameByIds(goodsIds);
        Map<Integer, GoodsTraditional> integerGoodsTraditionalMap = goodsTraditionalService.mapNameByIds(goodsIds);
        Map<Integer, GoodsCategoryMultilingual> integerGoodsTypeMultilingualMap = goodsCategoryMultilingualService.mapNameByIds(goodsTagIds);
        Map<Integer, GoodsCategoryTraditional> integerGoodsTypeTraditionalMap = goodsCategoryTraditionalService.mapNameByIds(goodsTagIds);
        for (EmailTemplate emailTemplate : list) {
            EmailTemplateVo vo = new EmailTemplateVo();
            BeanUtils.copyProperties(emailTemplate, vo);
            if (Objects.equals(emailTemplate.getGoodsType(), GoodsTypeEnum.MULTILINGUAL.getValue())) {
                vo.setGoodsName(Optional.ofNullable(integerGoodsMultilingualMap.get(emailTemplate.getGoodsId())).orElse(new GoodsMultilingual()).getName());
                vo.setGoodsTag(Optional.ofNullable(integerGoodsTypeMultilingualMap.get(emailTemplate.getGoodsTagId())).orElse(new GoodsCategoryMultilingual()).getName());
            } else {
                vo.setGoodsName(Optional.ofNullable(integerGoodsTraditionalMap.get(emailTemplate.getGoodsId())).orElse(new GoodsTraditional()).getName());
                vo.setGoodsTag(Optional.ofNullable(integerGoodsTypeTraditionalMap.get(emailTemplate.getGoodsTagId())).orElse(new GoodsCategoryTraditional()).getName());
            }
            result.add(vo);
        }
        return result;
    }


    @Override
    public void updateState(UpdateStateForm form) {
        EmailTemplate updateParam = EmailTemplate.builder().status(form.getStatus()).build();
        updateParam.setId(form.getId());
        iEmailTemplateService.updateById(updateParam);
    }

    @Override
    public EmailTemplateDetailsVO getDetails(DetailsForm form) {
        EmailTemplateDetailsVO vo = new EmailTemplateDetailsVO();
        EmailTemplate emailTemplate = iEmailTemplateService.getById(form.getId());
        List<EmailTemplate> list = new ArrayList<>();
        list.add(emailTemplate);
        List<EmailTemplateVo> emailTemplateVos = changeVo(list);
        vo.setEmailTemplate(emailTemplateVos.get(0));
        QueryWrapper<EmailTemplateContent> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(CommonConstant.EMAIL_TEMPLATE_ID, form.getId());
        queryWrapper.eq(CommonConstant.DELETE_TIME, 0);
        List<EmailTemplateContent> emailTemplateContents =
                iEmailTemplateContentService.list(queryWrapper);
        vo.setEmailTemplateContents(emailTemplateContents);

        return vo;
    }

}




