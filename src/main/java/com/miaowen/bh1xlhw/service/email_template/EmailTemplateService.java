package com.miaowen.bh1xlhw.service.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email_template.DetailsForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateAddForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplatePageForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailTemplateDetailsVO;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailTemplateVo;

/**
 * @ClassName EmailTemplateContentService
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/8 15:12
 */
public interface EmailTemplateService {

    void add(EmailTemplateAddForm form);
    void update(EmailTemplateAddForm form);
    void deleteBatch(DeleteBatchForm form);
    PageVO<EmailTemplateVo> page(EmailTemplatePageForm form);
    void updateState(UpdateStateForm form);
    EmailTemplateDetailsVO getDetails(DetailsForm form);
}
