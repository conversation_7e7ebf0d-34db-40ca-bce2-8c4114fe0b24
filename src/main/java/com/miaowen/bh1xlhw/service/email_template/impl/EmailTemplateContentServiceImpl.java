package com.miaowen.bh1xlhw.service.email_template.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateContentForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateContentPageForm;
import com.miaowen.bh1xlhw.repository.IEmailTemplateContentService;
import com.miaowen.bh1xlhw.service.email_template.EmailTemplateContentService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;
import static com.miaowen.bh1xlhw.constant.CommonConstant.ID;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
public class EmailTemplateContentServiceImpl implements EmailTemplateContentService {

    @Resource
    private IEmailTemplateContentService emailTemplateContentService;


    @Override
    public void add(EmailTemplateContentForm emailTemplateContent) {
        emailTemplateContentService.save(BeanUtils.copy(emailTemplateContent, EmailTemplateContent.class));
    }

    @Override
    public void update(EmailTemplateContentForm emailTemplateContent) {
        emailTemplateContentService.updateById(BeanUtils.copy(emailTemplateContent, EmailTemplateContent.class));
    }

    @Override
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<EmailTemplateContent> wrapper = new UpdateWrapper<EmailTemplateContent>().in(ID, deleteBatchForm.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        emailTemplateContentService.update(wrapper);
    }

    @Override
    public ResultVO<?> page(EmailTemplateContentPageForm form) {
        IPage<EmailTemplateContent> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<EmailTemplateContent> qw = new QueryWrapper<>();
        qw.eq(DELETE_TIME, 0);
        if (form.getEmailTemplateId() != null) {
            qw.eq("email_template_id", form.getEmailTemplateId());
        }
        qw.orderByDesc("create_time");
        page = emailTemplateContentService.page(page, qw);

        List<EmailTemplateContent> list = page.getRecords();
        //数据处理

        return ResultVO.successForPage(list, page.getTotal(), form.getPageInt(), form.getPageSize());
    }

}




