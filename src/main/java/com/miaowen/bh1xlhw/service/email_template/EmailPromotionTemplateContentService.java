package com.miaowen.bh1xlhw.service.email_template;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplateContentForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailPromotionTemplateContentPageForm;

/**
 * @ClassName EmailPromotionTemplateContentService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/8 15:12
 */
public interface EmailPromotionTemplateContentService {

    void add(EmailPromotionTemplateContentForm emailPromotionTemplateContent);
    void update(EmailPromotionTemplateContentForm emailPromotionTemplateContent);
    void deleteBatch(DeleteBatchForm form);
    ResultVO<?> page(EmailPromotionTemplateContentPageForm form);
}
