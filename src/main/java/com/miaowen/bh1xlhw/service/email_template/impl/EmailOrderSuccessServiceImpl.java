package com.miaowen.bh1xlhw.service.email_template.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.email_template.EmailOrderSuccessForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailOrderSuccessVo;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email_template.EmailOrderSuccessService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 14:44
 */
@Slf4j
@Service
@AllArgsConstructor
public class EmailOrderSuccessServiceImpl implements EmailOrderSuccessService {
    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;

    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;

    private final IEmailOrderSuccessService iEmailOrderSuccessService;
    private final IEmailUnsubscribeService iEmailUnsubscribeService;


    @Override
    public PageVO<EmailOrderSuccessVo> page(EmailOrderSuccessForm pageForm) {
        try {
            LambdaQueryWrapper<EmailOrderSuccess> wrapper = Wrappers.lambdaQuery();
            if (Objects.nonNull(pageForm.getGoodsId()) || Objects.nonNull(pageForm.getGoodsCategoryId())) {
                List<Integer> goodsIds = getGoodsIdByGoodsName(pageForm);
                if (CollectionUtils.isEmpty(goodsIds)) {
                    return new PageVO<>();
                }
                wrapper.in(EmailOrderSuccess::getGoodsId,
                        goodsIds);
            }
            if (pageForm.getGoodsType() != null) {
                wrapper.eq(EmailOrderSuccess::getGoodsType, pageForm.getGoodsType());
            }
            if (StringUtils.isNotEmpty(pageForm.getEmail())) {
                wrapper.eq(EmailOrderSuccess::getEmail, pageForm.getEmail());
            }
            if (StringUtils.isNotEmpty(pageForm.getOrderNo())) {
                wrapper.eq(EmailOrderSuccess::getOutTradeNo, pageForm.getOrderNo());
            }

            if (StringUtils.isNotEmpty(pageForm.getLanguageCode())) {
                wrapper.eq(EmailOrderSuccess::getLanguageCode, pageForm.getLanguageCode());
            }
            if (StringUtils.isNotEmpty(pageForm.getStatus())) {
                if (pageForm.getStatus().equals("0")) {
                    wrapper.notIn(EmailOrderSuccess::getEmail, getEmails());
                } else if (pageForm.getStatus().equals("1")) {
                    wrapper.in(EmailOrderSuccess::getEmail, getEmails());
                }
            }
            if (pageForm.getStartTime() != null && pageForm.getEndTime() != null) {
                MonthShardingTableNameHandler.setParams(DateUtils.getData(pageForm.getStartTime()));
                if (!DateUtils.isSameMonth(DateUtils.getData(pageForm.getStartTime()), DateUtils.getData(pageForm.getEndTime()))) {
                    //跨月 则取开始时间当月
                    wrapper.between(EmailOrderSuccess::getPaymentTime, pageForm.getStartTime(),
                        DateUtils.getLastDayOfMonth(DateUtils.getData(pageForm.getStartTime())));
                } else {
                    wrapper.between(EmailOrderSuccess::getPaymentTime, pageForm.getStartTime(), pageForm.getEndTime());
                }
            } else {
                //没有的话则需要放入时间范围 默认查当月
                wrapper.between(EmailOrderSuccess::getPaymentTime, DateUtils.getFirstDayOfMonth(), new Date());
            }
            wrapper.eq(EmailOrderSuccess::getDeleteTime, 0);
            long count = iEmailOrderSuccessService.count(wrapper);
            if (count == 0) {
                return new PageVO<>();
            }
            wrapper.orderByDesc(EmailOrderSuccess::getCreateTime);
            // 构建分页查询的SQL语句
            int offset = (pageForm.getPageInt() - 1) * pageForm.getPageSize();
            int limit = pageForm.getPageSize();
            // 添加LIMIT条件
            wrapper.last("LIMIT " + offset + "," + limit);
            List<EmailOrderSuccess> emailOrderSuccessList = iEmailOrderSuccessService.list(wrapper);
            List<EmailOrderSuccessVo> records = getVo(emailOrderSuccessList, pageForm);
            Page<EmailOrderSuccessVo> page = new Page<>(pageForm.getPageInt(), pageForm.getPageSize());
            page.setRecords(records);
            page.setTotal(count);
            return new PageVO<>(page, EmailOrderSuccessVo.class);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    /**
     * 获取符合条件的商品id
     */
    private List<Integer> getGoodsIdByGoodsName(EmailOrderSuccessForm pageForm) {
        List<Integer> goodsIdList = new ArrayList<>();
        if (Objects.equals(pageForm.getGoodsType(), GoodsTypeEnum.MULTILINGUAL.getValue())) {
            QueryWrapper<GoodsMultilingual> multilingualQueryWrapper = new QueryWrapper<>();
            if (Objects.nonNull(pageForm.getGoodsId())) {
                multilingualQueryWrapper.eq("id", pageForm.getGoodsId());
            }
            if (Objects.nonNull(pageForm.getGoodsCategoryId())) {
                multilingualQueryWrapper.eq("goods_category_id", pageForm.getGoodsCategoryId());
            }
            List<GoodsMultilingual> multilingualList = goodsMultilingualService.list(multilingualQueryWrapper);
            if (!CollectionUtils.isEmpty(multilingualList)){
                multilingualList.forEach(goodsMultilingual -> goodsIdList.add(goodsMultilingual.getId()));
            }
        }
        if (Objects.equals(pageForm.getGoodsType(), GoodsTypeEnum.TRADITIONAL.getValue())) {
            QueryWrapper<GoodsTraditional> traditionalQueryWrapper = new QueryWrapper<>();
            if (Objects.nonNull(pageForm.getGoodsId())) {
                traditionalQueryWrapper.eq("id", pageForm.getGoodsId());
            }
            if (Objects.nonNull(pageForm.getGoodsCategoryId())) {
                traditionalQueryWrapper.eq("goods_category_id", pageForm.getGoodsCategoryId());
            }
            List<GoodsTraditional> traditions = goodsTraditionalService.list(traditionalQueryWrapper);
            if (!CollectionUtils.isEmpty(traditions)){
                traditions.forEach(goodsTraditional -> goodsIdList.add(goodsTraditional.getId()));
            }
        }
        return goodsIdList;
    }

    private List<EmailOrderSuccessVo> getVo(List<EmailOrderSuccess> emailOrderSuccessList, EmailOrderSuccessForm pageForm) {
        List<EmailOrderSuccessVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(emailOrderSuccessList)) {
            return result;
        }
        //获取商品信息 及商品类型信息
        List<Integer> idListMul = emailOrderSuccessList.stream()
                .filter(order -> order.getGoodsType() == 1)
                .map(EmailOrderSuccess::getGoodsId)
                .collect(Collectors.toList());

        List<GoodsMultilingual> multilingualList;
        List<GoodsCategoryMultilingual> goodsCategoryMultilinguals;
        if (!CollectionUtils.isEmpty(idListMul)) {
            multilingualList = goodsMultilingualService.listByIds(idListMul);
            goodsCategoryMultilinguals =
                    goodsCategoryMultilingualService.listByIds(multilingualList.stream()
                            .map(GoodsMultilingual::getGoodsCategoryId)
                            .collect(Collectors.toList()));
        } else {
            goodsCategoryMultilinguals = new ArrayList<>();
            multilingualList = new ArrayList<>();
        }
        List<Integer> idListTra = emailOrderSuccessList.stream()
                .filter(order -> order.getGoodsType() == 2)
                .map(EmailOrderSuccess::getGoodsId)
                .collect(Collectors.toList());
        List<GoodsTraditional> traditions;
        List<GoodsCategoryTraditional> goodsCategoryTraditionals;
        if (!CollectionUtils.isEmpty(idListTra)) {
            traditions = goodsTraditionalService.listByIds(idListTra);
            goodsCategoryTraditionals =
                    goodsCategoryTraditionalService.listByIds(traditions.stream()
                            .map(GoodsTraditional::getGoodsCategoryId)
                            .collect(Collectors.toList()));
        } else {
            goodsCategoryTraditionals = new ArrayList<>();
            traditions = new ArrayList<>();
        }
        Set<String> unsubscribe = getEmails();
        emailOrderSuccessList.forEach(emailOrderSuccess -> {
            EmailOrderSuccessVo vo = BeanUtils.copy(emailOrderSuccess, EmailOrderSuccessVo.class);
            if (emailOrderSuccess.getGoodsType() == 1) {
                for (GoodsMultilingual goodsMultilingual : multilingualList) {
                    if (goodsMultilingual.getId().equals(emailOrderSuccess.getGoodsId())) {
                        vo.setGoodsName(goodsMultilingual.getName());
                        vo.setGoodsCategoryId(goodsMultilingual.getGoodsCategoryId());
                        for (GoodsCategoryMultilingual goodsCategoryMultilingual : goodsCategoryMultilinguals) {
                            if (goodsCategoryMultilingual.getId().equals(goodsMultilingual.getGoodsCategoryId())) {
                                vo.setGoodsTag(goodsCategoryMultilingual.getType());
                            }
                        }
                    }

                }
            } else {
                for (GoodsTraditional goodsTraditional : traditions) {
                    if (goodsTraditional.getId().equals(emailOrderSuccess.getGoodsId())) {
                        vo.setGoodsName(goodsTraditional.getName());
                        vo.setGoodsCategoryId(goodsTraditional.getGoodsCategoryId());
                        for (GoodsCategoryTraditional goodsCategoryTraditional : goodsCategoryTraditionals) {
                            if (goodsCategoryTraditional.getId().equals(goodsTraditional.getGoodsCategoryId())) {
                                vo.setGoodsTag(goodsCategoryTraditional.getType());
                            }
                        }
                    }

                }
            }
            if (StringUtils.isNotEmpty(pageForm.getStatus())) {
                vo.setStatus(pageForm.getStatus());
            } else {
                if (unsubscribe.contains(emailOrderSuccess.getEmail())) {
                    //退订
                    vo.setStatus("1");
                } else {
                    vo.setStatus("0");
                }
            }

            result.add(vo);

        });

        return result;

    }


    /**
     * 查询已退订邮箱数据
     * @return
     */
    private Set<String> getEmails() {
        return iEmailUnsubscribeService.list(new LambdaQueryWrapper<>(EmailUnsubscribe.class)
                        .eq(EmailUnsubscribe::getDeleteTime, 0)
                        .eq(EmailUnsubscribe::getStatus, 1)).stream()
                .map(EmailUnsubscribe::getEmail)
                .collect(Collectors.toSet());
    }


}
