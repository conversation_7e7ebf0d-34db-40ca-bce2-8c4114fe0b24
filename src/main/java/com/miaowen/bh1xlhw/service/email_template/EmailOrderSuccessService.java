package com.miaowen.bh1xlhw.service.email_template;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.email_template.EmailOrderSuccessForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailOrderSuccessVo;

/**
 * @Description
 * @Author：huang<PERSON>
 * @Date：2025/5/21 14:43
 */
public interface EmailOrderSuccessService {

    PageVO<EmailOrderSuccessVo> page(EmailOrderSuccessForm pageForm);

}
