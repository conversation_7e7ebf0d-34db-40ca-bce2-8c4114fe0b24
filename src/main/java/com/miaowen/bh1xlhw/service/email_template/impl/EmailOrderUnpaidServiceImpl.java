package com.miaowen.bh1xlhw.service.email_template.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.email_template.EmailOrderUnpaidForm;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailOrderUnpaidVo;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.email_template.EmailOrderUnpaidService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 14:44
 */
@Service
@AllArgsConstructor
public class EmailOrderUnpaidServiceImpl implements EmailOrderUnpaidService {
    private final IGoodsMultilingualService goodsMultilingualService;
    private final GoodsCategoryMultilingualService goodsCategoryMultilingualService;

    private final IGoodsTraditionalService goodsTraditionalService;
    private final GoodsCategoryTraditionalService goodsCategoryTraditionalService;

    private final IEmailOrderUnpaidService iEmailOrderUnpaidService;
    private final IEmailUnsubscribeService iEmailUnsubscribeService;

    @Override
    public PageVO<EmailOrderUnpaidVo> page(EmailOrderUnpaidForm pageForm) {
        try {
            LambdaQueryWrapper<EmailOrderUnpaid> wrapper = Wrappers.lambdaQuery();
            if (Objects.nonNull(pageForm.getGoodsId()) || Objects.nonNull(pageForm.getGoodsCategoryId())) {
                List<Integer> goodsIds = getGoodsIdByGoodsName(pageForm);
                if (CollectionUtils.isEmpty(goodsIds)) {
                    return new PageVO<>();
                }
                wrapper.in(EmailOrderUnpaid::getGoodsId,
                        goodsIds);
            }
            if (pageForm.getGoodsType() != null) {
                wrapper.eq(EmailOrderUnpaid::getGoodsType, pageForm.getGoodsType());
            }
            if (StringUtils.isNotEmpty(pageForm.getEmail())) {
                wrapper.like(EmailOrderUnpaid::getEmail, pageForm.getEmail());
            }
            if (StringUtils.isNotEmpty(pageForm.getLanguageCode())) {
                wrapper.eq(EmailOrderUnpaid::getLanguageCode, pageForm.getLanguageCode());
            }
            if (pageForm.getStartTime() != null && pageForm.getEndTime() != null) {
                Date startTime = DateUtils.getData(pageForm.getStartTime());
                Date endTime = DateUtils.getData(pageForm.getEndTime());
                if (org.springframework.util.StringUtils.isEmpty(pageForm.getTimezone())) {
                    pageForm.setTimezone("Asia/Shanghai");
                }

                Date east8StartDate = DateUtils.getEast8Date(pageForm.getTimezone(), startTime);
                Date east8EndDate = DateUtils.getEast8Date(pageForm.getTimezone(), endTime);

                MonthShardingTableNameHandler.setParams(east8StartDate);
                if (!DateUtils.isSameMonth(east8StartDate, east8EndDate)) {
                    //跨月 则取开始时间当月
                    wrapper.between(EmailOrderUnpaid::getOrderCreateTime, east8StartDate, DateUtils.getLastDayOfMonth(east8StartDate));
                } else {
                    wrapper.between(EmailOrderUnpaid::getOrderCreateTime, east8StartDate, east8EndDate);
                }
            } else {
                //没有的话则需要放入时间范围 默认查当月
                wrapper.between(EmailOrderUnpaid::getOrderCreateTime, DateUtils.getFirstDayOfMonth(), new Date());
            }
            wrapper.eq(EmailOrderUnpaid::getDeleteTime, 0);
            long count = iEmailOrderUnpaidService.count(wrapper);
            if (count == 0) {
                return new PageVO<>();
            }
            wrapper.orderByDesc(EmailOrderUnpaid::getCreateTime);
            // 构建分页查询的SQL语句
            int offset = (pageForm.getPageInt() - 1) * pageForm.getPageSize();
            int limit = pageForm.getPageSize();
            // 添加LIMIT条件
            wrapper.last("LIMIT " + offset + "," + limit);
            List<EmailOrderUnpaid> EmailOrderUnPaidList = iEmailOrderUnpaidService.list(wrapper);
            List<EmailOrderUnpaidVo> records = getVo(EmailOrderUnPaidList);
            Page<EmailOrderUnpaidVo> page = new Page<>(pageForm.getPageInt(), pageForm.getPageSize());
            page.setRecords(records);
            page.setTotal(count);
            return new PageVO<>(page, EmailOrderUnpaidVo.class);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }

    /**
     * 获取符合条件的商品id
     *
     * @return
     */
    private List<Integer> getGoodsIdByGoodsName(EmailOrderUnpaidForm pageForm) {
        List<Integer> goodsIdList = new ArrayList<>();
        if (Objects.equals(pageForm.getGoodsType(), GoodsTypeEnum.MULTILINGUAL.getValue())) {
            QueryWrapper<GoodsMultilingual> multilingualQueryWrapper = new QueryWrapper<>();
            if (Objects.nonNull(pageForm.getGoodsId())) {
                multilingualQueryWrapper.eq("id", pageForm.getGoodsId());
            }
            if (Objects.nonNull(pageForm.getGoodsCategoryId())) {
                multilingualQueryWrapper.eq("goods_category_id", pageForm.getGoodsCategoryId());
            }
            List<GoodsMultilingual> multilingualList = goodsMultilingualService.list(multilingualQueryWrapper);
            if (!CollectionUtils.isEmpty(multilingualList)){
                multilingualList.forEach(goodsMultilingual -> goodsIdList.add(goodsMultilingual.getId()));
            }
        }
        if (Objects.equals(pageForm.getGoodsType(), GoodsTypeEnum.TRADITIONAL.getValue())) {
            QueryWrapper<GoodsTraditional> traditionalQueryWrapper = new QueryWrapper<>();
            if (Objects.nonNull(pageForm.getGoodsId())) {
                traditionalQueryWrapper.eq("id", pageForm.getGoodsId());
            }
            if (Objects.nonNull(pageForm.getGoodsCategoryId())) {
                traditionalQueryWrapper.eq("goods_category_id", pageForm.getGoodsCategoryId());
            }
            List<GoodsTraditional> traditions = goodsTraditionalService.list(traditionalQueryWrapper);
            if (!CollectionUtils.isEmpty(traditions)){
                traditions.forEach(goodsTraditional -> goodsIdList.add(goodsTraditional.getId()));
            }
        }
        return goodsIdList;
    }

    private List<EmailOrderUnpaidVo> getVo(List<EmailOrderUnpaid> EmailOrderUnPaidList) {
        List<EmailOrderUnpaidVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(EmailOrderUnPaidList)) {
            return result;
        }
        //获取商品信息 及商品类型信息
        List<Integer> idListMul = EmailOrderUnPaidList.stream()
                .filter(order -> order.getGoodsType() == 1)
                .map(EmailOrderUnpaid::getGoodsId)
                .collect(Collectors.toList());
        List<GoodsMultilingual> multilingualList;
        List<GoodsCategoryMultilingual> goodsCategoryMultilinguals;
        if (!CollectionUtils.isEmpty(idListMul)) {
            multilingualList = goodsMultilingualService.listByIds(idListMul);
            goodsCategoryMultilinguals =
                    goodsCategoryMultilingualService.listByIds(multilingualList.stream()
                            .map(GoodsMultilingual::getGoodsCategoryId)
                            .collect(Collectors.toList()));
        } else {
            goodsCategoryMultilinguals = new ArrayList<>();
            multilingualList = new ArrayList<>();
        }
        List<Integer> idListTra = EmailOrderUnPaidList.stream()
                .filter(order -> order.getGoodsType() == 2)
                .map(EmailOrderUnpaid::getGoodsId)
                .collect(Collectors.toList());
        List<GoodsTraditional> traditions;
        List<GoodsCategoryTraditional> goodsCategoryTraditionals;
        if (!CollectionUtils.isEmpty(idListTra)) {
            traditions = goodsTraditionalService.listByIds(idListTra);
            goodsCategoryTraditionals =
                    goodsCategoryTraditionalService.listByIds(traditions.stream()
                            .map(GoodsTraditional::getGoodsCategoryId)
                            .collect(Collectors.toList()));
        } else {
            goodsCategoryTraditionals = new ArrayList<>();
            traditions = new ArrayList<>();
        }
        Set<String> emails = getEmails();
        EmailOrderUnPaidList.forEach(EmailOrderUnPaid -> {
            EmailOrderUnpaidVo vo = BeanUtils.copy(EmailOrderUnPaid, EmailOrderUnpaidVo.class);
            if (EmailOrderUnPaid.getGoodsType() == 1) {
                for (GoodsMultilingual goodsMultilingual : multilingualList) {
                    if (goodsMultilingual.getId().equals(EmailOrderUnPaid.getGoodsId())) {
                        vo.setGoodsName(goodsMultilingual.getName());
                        vo.setGoodsCategoryId(goodsMultilingual.getGoodsCategoryId());
                        for (GoodsCategoryMultilingual goodsCategoryMultilingual : goodsCategoryMultilinguals) {
                            if (goodsCategoryMultilingual.getId().equals(goodsMultilingual.getGoodsCategoryId())) {
                                vo.setGoodsTag(goodsCategoryMultilingual.getType());
                            }
                        }
                    }

                }
            } else {
                for (GoodsTraditional goodsTraditional : traditions) {
                    if (goodsTraditional.getId().equals(EmailOrderUnPaid.getGoodsId())) {
                        vo.setGoodsName(goodsTraditional.getName());
                        vo.setGoodsCategoryId(goodsTraditional.getGoodsCategoryId());
                        for (GoodsCategoryTraditional goodsCategoryTraditional : goodsCategoryTraditionals) {
                            if (goodsCategoryTraditional.getId().equals(goodsTraditional.getGoodsCategoryId())) {
                                vo.setGoodsTag(goodsCategoryTraditional.getType());
                            }
                        }
                    }

                }
            }
            if (emails.contains(EmailOrderUnPaid.getEmail())) {
                vo.setStatus("1");
            } else {
                vo.setStatus("0");
            }

            result.add(vo);

        });
        return result;

    }


    /**
     * 查询已退订邮箱数据
     * @return
     */
    private Set<String> getEmails() {
        return iEmailUnsubscribeService.list(new LambdaQueryWrapper<>(EmailUnsubscribe.class)
                        .eq(EmailUnsubscribe::getDeleteTime, 0)
                        .eq(EmailUnsubscribe::getStatus, 1)).stream()
                .map(EmailUnsubscribe::getEmail)
                .collect(Collectors.toSet());
    }
}
