package com.miaowen.bh1xlhw.service.email_template;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.email_template.*;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailPromotionTemplateDetailsVO;
import com.miaowen.bh1xlhw.model.vo.email_template.EmailTemplateDetailsVO;

/**
 * @ClassName EmailTemplateContentService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/8 15:12
 */
public interface EmailPromotionTemplateService {

    void add(EmailPromotionTemplateAddForm form);
    void update(EmailPromotionTemplateAddForm form);
    void deleteBatch(DeleteBatchForm form);
    ResultVO<?> page(EmailPromotionTemplatePageForm form);
    void updateState(UpdateStateForm form);
    EmailPromotionTemplateDetailsVO getDetails(DetailsForm form);

    void send(EmailPromotionSendForm form);
}
