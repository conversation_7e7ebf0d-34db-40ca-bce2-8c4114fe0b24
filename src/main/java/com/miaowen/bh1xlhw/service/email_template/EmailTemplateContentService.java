package com.miaowen.bh1xlhw.service.email_template;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateContentForm;
import com.miaowen.bh1xlhw.model.query.email_template.EmailTemplateContentPageForm;

/**
 * @ClassName EmailTemplateContentService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/8 15:12
 */
public interface EmailTemplateContentService {

    void add(EmailTemplateContentForm emailTemplateContent);
    void update(EmailTemplateContentForm emailTemplateContent);
    void deleteBatch(DeleteBatchForm form);
    ResultVO<?> page(EmailTemplateContentPageForm form);
}
