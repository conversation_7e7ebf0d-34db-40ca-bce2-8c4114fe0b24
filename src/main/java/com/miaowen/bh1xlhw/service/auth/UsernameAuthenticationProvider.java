package com.miaowen.bh1xlhw.service.auth;


import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.config.security.login.UsernameAuthenticationToken;
import com.miaowen.bh1xlhw.constant.RedisConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.properties.AdminLoginProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCrypt;

import java.util.Objects;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
public class UsernameAuthenticationProvider implements AuthenticationProvider {
    private final UserDetailsService userDetailsService;
    private final StringRedisTemplate stringRedisTemplate;
    private final AdminLoginProperties adminLoginProperties;

    public UsernameAuthenticationProvider(UserDetailsService userDetailsService, StringRedisTemplate stringRedisTemplate, AdminLoginProperties adminLoginProperties) {
        this.userDetailsService = userDetailsService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.adminLoginProperties = adminLoginProperties;
    }


    public boolean verifyPassword(String userInputPassword, String hashedPassword) {
        try {
            String combinedPassword = userInputPassword + adminLoginProperties.getAuthKey();
            return BCrypt.checkpw(combinedPassword, hashedPassword);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public Authentication authenticate(Authentication authentication) {
        UsernameAuthenticationToken usernameAuthenticationToken = (UsernameAuthenticationToken) authentication;
        String username = usernameAuthenticationToken.getUsername();
        String password = usernameAuthenticationToken.getPassword();
        String verification = usernameAuthenticationToken.getVerification();
        String verificationUuid = usernameAuthenticationToken.getVerificationUuid();
        //验证码校验
        checkVerificationCode(verificationUuid, verification);

        OAuthUser userDetails = (OAuthUser) userDetailsService.loadUserByUsername(username);

        if (!(verifyPassword(password, userDetails.getPassword()))) {
            throw new BadCredentialsException("账号或密码错误!");
        }

        if (!userDetails.getStatus().equals(BooleanEnum.TRUE.getValue())) {
            throw new BadCredentialsException("禁止登录");
        }
        UsernameAuthenticationToken authenticationResult = new UsernameAuthenticationToken(null);
        //认证完成移除密码参数
        userDetails.setPassword("");
        authenticationResult.setDetails(userDetails);
        return authenticationResult;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernameAuthenticationToken.class.isAssignableFrom(authentication);
    }

    private void checkVerificationCode(String verificationCodeUuid, String verificationCode) {
        log.info("verificationCodeUuid:{},verificationCode:{}", verificationCodeUuid, verificationCode);
        if (verificationCodeUuid == null || verificationCode == null) {
            throw new BadCredentialsException("verify_code_uuid和verify_code不能为空!");
        }
        String existCode = stringRedisTemplate.opsForValue().get(String.format(RedisConstant.VERIFY_CODE, verificationCodeUuid));
        if (Objects.isNull(existCode) || !existCode.equals(verificationCode)) {
            throw new BadCredentialsException("验证码错误或失效!");
        }
    }
}
