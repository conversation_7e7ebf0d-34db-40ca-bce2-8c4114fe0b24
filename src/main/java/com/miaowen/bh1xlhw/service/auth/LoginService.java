package com.miaowen.bh1xlhw.service.auth;

import com.miaowen.bh1xlhw.model.vo.login.TokenVO;
import org.springframework.security.authentication.AbstractAuthenticationToken;

import java.util.Map;

public interface LoginService {

    /**
     * 认证发放token
     *
     * @param token:
     * @return null
     */
    TokenVO createAccessToken(AbstractAuthenticationToken token);

    /**
     * 获取机器验证码
     *
     * @return null
     */
    Map<String, Object> verify();

    /**
     * 刷新token
     */
    TokenVO refreshToken();

    void logout();

}
