package com.miaowen.bh1xlhw.service.auth.impl;

import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.model.entity.AccUser;
import com.miaowen.bh1xlhw.repository.AccUserService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;

/**
 * 核心类，通过username获取到用户信息，Security中到处会用到
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final AccUserService accUserService;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        AccUser accUser = accUserService.getByUsernameOrEmail(username);
        if (Objects.isNull(accUser)){
            throw new BizException(ResultEnum.NotFoundUser);
        }
        OAuthUser user = new OAuthUser(accUser.getUsername(), accUser.getPassword(), Collections.emptyList());
        user.setUserId(accUser.getId());
        user.setUsername(accUser.getUsername());
        user.setEmail(accUser.getEmail());
        user.setPassword(accUser.getPassword());
        user.setStatus(accUser.getStatus());
        return user;
    }



}
