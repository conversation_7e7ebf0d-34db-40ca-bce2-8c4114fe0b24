package com.miaowen.bh1xlhw.service.auth.impl;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.captcha.generator.RandomGenerator;
import cn.hutool.core.util.IdUtil;
import com.miaowen.bh1xlhw.config.security.JwtTokenConfig;
import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.constant.RedisConstant;
import com.miaowen.bh1xlhw.model.vo.login.TokenVO;
import com.miaowen.bh1xlhw.service.auth.LoginService;
import com.miaowen.bh1xlhw.utils.SecurityUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.miaowen.bh1xlhw.config.security.JwtAuthenticationTokenFilter.HEADER;

/**
 * LoginServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-06
 */
@Slf4j
@Service
@AllArgsConstructor
public class LoginServiceImpl implements LoginService {
    private final AuthenticationManager authenticationManager;
    private final StringRedisTemplate stringRedisTemplate;
    private final JwtTokenConfig jwtTokenConfig;
    private final HttpServletRequest http;

    @Override
    public TokenVO createAccessToken(AbstractAuthenticationToken token) {
        Authentication authentication = authenticationManager.authenticate(token);
        SecurityContextHolder.getContext().setAuthentication(authentication);
        OAuthUser oAuthUser = (OAuthUser) authentication.getDetails();
        TokenVO tokenVO = jwtTokenConfig.generateToken(oAuthUser);
        String generateToken = tokenVO.getToken();
        String base64Token = Base64.getUrlEncoder().encodeToString(generateToken.getBytes(StandardCharsets.UTF_8));
        tokenVO.setToken(base64Token);
        tokenVO.setEmail(oAuthUser.getEmail());
        tokenVO.setUsername(oAuthUser.getUsername());
        return tokenVO;
    }

    @Override
    public Map<String, Object> verify() {
        Map<String, Object> map = new HashMap<>();
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(150, 40, 4, 4);
        captcha.setGenerator(new RandomGenerator("0123456789", 4));
        log.info("captcha：{}",captcha.getCode());
        String base64String = "data:image/png;base64," + captcha.getImageBase64();
        String uuid = IdUtil.getSnowflake(1, 1).nextIdStr();

        map.put("base64String", base64String);
        map.put("uuid", uuid);

        stringRedisTemplate.opsForValue().set(String.format(RedisConstant.VERIFY_CODE, uuid),  captcha.getCode(), 60, TimeUnit.SECONDS);
        return map;
    }

    @Override
    public TokenVO refreshToken() {
        String base64OldToken = http.getHeader(HEADER);
        String token = new String(Base64.getUrlDecoder().decode(base64OldToken));
        //删除旧token
        stringRedisTemplate.delete(token);
        OAuthUser oAuthUser = SecurityUtil.currentUser();
        //申请一个新token
        TokenVO tokenVO = jwtTokenConfig.generateToken(oAuthUser);
        String generateToken = tokenVO.getToken();
        String base64Token = Base64.getUrlEncoder().encodeToString(generateToken.getBytes(StandardCharsets.UTF_8));
        tokenVO.setToken(base64Token);
        return tokenVO;
    }

    @Override
    public void logout() {
        OAuthUser oAuthUser = SecurityUtil.currentUser();
        removeToken(oAuthUser.getUserId());
        SecurityUtil.clearCurrentUser();
    }

    public void removeToken(Integer id) {
        ValueOperations<String, String> valueOperations = stringRedisTemplate.opsForValue();
        String key = JwtTokenConfig.KEY_PRE + ":" + id;
        String token = valueOperations.get(key);
        List<String> keys = new ArrayList<>();
        keys.add(key);
        if (StringUtils.hasText(token)) {
            keys.add(token);
        }
        stringRedisTemplate.delete(keys);

    }
}
