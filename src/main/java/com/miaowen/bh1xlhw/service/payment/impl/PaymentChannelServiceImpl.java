package com.miaowen.bh1xlhw.service.payment.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.entity.PaymentChannel;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.payment.PaymentChannelForm;
import com.miaowen.bh1xlhw.model.vo.payment.PaymentChannelVO;
import com.miaowen.bh1xlhw.repository.IPaymentChannelService;
import com.miaowen.bh1xlhw.service.payment.PaymentChannelService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 针对表【po_payment_pattern(支付方式模式配置)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 09:50:56
 */
@Service
@AllArgsConstructor
public class PaymentChannelServiceImpl implements PaymentChannelService {
    private final IPaymentChannelService paymentChannelService;

    @Override
    public void savePaymentChannel(PaymentChannelForm form) {
        PaymentChannel paymentChannel = BeanUtils.copy(form, PaymentChannel.class);
        paymentChannel.setPaymentSort(CommonConstant.listToString(form.getPaymentCodeSort()));
        paymentChannelService.save(paymentChannel);
        //同步其他状态为非默认
        if (BooleanEnum.TRUE.getValue().equals(form.getDefaultStatus())){
            paymentChannelService.updateNoDefault(paymentChannel.getId());
        }

    }

    @Override
    public void updatePaymentChannel(Integer id, PaymentChannelForm form) {
        PaymentChannel paymentChannel = BeanUtils.copy(form, PaymentChannel.class);
        paymentChannel.setId(id);
        paymentChannel.setPaymentSort(CommonConstant.listToString(form.getPaymentCodeSort()));
        paymentChannelService.updateById(paymentChannel);
        //同步其他状态为非默认
        if (BooleanEnum.TRUE.getValue().equals(form.getDefaultStatus())){
            paymentChannelService.updateNoDefault(id);
        }
    }

    @Override
    public PageVO<PaymentChannelVO> pagePaymentChannel(PageForm pageForm, String name) {
        Page<PaymentChannel> paymentPatternPage = paymentChannelService.pageInfo(pageForm,name);
        List<PaymentChannel> records = paymentPatternPage.getRecords();
        List<PaymentChannelVO> paymentChannelVOList = getPaymentChannelVoList(records);
        return new PageVO<PaymentChannelVO>(paymentPatternPage).convert(paymentChannelVOList);
    }

    @NotNull
    private static List<PaymentChannelVO> getPaymentChannelVoList(List<PaymentChannel> records) {
        return records.stream().map(paymentChannel -> {
            PaymentChannelVO paymentChannelVo = BeanUtils.copy(paymentChannel, PaymentChannelVO.class);
            paymentChannelVo.setPaymentCodeSort(CommonConstant.stringToList(paymentChannel.getPaymentSort()));
            return paymentChannelVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<PaymentChannelVO> allPaymentPattern() {
        List<PaymentChannel> paymentChannels = paymentChannelService.allList();
        return getPaymentChannelVoList(paymentChannels);
    }

    @Override
    public void deletePaymentChannel(Integer id) {
        paymentChannelService.logicRemoveById(id);
    }
}




