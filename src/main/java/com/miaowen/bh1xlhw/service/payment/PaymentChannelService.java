package com.miaowen.bh1xlhw.service.payment;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.payment.PaymentChannelForm;
import com.miaowen.bh1xlhw.model.vo.payment.PaymentChannelVO;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 09:50:56
 */
public interface PaymentChannelService {

    /**
     * 保存支付通道配置信息
     */
    void savePaymentChannel(PaymentChannelForm form);


    /**
     * 修改支付通道配置信息
     */
    void updatePaymentChannel(Integer id, PaymentChannelForm form);

    /**
     * 获取支付通道信息列表
     */
    PageVO<PaymentChannelVO> pagePaymentChannel(PageForm pageForm ,String name);

    /**
     * 获取支付通道信息列表全部
     */
    List<PaymentChannelVO> allPaymentPattern();

    /**
     * 删除支付配置信息
     */
    void deletePaymentChannel(Integer id);


}
