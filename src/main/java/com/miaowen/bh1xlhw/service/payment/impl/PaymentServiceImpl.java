package com.miaowen.bh1xlhw.service.payment.impl;

/**
 * @ClassName AttachmentService
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 18:54
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.entity.Merchant;
import com.miaowen.bh1xlhw.model.entity.SystemPayment;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.query.payment.PaymentAddOrUpdateForm;
import com.miaowen.bh1xlhw.model.query.payment.PoSystemPaymentPageForm;
import com.miaowen.bh1xlhw.model.vo.payment.PaymentVO;
import com.miaowen.bh1xlhw.repository.IMerchantService;
import com.miaowen.bh1xlhw.repository.IPaymentService;
import com.miaowen.bh1xlhw.service.payment.PaymentService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;

@Slf4j
@Service
@AllArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    private final IMerchantService iMerchantService;
    private final IPaymentService iPaymentService;

    @Override
    public void add(PaymentAddOrUpdateForm payment) throws JsonProcessingException {
        SystemPayment systemPayment = BeanUtils.copy(payment, SystemPayment.class);
        systemPayment.setCountryJson(CommonConstant.listToString(payment.getCountryCodeList()));
        systemPayment.setCurrencyJson(CommonConstant.listToString(payment.getCurrencyCodeList()));
        Merchant merchant = iMerchantService.getMerchantById(payment.getMerchantId());
        systemPayment.setMerchantTypeCode(merchant.getTypeCode());
        iPaymentService.save(systemPayment);
    }


    @Override
    public void update(PaymentAddOrUpdateForm payment) throws JsonProcessingException {
        SystemPayment systemPayment = BeanUtils.copy(payment, SystemPayment.class);
        systemPayment.setCountryJson(CommonConstant.listToString(payment.getCountryCodeList()));
        systemPayment.setCurrencyJson(CommonConstant.listToString(payment.getCurrencyCodeList()));
        Merchant merchant = iMerchantService.getMerchantById(payment.getMerchantId());
        systemPayment.setMerchantTypeCode(merchant.getTypeCode());
        iPaymentService.updateById(systemPayment);
    }

    @Override
    public void deleteBatch(DeleteBatchForm form) {
        if (CollectionUtils.isEmpty(form.getIds())) {
            return;
        }
        iPaymentService.logicDelete(form.getIds());
    }

    @Override
    public ResultVO<PageVO<PaymentVO>> page(PoSystemPaymentPageForm form) {
        IPage<SystemPayment> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<SystemPayment> qw = new QueryWrapper<>();

        if (BooleanEnum.TRUE.getValue().equals(form.getIsRecycle())){
            qw.gt(DELETE_TIME, SQLConstant.DEFAULT_DELETE_TIME);
        }else {
            qw.eq(DELETE_TIME, SQLConstant.DEFAULT_DELETE_TIME);
        }
        // 根据请求参数动态添加查询条件
        if (StringUtils.isNotBlank(form.getName())) {
            qw.like("name", form.getName());
        }
        if (form.getMerchantId() != null) {
            qw.eq("merchant_id", form.getMerchantId());
        }
        if (form.getCurrencyUnit() != null) {
            qw.like("currency_json", form.getCurrencyUnit());
        }
        if (StringUtils.isNotBlank(form.getPaymentCode())) {
            qw.eq("payment_code", form.getPaymentCode());
        }

        qw.orderByAsc("sort");
        qw.orderByDesc("create_time");
        page = iPaymentService.page(page, qw);

        List<SystemPayment> list = page.getRecords();
        //数据处理
        List<PaymentVO> vos = list.stream().map(payment -> {
            PaymentVO paymentVO = BeanUtils.copy(payment, PaymentVO.class);
            Merchant merchant = iMerchantService.getById(payment.getMerchantId());
            paymentVO.setMerchantName(merchant.getName());
            String countryJson = payment.getCountryJson();
            String currencyJson = payment.getCurrencyJson();
            List<String> countryCodeList = CommonConstant.stringToList(countryJson);
            List<String> currencyCodeList = CommonConstant.stringToList(currencyJson);
            paymentVO.setCountryCodeList(countryCodeList);
            paymentVO.setCurrencyCodeList(currencyCodeList);
            return paymentVO;
        }).collect(Collectors.toList());

        return ResultVO.successForPage(vos, page.getTotal(), form.getPageInt(), form.getPageSize());
    }

    @Override
    public void updateState(UpdateStateForm form) {
        SystemPayment updateParam = new SystemPayment();
        updateParam.setStatus(form.getStatus());
        updateParam.setId(form.getId());
        iPaymentService.updateById(updateParam);
    }

    @Override
    public void updateDefault(UpdateIsDefaultForm form) {
        SystemPayment updateParam = new SystemPayment();
        updateParam.setIsDefault(form.getIsDefault());
        updateParam.setId(form.getId());
        iPaymentService.updateById(updateParam);
    }

    @Override
    public void deleteRecycles(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        iPaymentService.removeBatchByIds(deleteBatchForm.getIds());
    }

    @Override
    public void recoverRecycles(DeleteBatchForm deleteBatchForm) {
        List<Integer> ids = deleteBatchForm.getIds();
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        iPaymentService.recoverBatchByIds(deleteBatchForm.getIds());
    }


}
