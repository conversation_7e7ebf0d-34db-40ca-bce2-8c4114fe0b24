package com.miaowen.bh1xlhw.service.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.SystemPayment;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.query.payment.PaymentAddOrUpdateForm;
import com.miaowen.bh1xlhw.model.query.payment.PoSystemPaymentPageForm;
import com.miaowen.bh1xlhw.model.vo.payment.PaymentVO;

/**
 * @ClassName PoSystemFolder
 * @Description
 * <AUTHOR>
 * @Date 2025/5/7 19:22
 * @company 武汉秒闻网络科技有限公司
 */
public interface PaymentService {

    void add(PaymentAddOrUpdateForm payment) throws JsonProcessingException;

    void update(PaymentAddOrUpdateForm payment) throws JsonProcessingException;

    void deleteBatch(DeleteBatchForm form);

    ResultVO<PageVO<PaymentVO>> page(PoSystemPaymentPageForm form);


    void updateState(UpdateStateForm form);

    void updateDefault(UpdateIsDefaultForm form);

    void deleteRecycles(DeleteBatchForm deleteBatchForm);

    void recoverRecycles(DeleteBatchForm deleteBatchForm);
}
