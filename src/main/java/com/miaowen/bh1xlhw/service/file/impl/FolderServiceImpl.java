package com.miaowen.bh1xlhw.service.file.impl;

/**
 * @ClassName AttachmentService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 18:54
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.Folder;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.file.FolderForm;
import com.miaowen.bh1xlhw.model.query.file.FolderPageForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.vo.file.FolderPageVO;
import com.miaowen.bh1xlhw.repository.IFolderService;
import com.miaowen.bh1xlhw.service.file.FolderService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;
import static com.miaowen.bh1xlhw.constant.CommonConstant.ID;

@Slf4j
@Service
public class FolderServiceImpl implements FolderService {

    @Resource
    private IFolderService  folderService;

    @Override
    public void add(FolderForm folder) {
        folderService.save(BeanUtils.copy(folder, Folder.class));
    }

    @Override
    public void update(FolderForm folder) {
        folderService.updateById(BeanUtils.copy(folder, Folder.class));
    }

    @Override
    public void deleteBatch(DeleteBatchForm form) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<Folder> wrapper = new UpdateWrapper<Folder>().in(ID, form.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        folderService.update(wrapper);
    }

    @Override
    public List<FolderPageVO> listAll() {
        // 1. 查询未删除数据
        QueryWrapper<Folder> wrapper = new QueryWrapper<Folder>().eq(DELETE_TIME, 0);

        // 2. 转换结果
        List<FolderPageVO> list = folderService.list(wrapper).stream().map(this::convertToPageVO).collect(Collectors.toList());
        return list;
    }

    @Override
    public ResultVO<?> page(FolderPageForm form) {
        IPage<Folder> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<Folder> qw = new QueryWrapper<>();
        qw.like(DELETE_TIME, 0);

        // 组名字
        if (StringUtils.isNotBlank(form.getName())) {
            qw.like("name", form.getName());
        }

        // 文件分组id
        if (StringUtils.isNotBlank(form.getFolderId())) {
            qw.eq("folder_id", form.getFolderId());
        }

        // 排序值
        if (form.getSort() != null) {
            qw.eq("sort", form.getSort());
        }

        // 是否默认
        if (form.getIsDefault() != null) {
            qw.eq("is_default", form.getIsDefault());
        }

        // 状态
        if (form.getStatus() != null) {
            qw.eq("status", form.getStatus());
        }

        // 排序
        qw.orderByAsc("sort","id");

        // 分页查询
        page = folderService.page(page, qw);

        List<Folder> list = page.getRecords();
        // 数据处理

        return ResultVO.successForPage(list, page.getTotal(), form.getPageInt(), form.getPageSize());
    }


    @Override
    public void updateState(UpdateStateForm form) {
        Folder updateParam = Folder.builder().status(form.getStatus()).build();
        updateParam.setId(form.getId());
        folderService.updateById(updateParam);
    }

    @Override
    public void updateDefault(UpdateIsDefaultForm form) {
        Folder updateParam = Folder.builder().isDefault(form.getIsDefault()).build();
        updateParam.setId(form.getId());
        folderService.updateById(updateParam);
    }


    /**
     * 实体转VO方法
     */
    private FolderPageVO convertToPageVO(Folder entity) {
        FolderPageVO vo = BeanUtils.copy(entity, FolderPageVO.class);
        return vo;
    }
}
