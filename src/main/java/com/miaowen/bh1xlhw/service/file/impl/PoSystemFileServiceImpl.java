package com.miaowen.bh1xlhw.service.file.impl;

/**
 * @ClassName AttachmentService
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 18:54
 */

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.vo.file.FileVO;
import com.miaowen.bh1xlhw.service.oss.FileStorageContext;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.entity.Folder;
import com.miaowen.bh1xlhw.model.entity.PoSystemFile;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.file.PoSystemFilePageForm;
import com.miaowen.bh1xlhw.repository.IFolderService;
import com.miaowen.bh1xlhw.repository.IPoSystemFileService;
import com.miaowen.bh1xlhw.service.file.PoSystemFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

import static com.miaowen.bh1xlhw.constant.CommonConstant.*;

@Slf4j
@Service
public class PoSystemFileServiceImpl implements PoSystemFileService {

    public static final String URL = "http://h1-xl-hw.oss-us-west-1.aliyuncs.com/";
    @Resource
    private IPoSystemFileService fileService;

    @Resource
    private IFolderService folderService;

    // 存储服务（OSS/本地）
    @Resource
    private FileStorageContext storageContext;

    @Value("${storage.type}")
    private String ossType;

    /**
     * 文件上传
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public String uploadFile(MultipartFile file, Integer folderId) throws IOException {

        // 1. 存储文件
        log.info("start uploadFile...........");
        String fileUrl = storageContext.getStrategy(ossType).upload(file);
        log.info("end uploadFile............");
        // 2. 构建实体

        log.info("fileUrl:{}", fileUrl);


        PoSystemFile fileEntity = new PoSystemFile();
        fileEntity.setName(file.getOriginalFilename());
        fileEntity.setContentType(file.getContentType());
        fileEntity.setUrl(fileUrl);
        fileEntity.setFolderId(folderId);
        // 默认启用
        fileEntity.setStatus(BooleanEnum.TRUE.getValue());

        if(!Objects.isNull(folderId) && folderId != 0) {
            Folder folder = folderService.getById(folderId);
            if (Objects.isNull(folder)) {
                Folder entity = new Folder();
                entity.setName(String.valueOf(folderId));
                entity.setFolderId(folderId);
                entity.setIsDefault(BooleanEnum.TRUE.getValue());
                entity.setStatus(BooleanEnum.TRUE.getValue());
                entity.setSort(new Random().nextInt());
                folderService.save(entity);
            }else {
                folder.setFolderId(folderId);
                folderService.updateById(folder);
            }
        }

        // 3. 插入数据库
        fileService.getBaseMapper().insert(fileEntity);
        return fileUrl;
    }

    /**
     * 分页查询文件列表（支持排序）
     */
    @Override
    public PageVO listFiles(Integer pageInt, Integer pageSize, String sortField, String sortOrder, String folderId,String name) {
        // 1. 安全校验排序字段
        validateSortField(sortField);

        // 2. 构建排序SQL片段
        String orderBy = StringUtils.isNotBlank(sortField) ? "ORDER BY " + sortField + " " + ("desc".equalsIgnoreCase(sortOrder) ? "DESC" : "ASC") : "";

        // 3. 执行分页查询
        Page<PoSystemFile> page = new Page<>(pageInt, pageSize);
        QueryWrapper<PoSystemFile> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(DELETE_TIME, 0);
        queryWrapper.like(StringUtils.isNotBlank(name),"name", name);
        queryWrapper.eq(StringUtils.isNotBlank(folderId), "folder_id", folderId);

//        queryWrapper.orderBy(StringUtils.isNotBlank(sortField), false, sortField);
        queryWrapper.orderByDesc("create_time");
        IPage<PoSystemFile> result = fileService.getBaseMapper().selectPage(page, queryWrapper);

        // 4. 转换为统一分页格式
        return new PageVO<>(page, PoSystemFilePageForm.class);
    }

    @Override
    public void deleteBatch(DeleteBatchForm form) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<PoSystemFile> wrapper = new UpdateWrapper<PoSystemFile>().in(ID, form.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        fileService.update(wrapper);
    }

    @Override
    public FileVO uploadLocalFile(MultipartFile file) {
        String upload = storageContext.getStrategy(STORAGE_LOCAL).upload(file);
        FileVO fileVO = new FileVO();
        fileVO.setFileUrl(upload);
        return fileVO;
    }

    /**
     * 软删除文件
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteFile(Integer fileId) throws IOException {
        PoSystemFile file = fileService.getBaseMapper().selectById(fileId);
        if (file != null && file.getDeleteTime() == 0) {
            file.setDeleteTime((int) (System.currentTimeMillis() / 1000));
            fileService.getBaseMapper().updateById(file);

            // 可选：删除物理文件
            storageContext.getStrategy(STORAGE_OSS).delete(file.getUrl());
        }
    }

    /**
     * 更新文件状态
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatus(Integer fileId, Integer status) {
        PoSystemFile file = fileService.getBaseMapper().selectById(fileId);
        if (file != null &&  file.getDeleteTime() == 0) {
            file.setStatus(status);
            fileService.getBaseMapper().updateById(file);
        }
    }

    /**
     * 校验排序字段合法性（防止SQL注入）
     */
    private void validateSortField(String sortField) {
        if (StringUtils.isBlank(sortField)) {
            return;
        }

        Set<String> allowedFields = new HashSet<>(Arrays.asList("create_time", "name", "sort", "update_time"));

        if (!allowedFields.contains(sortField.toLowerCase())) {
            throw new IllegalArgumentException("非法的排序字段");
        }
    }
}
