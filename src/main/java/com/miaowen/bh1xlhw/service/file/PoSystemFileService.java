package com.miaowen.bh1xlhw.service.file;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.file.PoSystemFilePageForm;
import com.miaowen.bh1xlhw.model.vo.file.FileVO;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * @ClassName PoSystemFile
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 19:22
 */
public interface PoSystemFileService {

    String uploadFile(MultipartFile file, Integer folderId) throws IOException;

     void deleteFile(Integer fileId) throws IOException;

     void updateStatus(Integer fileId, Integer status);

      PageVO<PoSystemFilePageForm> listFiles(Integer pageInt, Integer pageSize, String sortField, String sortOrder, String folderId,String name);

    void deleteBatch(DeleteBatchForm form);

    FileVO uploadLocalFile(MultipartFile file);

}
