package com.miaowen.bh1xlhw.service.file;

import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.file.FolderForm;
import com.miaowen.bh1xlhw.model.query.file.FolderPageForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.vo.file.FolderPageVO;

import java.util.List;

/**
 * @ClassName PoSystemFolder
 * @Description
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 19:22
 */
public interface FolderService {

    void add(FolderForm folder);

    void update(FolderForm folder);

    void deleteBatch(DeleteBatchForm form);

    ResultVO<?> page(FolderPageForm form);

    void updateState(UpdateStateForm form);

    void updateDefault(UpdateIsDefaultForm form);

    List<FolderPageVO> listAll();
}
