package com.miaowen.bh1xlhw.service.system;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryTypeForm;
import com.miaowen.bh1xlhw.model.query.system.UpdateStateForm;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryTypeVO;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryVO;

import java.util.List;

/**
 * DictionaryInfoService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08
 */
public interface DictionaryConfigService {
    /**
     * 保存字典类型
     */
    void addDictionaryType(DictionaryTypeForm dictionaryType);

    /**
     * 修改字典类型
     */
    void updateDictionaryType(DictionaryTypeForm dictionaryType);

    /**
     * 批量删除字典类型
     */
    void deleteBatchDictionaryType(DeleteBatchForm form);

    /**
     * 分页查询字典类型
     */
    PageVO<DictionaryTypeVO> pageDictionaryType(DictionaryPageForm form);

    /**
     * 修改字典类型状态
     */
    void updateStateDictionaryType(UpdateStateForm form);

    /**
     * 根据字典类型查询所有字典
     */
    List<DictionaryVO> listDictionaryByCode(String code);

    /**
     * 保存字典
     */
    void addDictionary(DictionaryForm dictionary);

    /**
     * 修改字典
     */
    void updateDictionary(DictionaryForm dictionary);

    /**
     * 批量删除字典
     */
    void deleteBatchDictionary(DeleteBatchForm form);

    /**
     * 分页查询字典
     */
    PageVO<DictionaryVO> pageDictionary(DictionaryPageForm form);

    /**
     * 修改字典状态
     */
    void updateStateDictionary(UpdateStateForm form);
}
