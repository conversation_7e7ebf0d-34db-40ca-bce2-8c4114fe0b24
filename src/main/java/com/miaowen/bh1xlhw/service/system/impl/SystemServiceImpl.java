package com.miaowen.bh1xlhw.service.system.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.config.security.JwtAuthenticationTokenFilter;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import com.miaowen.bh1xlhw.constant.enums.AccRoleEnum;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.query.auth.*;
import com.miaowen.bh1xlhw.model.vo.system.*;
import com.miaowen.bh1xlhw.properties.AdminLoginProperties;
import com.miaowen.bh1xlhw.repository.*;
import com.miaowen.bh1xlhw.service.system.SystemService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.PasswordUtil;
import com.miaowen.bh1xlhw.utils.SecurityUtil;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;
import static com.miaowen.bh1xlhw.constant.CommonConstant.ID;

/**
 * SystemServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-06
 */
@Slf4j
@Service
@AllArgsConstructor
public class SystemServiceImpl implements SystemService {
    private final AccUserRoleService accUserRoleService;
    private final AccUserService accUserService;
    private final AccPermissionService accPermissionService;
    private final AccRolePermissionService accRolePermissionService;
    private final AccRoleService accRoleService;
    private final AdminLoginProperties adminLoginProperties;

    @Override
    public void saveOrUpdatePermission(PermissionForm permissionForm) {
        if (StringUtils.isNotBlank(permissionForm.getPath())){
            AccPermission accPermission = accPermissionService.getByPath(permissionForm.getPath());
            if (Objects.nonNull(accPermission) && !accPermission.getId().equals(permissionForm.getId())){
                throw new BizException(ResultEnum.DataCheckFail.getCode(), "路径已存在");
            }
        }
        AccPermission accPermission = BeanUtils.copy(permissionForm, AccPermission.class);
        accPermissionService.saveOrUpdate(accPermission);
    }

    @Override
    public void removePermission(Integer permissionId) {

        List<AccPermission> allPermission = accPermissionService.list();
        Set<Integer> permissionIds = new HashSet<>();
        //查询所有子菜单
        getAllSubPermission(permissionId, allPermission, permissionIds);
        //逻辑删除所有子菜单 以及 角色菜单关联表
        accPermissionService.logicRemoveByIds(permissionIds);
        accRolePermissionService.logicRemoveByPermissionIds(permissionIds);
    }

   private void getAllSubPermission(Integer currentMenuId, List<AccPermission> oaMenusList, Set<Integer> result){
        if (result.contains(currentMenuId)) {
            return;
        }
        result.add(currentMenuId);
        for (AccPermission menus : oaMenusList) {
            if (menus.getPid() != null && menus.getPid().equals(currentMenuId)) {
                getAllSubPermission(menus.getId(), oaMenusList, result);
            }
        }
    }

    @Override
    public List<PermissionVO> listPermission() {
        List<AccPermission> list = accPermissionService.listExistAll();
        return BeanUtils.copyList(list, PermissionVO.class);
    }

    @Override
    public void saveOrUpdateRole(AccRoleForm accRoleForm) {
        AccRole accRole = BeanUtils.copy(accRoleForm, AccRole.class);
        accRoleService.saveOrUpdate(accRole);
    }

    @Override
    public void removeRole(Integer roleId) {
        accRoleService.logicRemoveById(roleId);
        accUserRoleService.logicRemoveByRoleId(roleId);
        accRolePermissionService.logicRemoveByRoleId(roleId);
    }

    @Override
    public PageVO<RoleVO> pageRole(AccRolePageForm form) {
        LambdaQueryWrapper<AccRole> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(AccRole::getDeleteTime, 0);
        // 根据ID精确查询
        if (form.getId() != null) {
            queryWrapper.eq(AccRole::getId, form.getId());
        }
        // 根据角色名模糊查询
        if (StringUtils.isNotBlank(form.getName())) {
            queryWrapper.like(AccRole::getName, form.getName());
        }
        // 根据描述模糊查询
        if (StringUtils.isNotBlank(form.getDescription())) {
            queryWrapper.like(AccRole::getDescription, form.getDescription());
        }
        // 根据是否是超管查询
        if (form.getIsSuperAdmin() != null) {
            queryWrapper.eq(AccRole::getIsSuperAdmin, form.getIsSuperAdmin());
        }
        // 根据角色状态查询
        if (form.getStatus() != null) {
            queryWrapper.eq(AccRole::getStatus, form.getStatus());
        }

        // 执行分页查询
        Page<AccRole> page = accRoleService.page(new Page<>(form.getPageInt(), form.getPageSize()), queryWrapper);

        //数据处理
        List<AccRole> list = page.getRecords();
        List<Integer> roleIds = StreamUtil.fetchList(list, AccRole::getId);
        List<AccRolePermission> accRolePermissions = accRolePermissionService.listByRoleIds(roleIds);
        Map<Integer, List<Integer>> roleMap = StreamUtil.groupingToList(accRolePermissions, AccRolePermission::getRoleId, AccRolePermission::getPermissionId);

        List<RoleVO> vos = new ArrayList<>();
        for (AccRole accRole : list) {
            RoleVO vo = new RoleVO(accRole);
            List<Integer> permissionIds = roleMap.getOrDefault(vo.getId(), Collections.emptyList());
            vo.setPermissionIds(permissionIds);
            vos.add(vo);
        }
        return new PageVO<RoleVO>(page).convert(vos);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void authorize(AuthorizeForm form) {
        // 错误：.eq(AccRolePermission::getCreateTime, form.getRoleId())
        // 修正为：
        accRolePermissionService.remove(Wrappers.lambdaQuery(AccRolePermission.class)
                .eq(AccRolePermission::getRoleId, form.getRoleId()));

        List<AccRolePermission> list = form.getPermissionIds().stream()
                .map(permissionId -> new AccRolePermission(form.getRoleId(), permissionId))
                .collect(Collectors.toList());

        accRolePermissionService.saveBatch(list);
    }

    @Override
    public UserPermissionVO getMyMenu(Integer userId) {
        // 1. 获取用户绑定的角色ID集合
        List<AccUserRole> userRoles = accUserRoleService.listByUserId(userId);
        Set<Integer> roleIds = StreamUtil.fetchSet(userRoles, AccUserRole::getRoleId);

        // 2. 根据角色ID查询有效角色列表（自动过滤已删除/无效角色）
        List<AccRole> validRoles = accRoleService.listExistByIds(roleIds);

        // 3. 判断是否包含超级管理员角色（使用Stream优化循环）
        boolean isSuperAdmin = validRoles.stream()
                .anyMatch(role -> BooleanEnum.TRUE.getValue().equals(role.getIsSuperAdmin()));

        // 4. 获取权限数据（区分管理员和普通用户）
        List<AccPermission> permissions = isSuperAdmin ?
                accPermissionService.listExistAll() : // 管理员获取全部权限
                getMenuListByRoleIds(roleIds);        // 普通用户根据角色获取权限

        // 5. 过滤无效权限条目（防止脏数据）
        List<AccPermission> validPermissions = permissions.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 6. 权限分类处理（菜单 vs 按钮）
        Map<Boolean, List<AccPermission>> classified = classifyPermissions(validPermissions);
        List<AccPermission> menuPermissions = classified.get(true);
        List<AccPermission> buttonPermissions = classified.get(false);

        // 7. 转换为视图对象
        List<PermissionVO> menuVos = BeanUtils.copyList(menuPermissions, PermissionVO.class);
        List<PermissionVO> buttonVos = BeanUtils.copyList(buttonPermissions, PermissionVO.class);

        return new UserPermissionVO(menuVos, buttonVos);
    }

    /**
     * 权限分类逻辑（菜单类型 vs 按钮类型）
     */
    private Map<Boolean, List<AccPermission>> classifyPermissions(List<AccPermission> permissions) {
        return permissions.stream()
                .collect(Collectors.partitioningBy(
                        perm -> isMenuType(perm.getType())
                ));
    }

    /**
     * 判断权限类型是否为菜单（使用常量避免魔法值）
     */
    private boolean isMenuType(Integer permissionType) {
        return permissionType.equals(AccPermission.Type.FIRST.getValue()) ||
                permissionType.equals(AccPermission.Type.SECOND.getValue());
    }



    private List<AccPermission> getMenuListByRoleIds(Set<Integer> roleIds) {
        List<AccRolePermission> accRolePermissions = accRolePermissionService.listByRoleIds(roleIds);
        Set<Integer> permissionIds = StreamUtil.fetchSet(accRolePermissions, AccRolePermission::getPermissionId);
        return accPermissionService.listExistByIds(permissionIds);
    }


    @Override
    public AccMyInfoVo getMyInfo(Integer userId) {
        AccUser accUser = accUserService.getExistById(userId);
        return new AccMyInfoVo(accUser);
    }

    @Override
    public ResultVO<PageVO<AccUserPageVO>>page(AccUserPageForm form) {
        IPage<AccUser> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<AccUser> qw = new QueryWrapper<>();
        qw.eq(DELETE_TIME, 0);
        if (form.getRealName() != null) {
            qw.like("real_name", form.getRealName());
        }
        if (form.getUsername() != null) {
            qw.like("username", form.getUsername());
        }
        if (form.getEmail() != null) {
            qw.like("email", form.getEmail());
        }
        page = accUserService.page(page, qw);

        //数据处理
        List<AccUser> list = page.getRecords();
        List<AccUserPageVO> res = new ArrayList<>();
        for (AccUser accUser : list) {
            AccUserPageVO vo = BeanUtils.copy(accUser, AccUserPageVO.class);
            List<AccRole> roles = accRoleService.getRolesByIds(accUserRoleService.getRolesByUserId(accUser.getId()).stream().map(AccUserRole::getRoleId).collect(Collectors.toList()));
            vo.setRoles(roles);
            res.add(vo);
        }
        return ResultVO.successForPage(res, page.getTotal(), form.getPageInt(), form.getPageSize());
    }



    @Override
    public AccUserDetailsVO getDetail(Integer userId) {
        AccUser accUser = accUserService.getById(userId);
        if (Objects.nonNull(accUser)) {
            //其他信息处理
            List<AccRole> roles = accRoleService.getRolesByIds(accUserRoleService.getRolesByUserId(userId).stream().map(AccUserRole::getRoleId).collect(Collectors.toList()));
            AccUserDetailsVO vo = BeanUtils.copy(accUser, AccUserDetailsVO.class);
            vo.setRoles(roles);
            return vo;
        }else {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void add(AccUserAddOrUpdateForm form) {
        AccUser accUser = BeanUtils.copy(form, AccUser.class);
        // 唯一性校验（复用方法）
        checkUserUnique(null, accUser.getUsername(), accUser.getEmail());
        if (Objects.nonNull(form.getPassword())){
            String passwordEncode = new BCryptPasswordEncoder().encode(form.getPassword()+ adminLoginProperties.getAuthKey());
            accUser.setPassword(passwordEncode);
            accUser.setPasswordRank(PasswordUtil.passwordToRank(form.getPassword()));
        }else {
            //密码处理：新用户默认给初始密码
            String passwordEncode = new BCryptPasswordEncoder().encode(SystemConstant.DEFAULT_PASSWORD);
            accUser.setPassword(passwordEncode);
            accUser.setPasswordRank((PasswordUtil.passwordToRank(SystemConstant.DEFAULT_PASSWORD)));
        }

        accUserService.save(accUser);

        // 更新角色关联（原子操作）
        saveUserRoles(accUser.getId(), form.getRoles());

    }

    /**
     * 提取公共校验方法：检查用户名/邮箱唯一性
     * @param userId 需要排除的用户ID（更新时使用）
     * @param username 待校验用户名
     * @param email 待校验邮箱
     */
    private void checkUserUnique(Integer userId, String username, String email) {
        List<String> errors = new ArrayList<>(2);

        // 精确查询条件构造
        QueryWrapper<AccUser> queryWrapper = new QueryWrapper<AccUser>()
                .nested(wrapper -> {
                    wrapper.eq("username", username)
                            .or()
                            .eq("email", email);
                });

        // 排除当前用户（更新时）
        if (userId != null) {
            queryWrapper.ne("id", userId);
        }
        queryWrapper.eq(DELETE_TIME, 0);

        // 明确查询字段，减少数据传输量
        queryWrapper.select("username", "email");

        // 获取所有可能冲突的记录
        List<AccUser> conflicts = accUserService.list(queryWrapper);

        // 分离检查逻辑
        boolean usernameExists = conflicts.stream()
                .anyMatch(u -> username.equals(u.getUsername()));
        boolean emailExists = conflicts.stream()
                .anyMatch(u -> email.equals(u.getEmail()));

        if (usernameExists) errors.add("用户名已存在");
        if (emailExists) errors.add("邮箱已存在");

        if (!errors.isEmpty()) {
            throw new BizException(ResultEnum.DataCheckFail.getCode(), String.join(",", errors));
        }
    }

    /**
     * 统一处理用户-角色关联保存
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    private void saveUserRoles(Integer userId, List<Integer> roleIds) {
        // 清理旧关联
        accUserRoleService.remove(Wrappers.<AccUserRole>lambdaQuery()
                .eq(AccUserRole::getUserId, userId));

        if (roleIds != null && !roleIds.isEmpty()) {
            //  校验角色是否存在
            Set<Integer> existingRoleIds = accRoleService.getRolesByIds(roleIds).stream()
                    .map(AccRole::getId)
                    .collect(Collectors.toSet());

            List<Integer> invalidRoleIds = roleIds.stream()
                    .filter(id -> !existingRoleIds.contains(id))
                    .collect(Collectors.toList());

            if (!invalidRoleIds.isEmpty()) {
                throw new BizException(ResultEnum.INVALID_ROLE_IDS.getCode(),
                        "无效的角色ID: " + invalidRoleIds);
            }
            // 使用Stream批量构造关联对象
            List<AccUserRole> relations = roleIds.stream()
                    .map(roleId -> new AccUserRole(userId, roleId))
                    .collect(Collectors.toList());
            accUserRoleService.saveBatch(relations);
        }
    }





    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(AccUserAddOrUpdateForm form) {
        AccUser accUser = BeanUtils.copy(form, AccUser.class);

        // 唯一性校验（复用方法）
        checkUserUnique(accUser.getId(), accUser.getUsername(), accUser.getEmail());

        // 更新用户信息（排除密码字段）
        accUser.setPassword(null);
        accUserService.updateById(accUser);

        // 更新角色关联（原子操作）
        saveUserRoles(accUser.getId(), form.getRoles());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeUser(Integer userId) {
        //删除 acc_user, acc_user_role
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<AccUser> wrapper = new UpdateWrapper<AccUser>().eq(ID, userId).set(DELETE_TIME, System.currentTimeMillis() / 1000);
        accUserService.update(wrapper);

        UpdateWrapper<AccUserRole> accUserRoleUpdateWrapper = new UpdateWrapper<AccUserRole>().eq("user_id", userId).set(DELETE_TIME, System.currentTimeMillis() / 1000);
        accUserRoleService.update(accUserRoleUpdateWrapper);

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(UpdatePasswordForm form) {
        Integer userId = SecurityUtil.currentUser().getUserId();
        //确保当前用户是否拥有修改密码的权限

        // 1. 权限验证逻辑
        List<Integer> roleIds = accUserRoleService.getRolesByUserId(userId)
                .stream()
                .map(AccUserRole::getRoleId)
                .collect(Collectors.toList());

        // 判断是否为超级管理员
        boolean isSuperAdmin = accRoleService.getRolesByIds(roleIds)
                .stream()
                .anyMatch(role -> AccRoleEnum.SUPER_ADMIN.getCode().equals(role.getIsSuperAdmin()));

        // 非管理员只能修改自己的密码
        if (!isSuperAdmin && !userId.equals(form.getId())) {
            throw new BizException(ResultEnum.PERMISSION_DENIED.getCode(),
                    "无权限修改其他用户密码");
        }


        // 2. 验证用户是否存在
        AccUser accUser = accUserService.getExistById(form.getId());
        if (Objects.isNull(accUser)) {
            throw new BizException(ResultEnum.NotFoundUser.getCode(),
                    "用户ID不存在: " + form.getId());
        }


        // 3. 密码校验逻辑
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        // 修正参数顺序：原始密码在前，加密后的密码在后 非管理员需要校验老密码
        String combinedPassword = form.getOldPassword() + adminLoginProperties.getAuthKey();

        if (!isSuperAdmin && !encoder.matches(combinedPassword, accUser.getPassword())) {
            throw new BizException(ResultEnum.PasswordError.getCode(), "旧密码错误");
        }

        // 新密码与确认密码一致性校验
        if (!form.getNewPassword().equals(form.getConfirmPassword())) {
            throw new BizException(ResultEnum.PASSWORD_ERROR.getCode(),
                    "新密码与确认密码不一致");
        }
//
//        // 4. 密码强度校验（示例实现）
//        if (PasswordUtil.calculatePasswordStrength(form.getNewPassword()) < 3) {
//            throw new BizException(ResultEnum.WEAK_PASSWORD.getCode(),
//                    "密码强度不足，需包含大小写字母、数字和特殊字符");
//        }

        // 5. 更新密码
        accUser.setPassword(encoder.encode(form.getNewPassword() + adminLoginProperties.getAuthKey()));
        accUser.setPasswordRank(PasswordUtil.passwordToRank(form.getNewPassword()));
        accUserService.updateById(accUser);

        // 6. 记录日志（可选）
        log.info("用户[ID:{}]密码已更新，操作者:{}", form.getId(), SecurityUtil.currentUser().getPassword());
    }

    @Override
    public ResultVO<?> pagePermission(AccPermissionPageForm form) {
        // 创建分页对象
        IPage<AccPermission> page = new Page<>(form.getPageInt(), form.getPageSize());
        // 创建查询条件
        LambdaQueryWrapper<AccPermission> qw = new LambdaQueryWrapper<>();
        qw.eq(AccPermission::getDeleteTime, 0);

        // 根据菜单名称或按钮名称模糊查询
        if (StringUtils.isNotBlank(form.getTitle())) {
            qw.like(AccPermission::getTitle, form.getTitle());
        }
        // 根据名字模糊查询
        if (StringUtils.isNotBlank(form.getName())) {
            qw.like(AccPermission::getName, form.getName());
        }
        // 根据路径模糊查询
        if (StringUtils.isNotBlank(form.getPath())) {
            qw.like(AccPermission::getPath, form.getPath());
        }
        // 根据组件模糊查询
        if (StringUtils.isNotBlank(form.getComponent())) {
            qw.like(AccPermission::getComponent, form.getComponent());
        }
        // 根据排序值查询
        if (form.getSort() != null) {
            qw.eq(AccPermission::getSort, form.getSort());
        }
        // 根据权限标识查询
        if (StringUtils.isNotBlank(form.getSign())) {
            qw.eq(AccPermission::getSign, form.getSign());
        }
        // 根据菜单图标查询
        if (StringUtils.isNotBlank(form.getIcon())) {
            qw.like(AccPermission::getIcon, form.getIcon());
        }
        // 根据父节点ID查询
        if (form.getPid() != null) {
            qw.eq(AccPermission::getPid, form.getPid());
        }
        // 根据权限类型查询
        if (form.getType() != null) {
            qw.eq(AccPermission::getType, form.getType());
        }
        // 根据页面状态查询
        if (form.getHidden() != null) {
            qw.eq(AccPermission::getHidden, form.getHidden());
        }

        // 执行分页查询
        page = accPermissionService.page(page, qw);

        // 数据处理，将AccPermission列表转换为PermissionVO列表
        List<PermissionVO> voList = page.getRecords().stream()
                .map(permission -> BeanUtils.copy(permission, PermissionVO.class))
                .collect(Collectors.toList());

        // 返回分页结果
        return ResultVO.successForPage(voList, page.getTotal(), form.getPageInt(), form.getPageSize());
    }

    @Override
    public List<RoleVO>  list() {
        List<AccRole> list = accRoleService.getBaseMapper().selectList(Wrappers.lambdaQuery(AccRole.class)
                .eq(AccRole::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        List<RoleVO>  voList = BeanUtils.copyList(list, RoleVO.class);
        return voList;
    }

    @Override
    public ResultVO<?> clear() {
        log.info("userCache is {},清除用户缓存", JwtAuthenticationTokenFilter.cache);
        JwtAuthenticationTokenFilter.cache.clear();
        log.info("清除用户缓存后,userCache is {}.", JwtAuthenticationTokenFilter.cache);
        return ResultVO.success();
    }
}
