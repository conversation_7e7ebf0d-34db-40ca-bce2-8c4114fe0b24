package com.miaowen.bh1xlhw.service.system.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.entity.DictionaryType;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryTypeForm;
import com.miaowen.bh1xlhw.model.query.system.UpdateStateForm;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryTypeVO;
import com.miaowen.bh1xlhw.model.vo.system.DictionaryVO;
import com.miaowen.bh1xlhw.repository.DictionaryService;
import com.miaowen.bh1xlhw.repository.DictionaryTypeService;
import com.miaowen.bh1xlhw.service.system.DictionaryConfigService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * DictionaryInfoServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08
 */
@Service
@AllArgsConstructor
public class DictionaryConfigServiceImpl implements DictionaryConfigService {
    private final DictionaryService dictionaryService;
    private final DictionaryTypeService dictionaryTypeService;



    @Override
    public void deleteBatchDictionaryType(DeleteBatchForm form) {
        dictionaryTypeService.logicBatchRemoveById(form.getIds());
        dictionaryService.logicBatchRemoveByTypeIds(form.getIds());
    }

    @Override
    public void addDictionaryType(DictionaryTypeForm dictionaryTypeForm) {
        //code要保证唯一性
        try {
            DictionaryType dictionaryType = BeanUtils.copy(dictionaryTypeForm, DictionaryType.class);
            dictionaryTypeService.save(dictionaryType);
        } catch (Exception e) {
            throw new BizException(ResultEnum.DICTIONARY_TYPE_EXIST);
        }
    }

    @Override
    public PageVO<DictionaryTypeVO> pageDictionaryType(DictionaryPageForm form) {

        Page<DictionaryType> page = dictionaryTypeService.pageInfo(form);
        List<DictionaryType> list = page.getRecords();
        //数据处理
        List<DictionaryTypeVO> dictionaryTypeVos = BeanUtils.copyList(list, DictionaryTypeVO.class);
        return new PageVO<DictionaryTypeVO>(page).convert(dictionaryTypeVos);
    }
    @Override
    public void updateDictionaryType(DictionaryTypeForm dictionaryTypeForm) {
        try {
            DictionaryType dictionaryType = BeanUtils.copy(dictionaryTypeForm, DictionaryType.class);
            dictionaryTypeService.updateById(dictionaryType);
        } catch (Exception e) {
            throw new BizException(ResultEnum.DICTIONARY_TYPE_EXIST);
        }
    }
    @Override
    public void updateStateDictionaryType(UpdateStateForm form) {
        DictionaryType dictionaryType = BeanUtils.copy(form, DictionaryType.class);
        dictionaryTypeService.updateById(dictionaryType);

    }



    @Override
    public void addDictionary(DictionaryForm dictionaryForm) {
        //保证在dictionaryTypeId相同时，code要保证唯一性
        try {
            Dictionary dictionary = BeanUtils.copy(dictionaryForm, Dictionary.class);
            dictionaryService.save(dictionary);
        } catch (Exception e) {
            throw new BizException(ResultEnum.DICTIONARY_TYPE_EXIST);
        }
    }

    @Override
    public List<DictionaryVO> listDictionaryByCode(String code) {
        DictionaryType dictionaryType = dictionaryTypeService.getOneByCode(code);
        if (Objects.isNull(dictionaryType)) {
            return Collections.emptyList();
        }
        List<Dictionary> dictionaries = dictionaryService.listByTypeId(dictionaryType.getId());
        return BeanUtils.copyList(dictionaries, DictionaryVO.class);
    }

    @Override
    public void updateDictionary(DictionaryForm dictionaryForm) {
        //保证在dictionaryTypeId相同时，code要保证唯一性
        try {
            Dictionary dictionary = BeanUtils.copy(dictionaryForm, Dictionary.class);
            dictionaryService.updateById(dictionary);
        } catch (Exception e) {
            throw new BizException(ResultEnum.DICTIONARY_TYPE_EXIST);
        }
    }

    @Override
    public void deleteBatchDictionary(DeleteBatchForm form) {
        dictionaryService.logicBatchRemoveByIds(form.getIds());
    }

    @Override
    public PageVO<DictionaryVO> pageDictionary(DictionaryPageForm form) {
        Page<Dictionary> page = dictionaryService.pageInfo(form);
        List<Dictionary> list = page.getRecords();
        //数据处理
        List<DictionaryVO> dictionaryVos = BeanUtils.copyList(list, DictionaryVO.class);
        return new PageVO<DictionaryVO>(page).convert(dictionaryVos);
    }

    @Override
    public void updateStateDictionary(UpdateStateForm form) {
        Dictionary dictionary = BeanUtils.copy(form, Dictionary.class);
        dictionaryService.updateById(dictionary);
    }
}
