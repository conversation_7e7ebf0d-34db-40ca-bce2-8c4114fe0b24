package com.miaowen.bh1xlhw.service.system;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.auth.*;
import com.miaowen.bh1xlhw.model.vo.system.*;

import java.util.List;

/**
 * SystemService :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-06
 */
public interface SystemService {
    /**
     * 保存或修改菜单权限
     */
    void saveOrUpdatePermission(PermissionForm permissionForm);

    /**
     * 移除菜单权限
     */
    void removePermission(Integer permissionId);

    /**
     * 菜单列表
     */
    List<PermissionVO> listPermission();

    /**
     * 保存或修改菜单角色
     */
    void saveOrUpdateRole(AccRoleForm accRoleForm);

    /**
     * 移除菜单角色
     */
    void removeRole(Integer roleId);

    /**
     * 角色列表
     */
    PageVO<RoleVO> pageRole(AccRolePageForm form);

    /**
     * 角色绑定权限
     */
    void authorize(AuthorizeForm form);


    /**
     * 我的菜单
     */
    UserPermissionVO getMyMenu(Integer userId);

    /**
     * 个人主页
     */
    AccMyInfoVo getMyInfo(Integer userId);


    /**
     * 用户分页
     */
    ResultVO<PageVO<AccUserPageVO>> page(AccUserPageForm form);

    /**
     * 用户详情
     */
    AccUserDetailsVO getDetail(Integer userId);

    /**
     * 用户
     */
    void add(AccUserAddOrUpdateForm form);

    /**
     * 修改
     */
    void update(AccUserAddOrUpdateForm form);

    /**
     * 用户删除
     */
    void removeUser(Integer userId);

    /**
     * 修改密码
     */
    void updatePassword(UpdatePasswordForm form);

    ResultVO<?> pagePermission(AccPermissionPageForm form);

    List<RoleVO> list();

    ResultVO<?> clear();
}
