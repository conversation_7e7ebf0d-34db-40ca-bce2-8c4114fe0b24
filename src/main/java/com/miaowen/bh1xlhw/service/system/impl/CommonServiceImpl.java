package com.miaowen.bh1xlhw.service.system.impl;

import com.miaowen.bh1xlhw.constant.enums.Platform;
import com.miaowen.bh1xlhw.service.system.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * CommonServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Slf4j
@Service
public class CommonServiceImpl implements CommonService {
    @Override
    public List<String> listPlatform() {
        return Arrays.stream(Platform.values()).map(Platform::getType).collect(Collectors.toList());
    }
}
