package com.miaowen.bh1xlhw.service.oss;

import com.miaowen.bh1xlhw.utils.DateUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.util.UUID;

/**
 * @company 武汉秒闻网络科技有限公司
 * @ClassName ILanguageService
 * @Description 针对表【po_system_language(后台系统货币表)】的数据库操作Service
 * <AUTHOR>
 * @Date 2025/5/7 11:28
 */
public interface IFileStorageService {


    /**
     * 判断是否支持当前存储类型
     */
    boolean supports(String storageType);

    /**
     * 上传文件
     *
     * @return 存储路径（服务无关的唯一标识）
     */
    String upload(MultipartFile multipartFile);

    /**
     * 删除文件
     *
     * @param storagePath 存储路径
     */
    void delete(String storagePath) throws IOException;

    /**
     * 获取访问URL
     */
    String getAccessUrl(String storagePath);

    /**
     * 存储类型标识
     */
    String getStorageType();

    default String generateUniqueFileName(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return "storage/" + LocalDate.now().format(DateUtils.NUM_DATE_FORMAT) + "/" + UUID.randomUUID() + "." + fileExtension;
    }


}
