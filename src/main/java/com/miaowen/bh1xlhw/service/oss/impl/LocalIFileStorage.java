package com.miaowen.bh1xlhw.service.oss.impl;

import com.miaowen.bh1xlhw.service.oss.IFileStorageService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDate;
import java.util.UUID;

import static com.miaowen.bh1xlhw.constant.CommonConstant.STORAGE_LOCAL;

/**
 * @ClassName LocalFileStorageService
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 19:10
 */
@Slf4j
@Service(value = STORAGE_LOCAL)
@ConditionalOnProperty("storage.local.base-dir")
public class LocalIFileStorage implements IFileStorageService {

    @Value("${storage.local.base-dir:/data/uploads}")
    private String baseDir;

    @PostConstruct
    public void init() throws IOException {
        Files.createDirectories(Paths.get(baseDir));
    }

    @Override
    public boolean supports(String storageType) {
        return STORAGE_LOCAL.equalsIgnoreCase(storageType);
    }

    @Override
    public String upload(MultipartFile file) {
        String savedName = generateUniqueFileName(file);
        Path targetPath = Paths.get(baseDir, savedName);
        try {
            Files.copy(file.getInputStream(), targetPath, StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            log.info("本地文件上次失败");
        }
        return getAccessUrl(savedName);
    }

    @Override
    public String generateUniqueFileName(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        String fileExtension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return UUID.randomUUID() + "." + fileExtension;
    }

    @Override
    public void delete(String storagePath) throws IOException {
        Path filePath = Paths.get(baseDir, storagePath);
        Files.deleteIfExists(filePath);
    }

    @Override
    public String getAccessUrl(String storagePath) {
        return baseDir + "/" + storagePath;
    }

    @Override
    public String getStorageType() {
        return STORAGE_LOCAL;
    }
}
