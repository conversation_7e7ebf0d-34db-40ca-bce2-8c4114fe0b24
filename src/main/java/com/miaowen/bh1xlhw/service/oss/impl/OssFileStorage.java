package com.miaowen.bh1xlhw.service.oss.impl;

import com.aliyun.oss.OSS;
import com.miaowen.bh1xlhw.properties.AlibabaOssProperties;
import com.miaowen.bh1xlhw.service.oss.IFileStorageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

import static com.miaowen.bh1xlhw.constant.CommonConstant.STORAGE_OSS;

/**
 * @ClassName LocalFileStorageService
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 19:10
 */
@Slf4j
@AllArgsConstructor
public class OssFileStorage implements IFileStorageService {
    private final AlibabaOssProperties alibabaOssProperties;
    private final OSS ossClient;

    @Override
    public boolean supports(String storageType) {
        return STORAGE_OSS.equalsIgnoreCase(storageType);
    }

    @Override
    public String upload(MultipartFile file) {
        String objectKey = alibabaOssProperties.getBasePath()+ "/" + generateUniqueFileName(file);
        log.info("upload object key: {}", objectKey);
        try {
            ossClient.putObject(alibabaOssProperties.getBucketName(), objectKey, file.getInputStream());
        } catch (IOException e) {
            log.error("oss 上次文件失败", e);
        }
        return getAccessUrl(objectKey);
    }

    @Override
    public void delete(String objectKey) {
        ossClient.deleteObject(alibabaOssProperties.getBucketName(), objectKey);
    }

    @Override
    public String getAccessUrl(String objectKey) {
        return "https://"+alibabaOssProperties.getBucketName() + "." + alibabaOssProperties.getEndpoint() + "/" + objectKey;
    }

    @Override
    public String getStorageType() {
        return STORAGE_OSS;
    }
}