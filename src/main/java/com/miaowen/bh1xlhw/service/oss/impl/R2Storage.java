package com.miaowen.bh1xlhw.service.oss.impl;

import com.amazonaws.HttpMethod;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.properties.AwsR2ConfigProperties;
import com.miaowen.bh1xlhw.service.oss.IFileStorageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import static com.miaowen.bh1xlhw.constant.CommonConstant.*;

/**
 * R2StorageServiceImpl :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-03
 */
@Slf4j
@AllArgsConstructor
public class R2Storage implements IFileStorageService {
    private final AmazonS3 amazonS3;
    private final AwsR2ConfigProperties awsR2ConfigProperties;

    @Override
    public boolean supports(String storageType) {
        return STORAGE_R2.equalsIgnoreCase(storageType);
    }

    @Override
    public String upload(MultipartFile file){
        // 获取文件后缀
        String fileName = generateUniqueFileName(file);
        ObjectMetadata metadata = new ObjectMetadata();
        metadata.setContentLength(file.getSize());
        metadata.setContentType(file.getContentType());

        try {
            amazonS3.putObject(new PutObjectRequest(awsR2ConfigProperties.getBucketName(), fileName, file.getInputStream(),
                metadata)
                // 关键！设置公开访问
                .withCannedAcl(CannedAccessControlList.PublicRead)
            );
        } catch (IOException e) {
            log.error("cloudflare 上传文件报错",e);
            throw new BizException(ResultEnum.FILE_UPLOAD_FAILED);
        }

        return getAccessUrl(fileName);
    }

    @Override
    public void delete(String storagePath) throws IOException {
        try {
            amazonS3.deleteObject(new DeleteObjectRequest(awsR2ConfigProperties.getBucketName(), storagePath));
        } catch (Exception e) {
            log.error("删除文件失败: {}", storagePath);
        }
    }


    // 生成永久访问链接（无过期时间）
    public String getPublicUrl(String fileName) {
        String fileNameNew = fileName;
        try {
            fileNameNew = URLEncoder.encode(fileName, "UTF-8");
        } catch (UnsupportedEncodingException e) {
          log.error("cloudflare r2 getPublicUrl 报错: {}", e.getMessage());
        }
        return String.format("%s/%s", awsR2ConfigProperties.getOssDomain(), fileNameNew);
    }

    @Override
    public String getAccessUrl(String storagePath) {
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(awsR2ConfigProperties.getBucketName(), storagePath).withMethod(HttpMethod.GET);
        return getPublicUrl(storagePath);
    }



    @Override
    public String getStorageType() {
        return STORAGE_R2;
    }
}
