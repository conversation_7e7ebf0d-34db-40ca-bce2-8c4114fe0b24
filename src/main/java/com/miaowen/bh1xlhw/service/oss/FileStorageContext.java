package com.miaowen.bh1xlhw.service.oss;

import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@Component
public class FileStorageContext {
    private final Map<String, IFileStorageService> strategieMap;
    
    // 通过构造器自动注入所有策略实现
    public FileStorageContext(List<IFileStorageService> strategies) {
        if (!CollectionUtils.isEmpty(strategies)){
            this.strategieMap = StreamUtil.map(strategies, IFileStorageService::getStorageType);
        }else {
            this.strategieMap = new HashMap<>();
        }
        log.info("文件存储策略: {}", strategieMap.toString());
    }

    /**
     * 获取匹配的存储策略
     * @param storageType 策略类型（OSS/LOCAL等）
     */
    public IFileStorageService getStrategy(String storageType) {
        return Optional.ofNullable(strategieMap.get(storageType)).orElseThrow(() -> new BizException("未找到匹配的存储策略: " + storageType));
    }
}
