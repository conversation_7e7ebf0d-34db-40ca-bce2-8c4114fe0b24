package com.miaowen.bh1xlhw.service;

import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.utils.SecurityUtil;

import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

import static com.miaowen.bh1xlhw.common.vo.ResultEnum.FREQUENT_OPERATION;

/**
 * 
 * 公共服务
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-03-24
 */
@Component
@AllArgsConstructor
public class CommonService {
    private final StringRedisTemplate redisTemplate;
    private HttpServletRequest httpRequest;

    public void limit() {
        OAuthUser oAuthUser = SecurityUtil.currentUser();
        String requestUri = httpRequest.getRequestURI();
        String key = requestUri + "_" + oAuthUser.getUserId();
        this.limit(key);
    }

    public void limit(String key) {
        this.limit(key, 3);
    }

    public void limit(String key, int seconds) {
        Boolean b = redisTemplate.opsForValue().setIfAbsent(key, "1", seconds, TimeUnit.SECONDS);
        if (Boolean.FALSE.equals(b)) {
            throw new BizException(FREQUENT_OPERATION);
        }
    }
}
