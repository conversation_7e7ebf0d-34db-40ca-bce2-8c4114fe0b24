package com.miaowen.bh1xlhw.service.language.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.entity.Language;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.language.LanguageForm;
import com.miaowen.bh1xlhw.model.query.language.LanguagePageForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePageVO;
import com.miaowen.bh1xlhw.repository.ILanguageService;
import com.miaowen.bh1xlhw.service.language.LanguageService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.*;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
public class LanguageServiceImpl implements LanguageService {


    @Resource
    private ILanguageService languageService;

    @Override
    public PageVO<LanguagePageVO> pageLanguage(LanguagePageForm form) {
        // 1. 构建分页查询条件
        Page<Language> page = new Page<>(form.getPageInt(), form.getPageSize());
        QueryWrapper<Language> wrapper = new QueryWrapper<Language>().eq(DELETE_TIME, 0); // 过滤未删除的数据
        wrapper.eq(DELETE_TIME, 0);
        // 根据LanguagePageForm的参数动态添加查询条件
        if (form.getId() != null) {
            wrapper.eq("id", form.getId());
        }
        if (StringUtils.isNotBlank(form.getName())) {
            wrapper.like("name", form.getName());
        }
        if (StringUtils.isNotBlank(form.getFront())) {
            wrapper.like("front", form.getFront());
        }
        if (StringUtils.isNotBlank(form.getCode())) {
            wrapper.eq("code", form.getCode());
        }
        if (form.getSort() != null) {
            wrapper.eq("sort", form.getSort());
        }
        if (form.getIsDefault() != null) {
            wrapper.eq("is_default", form.getIsDefault());
        }
        if (form.getStatus() != null) {
            wrapper.eq("status", form.getStatus());
        }
        if (form.getCreateTime() != null) {
            wrapper.eq("create_time", form.getCreateTime());
        }
        wrapper.orderByAsc("sort","id");

        // 2. 执行分页查询
        Page<Language> result = languageService.page(page, wrapper);


        // 3. 构建分页响应
        return new PageVO<>(result, LanguagePageVO.class);
    }

    @Override
    public ResultVO<Void> saveOrUpdateLanguage(LanguageForm languageForm) {
        //判断是否重复code值
        if (languageService.count(new QueryWrapper<Language>().eq("code", languageForm.getCode())
                .ne(Objects.nonNull(languageForm.getId()), "id", languageForm.getId())) > 0) {
            return ResultVO.fail(ResultEnum.PARAM_ERROR,"语言编码已存在");
        }

        // 1. 转换Form为Entity
        Language entity = BeanUtils.copy(languageForm, Language.class);

        // 2. 设置时间（如果自动填充未配置
        if (entity.getId() == null) {
            entity.setCreateTime(LocalDateTime.now());
        }
        entity.setUpdateTime(LocalDateTime.now());

        // 3. 保存或更新
        languageService.saveOrUpdate(entity);
        return ResultVO.success();
    }

    @Override
    public void updateState(UpdateStateForm form) {
        // 1. 构建更新条件
        UpdateWrapper<Language> wrapper = new UpdateWrapper<Language>().eq(ID, form.getId()).set(STATUS, form.getStatus());

        // 2. 执行更新
        languageService.update(wrapper);
    }

    @Override
    public List<LanguagePageVO> listAll() {
        // 1. 查询未删除数据
        QueryWrapper<Language> wrapper = new QueryWrapper<Language>().eq(DELETE_TIME, 0);

        // 2. 转换结果
        return languageService.list(wrapper).stream().map(this::convertToPageVO).collect(Collectors.toList());
    }

    @Override
    public void updateDefault(UpdateIsDefaultForm form) {
        // 1. 构建更新条件
        UpdateWrapper<Language> wrapper = new UpdateWrapper<Language>().eq(ID, form.getId()).set(IS_DEFAULT, form.getIsDefault());

        // 2. 执行更新
        languageService.update(wrapper);
    }

    @Override
    public void deleteBatch(DeleteBatchForm deleteBatchForm) {
        // 1. 逻辑删除（设置delete_time为当前时间戳）
        UpdateWrapper<Language> wrapper = new UpdateWrapper<Language>().in(ID, deleteBatchForm.getIds()).set(DELETE_TIME, System.currentTimeMillis() / 1000);

        // 2. 执行更新
        languageService.update(wrapper);
    }

    /**
     * 实体转VO方法
     */
    private LanguagePageVO convertToPageVO(Language entity) {
        LanguagePageVO vo = BeanUtils.copy(entity, LanguagePageVO.class);
        return vo;
    }
}




