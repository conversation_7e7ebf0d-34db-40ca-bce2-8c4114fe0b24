package com.miaowen.bh1xlhw.service.language;

import com.miaowen.bh1xlhw.common.vo.PageVO;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.model.query.DeleteBatchForm;
import com.miaowen.bh1xlhw.model.query.UpdateStateForm;
import com.miaowen.bh1xlhw.model.query.language.LanguageForm;
import com.miaowen.bh1xlhw.model.query.language.LanguagePageForm;
import com.miaowen.bh1xlhw.model.query.language.UpdateIsDefaultForm;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePageVO;

import java.util.List;

/**
 * @ClassName PoCurrencyService
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 11:26
 */
public interface LanguageService {

    PageVO<LanguagePageVO> pageLanguage(LanguagePageForm form);

    ResultVO<Void> saveOrUpdateLanguage(LanguageForm languageForm);

    void updateState(UpdateStateForm form);

    List<LanguagePageVO> listAll();

    void updateDefault(UpdateIsDefaultForm form);

    void deleteBatch(DeleteBatchForm deleteBatchForm);
}
