package com.miaowen.bh1xlhw.service.language.impl;

import cn.hutool.core.map.MapUtil;
import com.miaowen.bh1xlhw.model.entity.LanguagePack;
import com.miaowen.bh1xlhw.model.entity.Sentence;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePackItemColVO;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePackItemVO;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePackVO;
import com.miaowen.bh1xlhw.model.vo.language.LanguagePageVO;
import com.miaowen.bh1xlhw.repository.ILanguagePackService;
import com.miaowen.bh1xlhw.repository.ISentenceService;
import com.miaowen.bh1xlhw.service.language.LanguagePackService;
import com.miaowen.bh1xlhw.service.language.LanguageService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description
 * @Date 2025/5/7 11:28
 */
@Service
public class LanguagePackServiceImpl implements LanguagePackService {


    @Resource
    ISentenceService iSentenceService;

    @Resource
    LanguageService languageService;

    @Resource
    ILanguagePackService iLanguagePackService;

    @Override
    public LanguagePackVO listAll() {
        // 获取所有需要国际化的句子列表
        List<Sentence> sentences = iSentenceService.listAllSentence();
        // 获取系统支持的所有语言列表
        List<LanguagePageVO> languages = languageService.listAll();
        // 获取所有已存在的语言包数据
        List<LanguagePack> languagePacks = iLanguagePackService.list();

        /**
         * 将语言包数据转换为双层嵌套Map结构：
         * 第一层Key: 句子ID（sentenceId）
         * 第二层Key: 语言代码（languageCode）
         * Value: 对应的语言包对象
         */
        Map<Integer, Map<String, LanguagePack>> languagePacksMap = convertToTwoLevelMap(
                languagePacks,
                LanguagePack::getSentenceId,    // 第一层键提取方法
                LanguagePack::getLanguageCode,  // 第二层键提取方法
                x -> x                          // 值直接使用对象本身
        );

        List<LanguagePackItemVO> items = new ArrayList<>();

        // 遍历所有句子构建返回结构
        sentences.forEach(sentence -> {
            // 创建条目对象（包含原始句子信息）
            LanguagePackItemVO itemVo = LanguagePackItemVO.builder()
                    .sentenceId(sentence.getId())
                    .content(sentence.getContent())
                    .build();

            List<LanguagePackItemColVO> cols = new ArrayList<>();

            // 为每个支持的语言创建列信息
            languages.forEach(language -> {
                // 创建语言列对象
                LanguagePackItemColVO colVo = LanguagePackItemColVO.builder()
                        .languageCode(language.getCode())
                        .languageName(language.getName())
                        .build();

                String content = "";
                try {
                    // 尝试获取已存在的翻译内容
                    content = languagePacksMap.get(sentence.getId())    // 获取当前句子的所有语言包
                            .get(language.getCode())                       // 获取指定语言的包
                            .getContent();                                 // 提取翻译内容

                    // 设置已存在语言包的ID（用于后续更新操作）
                    Integer id = languagePacksMap.get(sentence.getId())
                            .get(language.getCode())
                            .getId();
                    colVo.setLanguagePackId(id);
                } catch (Exception ignored) {
                    // 忽略空指针异常（当某个句子没有对应语言的翻译时）
                }

                colVo.setContent(content);  // 设置内容（已存在翻译或空字符串）
                cols.add(colVo);            // 将列对象添加到集合
            });

            itemVo.setCols(cols);  // 设置当前句子的多语言列数据
            items.add(itemVo);     // 将条目添加到结果列表
        });

        // 构建最终返回对象
        return LanguagePackVO.builder()
                .items(items)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)  // 声明式事务管理（任何异常都回滚）
    public void setLanguagePack(LanguagePackVO vo) {
        // 准备需要新增的语言包集合
        List<LanguagePack> languagePacksAdd = new ArrayList<>();
        // 准备需要更新的语言包集合
        List<LanguagePack> languagePacksUpdate = new ArrayList<>();

        // 遍历所有条目
        vo.getItems().forEach(item -> {
            // 遍历条目的所有语言列
            item.getCols().forEach(col -> {
                // 构建语言包实体
                LanguagePack languagePack = new LanguagePack();
                languagePack.setSentenceId(item.getSentenceId());   // 设置所属句子ID
                languagePack.setLanguageCode(col.getLanguageCode());// 设置语言代码
                languagePack.setContent(col.getContent());          // 设置翻译内容

                if (col.getLanguagePackId() == null) {
                    // 新增逻辑：当没有ID时添加到新增集合
                    languagePacksAdd.add(languagePack);
                } else {
                    // 更新逻辑：当存在ID时添加到更新集合
                    languagePack.setId(col.getLanguagePackId());    // 设置需要更新的记录ID
                    languagePacksUpdate.add(languagePack);
                }
            });
        });

        // 批量保存新增记录
        iLanguagePackService.saveBatch(languagePacksAdd);
        // 批量更新现有记录
        iLanguagePackService.updateBatchById(languagePacksUpdate);
    }

    /**
     * 将列表转换为双层嵌套Map结构的通用方法
     * @param list 原始数据列表
     * @param col1Getter 第一层Key的提取函数
     * @param col2Getter 第二层Key的提取函数
     * @param valueGetter Value的提取函数
     * @param <T> 列表元素类型
     * @param <K1> 第一层Key类型
     * @param <K2> 第二层Key类型
     * @param <V> Value类型
     * @return 双层嵌套Map结构 Map<K1, Map<K2, V>>
     */
    public <T, K1, K2, V> Map<K1, Map<K2, V>> convertToTwoLevelMap(
            List<T> list,
            Function<T, K1> col1Getter,  // 函数式接口：提取外层Map的Key
            Function<T, K2> col2Getter,  // 函数式接口：提取内层Map的Key
            Function<T, V> valueGetter    // 函数式接口：提取Map的Value
    ) {
        return list.stream().collect(
                Collectors.groupingBy(  // 外层分组
                        col1Getter,
                        Collectors.toMap(   // 内层Map构造
                                col2Getter,     // 内层Key提取
                                valueGetter,    // Value提取
                                (v1, v2) -> v2, // 冲突解决策略（保留后者）
                                MapUtil::newHashMap  // 指定Map实现（使用工具类创建）
                        )
                )
        );
    }

}




