package com.miaowen.bh1xlhw.config.strategy;

import com.miaowen.bh1xlhw.service.email.EmailSendStrategy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 邮件发送策略工厂
 * <AUTHOR>
 * @Date 2025/3/18 17:49
 */
@Component
public class EmailSendStrategyContext {
    
    @Resource
    private List<EmailSendStrategy> strategies;
    
    private final Map<Integer, EmailSendStrategy> strategyMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        for (EmailSendStrategy strategy : strategies) {
            strategyMap.put(strategy.getType(), strategy);
        }
    }
    
    public EmailSendStrategy getStrategy(Integer type) {
        EmailSendStrategy strategy = strategyMap.get(type);
        if (strategy == null) {
            throw new IllegalArgumentException("未找到对应的邮件发送策略: " + type);
        }
        return strategy;
    }
} 