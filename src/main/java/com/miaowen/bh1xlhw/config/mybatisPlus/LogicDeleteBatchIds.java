package com.miaowen.bh1xlhw.config.mybatisPlus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.toolkit.sql.SqlScriptUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * 批量逻辑删除
 * LogicDeleteBatchIds :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-11
 */
public class LogicDeleteBatchIds extends AbstractMethod {

    public LogicDeleteBatchIds() {
        this(MybatisPlusSqlMethod.LOGIC_DELETE_BATCH_BY_IDS.getMethod());
    }

    /**
     * @param methodName 方法名
     * @since 3.5.0
     */
    protected LogicDeleteBatchIds(String methodName) {
        super(methodName);
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        MybatisPlusSqlMethod sqlMethod = MybatisPlusSqlMethod.LOGIC_DELETE_BATCH_BY_IDS;

        String sql = String.format(sqlMethod.getSql(),
            tableInfo.getTableName(),
            "set delete_time = UNIX_TIMESTAMP()",
            tableInfo.getKeyColumn(),
            SqlScriptUtils.convertForeach("#{item}", COLL, null, "item", COMMA),
            "and delete_time = 0");
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql , Object.class);
        return this.addUpdateMappedStatement(mapperClass, modelClass, methodName, sqlSource);
    }
}
