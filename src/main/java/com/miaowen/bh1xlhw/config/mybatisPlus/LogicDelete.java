package com.miaowen.bh1xlhw.config.mybatisPlus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public class LogicDelete extends AbstractMethod {

    public LogicDelete() {
        this(MybatisPlusSqlMethod.LOGIC_DELETE.getMethod());
    }

    /**
     * @since 3.5.0
     * @param name 方法名
     */
    public LogicDelete(String name) {
        super(name);
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        MybatisPlusSqlMethod logicDelete = MybatisPlusSqlMethod.LOGIC_DELETE;
        String sql = String.format(logicDelete.getSql(),
            tableInfo.getTableName(),
            "set delete_time = UNIX_TIMESTAMP()",
            sqlWhereEntityWrapper(true, tableInfo),
            "and delete_time = 0");
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, methodName, sqlSource);
    }
}
