package com.miaowen.bh1xlhw.config.mybatisPlus;

import com.baomidou.mybatisplus.extension.plugins.handler.TableNameHandler;
import com.miaowen.bh1xlhw.config.mybatisPlus.ShardingTableContent;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneId;
import java.util.Date;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import org.springframework.util.StringUtils;

import java.time.LocalDate;

/**
 * 按月分表处理器
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@AllArgsConstructor
@Slf4j
public class MonthShardingTableNameHandler implements TableNameHandler {

    /**
     * 存储分表参数的ThreadLocal
     * 使用 InheritableThreadLocal 支持子线程继承
     */
    private static final InheritableThreadLocal<ShardingContext> CONTEXT_HOLDER = new InheritableThreadLocal<>();



    @Override
    public String dynamicTableName(String sql, String tableName) {
        if (!ShardingTableContent.TABLE_CREATE.containsKey(tableName)) {
            return tableName;
        }

        try {
            ShardingContext context = CONTEXT_HOLDER.get();
            String suffix = getSuffix(context);
            String finalTableName = tableName + "_" + suffix;
            log.debug("动态表名处理: {} -> {}, SQL: {}", tableName, finalTableName, sql);
            return finalTableName;

        } catch (Exception e) {
            log.error("动态表名处理异常, tableName: {}, sql: {}", tableName, sql, e);
            // 异常时返回当前月份的表
            String monthSuffix = LocalDate.now().format(DateUtils.MONTH_DATE_FORMAT);
            return tableName + "_" + monthSuffix;
        }
    }

    /**
     * 获取表名后缀
     */
    private String getSuffix(ShardingContext context) {
        if (Objects.nonNull(context) && StringUtils.hasText(context.getDateSuffix())) {
            return context.getDateSuffix();
        }

        // 默认使用当前月份
        return LocalDate.now().format(DateUtils.MONTH_DATE_FORMAT);
    }

    /**
     * 设置分表上下文
     */
    public static void setContext(String dateSuffix) {
        CONTEXT_HOLDER.set(new ShardingContext(dateSuffix));
    }

    /**
     * 设置分表上下文
     */
    public static void setParams(String dateSuffix) {
        CONTEXT_HOLDER.set(new ShardingContext(dateSuffix));
    }

    public static void setParams(Date queryDate) {
        if (queryDate != null) {
            LocalDate localDate = queryDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

            // 检查表是否存在，不存在则创建
            CONTEXT_HOLDER.set(new ShardingContext(localDate.format(DateUtils.MONTH_DATE_FORMAT)));
        }
    }

    /**
     * 设置分表上下文（支持更多参数）
     */
    public static void setContext(String dateSuffix, String bizType) {
        CONTEXT_HOLDER.set(new ShardingContext(dateSuffix, bizType));
    }

    /**
     * 清理分表上下文
     */
    public static void clearContext() {
        CONTEXT_HOLDER.remove();
    }
    /**
     * 清理分表上下文
     */
    public static void clearParams() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 获取当前上下文
     */
    public static ShardingContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 分表上下文
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ShardingContext {
        /**
         * 日期后缀 (如: 202412)
         */
        private String dateSuffix;

        /**
         * 业务类型 (预留字段，用于更复杂的分表逻辑)
         */
        private String bizType;

        public ShardingContext(String dateSuffix) {
            this.dateSuffix = dateSuffix;
        }
    }
}