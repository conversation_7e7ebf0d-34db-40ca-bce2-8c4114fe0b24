package com.miaowen.bh1xlhw.config.mybatisPlus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * RecoverById :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
public class RecoverById extends AbstractMethod {


    public RecoverById() {
        this(MybatisPlusSqlMethod.RECOVER_BY_ID.getMethod());
    }

    /**
     * @param methodName 方法名
     * @since 3.5.0
     */
    protected RecoverById(String methodName) {
        super(methodName);
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        MybatisPlusSqlMethod sqlMethod = MybatisPlusSqlMethod.LOGIC_DELETE_BY_ID;

        String sql = String.format(sqlMethod.getSql(),
            tableInfo.getTableName(),
            "set delete_time = 0",
            tableInfo.getKeyColumn(),
            ENTITY_DOT + tableInfo.getKeyProperty());
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, methodName, sqlSource);
    }
}
