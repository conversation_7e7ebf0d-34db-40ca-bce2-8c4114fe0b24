package com.miaowen.bh1xlhw.config.mybatisPlus;

import com.baomidou.dynamic.datasource.annotation.DS;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@DS("log")
public class ShardingTableContent {
    @FunctionalInterface
    public interface TableCreator {
        /**
         * 根据表名生成创建表的SQL
         *
         * @param tableName 实际表名
         * @return 创建表的SQL语句
         */
        String createTable(String tableName);
    }

    public static final Map<String, TableCreator> TABLE_CREATE = new HashMap<>();

    static {
        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_log_stripe_webhook",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'pk',\n" +
                "    `merchant_id` varchar(45)      NOT NULL DEFAULT '' COMMENT '商户id',\n" +
                "    `type`        varchar(45)      NOT NULL DEFAULT '' COMMENT '事件类型',\n" +
                "    `time`        int(11)          NOT NULL DEFAULT '0' COMMENT '事件时间',\n" +
                "    `content`     text COMMENT '回调内容json格式',\n" +
                "    `create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                "    `update_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    `delete_time` int(11)          NOT NULL DEFAULT '0',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `time_idx` (`create_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='stripe回调日志表'\n");


        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_log_paypal_webhook",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'pk',\n" +
                "    `merchant_id` varchar(45)      NOT NULL DEFAULT '' COMMENT '商户id',\n" +
                "    `type`        varchar(45)      NOT NULL DEFAULT '' COMMENT '事件类型',\n" +
                "    `time`        varchar(45)      NOT NULL DEFAULT '0' COMMENT '三方事件时间',\n" +
                "    `content`     text COMMENT '回调内容json格式',\n" +
                "    `create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                "    `update_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    `delete_time` int(11)          NOT NULL DEFAULT '0',\n" +
                "    PRIMARY KEY (`id`)\n" +
                ") ENGINE = InnoDB COMMENT ='paypal回调日志表'");

        TABLE_CREATE.put("po_log_alipay_notify",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'pk',\n" +
                "    `merchant_id` varchar(45)      NOT NULL DEFAULT '' COMMENT '商户id',\n" +
                "    `type`        varchar(45)      NOT NULL DEFAULT '' COMMENT '事件类型',\n" +
                "    `time`        varchar(45)      NOT NULL DEFAULT '0' COMMENT '三方事件时间',\n" +
                "    `content`     text COMMENT '回调内容json格式',\n" +
                "    `create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                "    `update_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    `delete_time` int(11)          NOT NULL DEFAULT '0',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `time_idx` (`create_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='alipay回调日志表'\n");

        TABLE_CREATE.put("po_log_wechat_pay_notify",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'pk',\n" +
                "    `merchant_id` varchar(45)      NOT NULL DEFAULT '' COMMENT '商户id',\n" +
                "    `type`        varchar(45)      NOT NULL DEFAULT '' COMMENT '事件类型',\n" +
                "    `time`        varchar(45)      NOT NULL DEFAULT '0' COMMENT '三方事件时间',\n" +
                "    `content`     text COMMENT '回调内容json格式',\n" +
                "    `create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                "    `update_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    `delete_time` int(11)          NOT NULL DEFAULT '0',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `time_idx` (`create_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='微信支付回调日志表'\n");

        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_session_tracking_log",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`                 int(11)      NOT NULL AUTO_INCREMENT,\n" +
                "    `log_id`             varchar(64)  NOT NULL DEFAULT '' COMMENT '日志ID',\n" +
                "    `device_fingerprint` varchar(64)  NOT NULL DEFAULT '' COMMENT '设备指纹信息',\n" +
                "    `order_no`           varchar(64)  NOT NULL DEFAULT '' COMMENT '订单编号',\n" +
                "    `env_type`           int(11)      NOT NULL DEFAULT '0' COMMENT '环境类型',\n" +
                "    `tuid`               varchar(20)  NOT NULL DEFAULT '' COMMENT '推广ID',\n" +
                "    `platform_code`      varchar(20)  NOT NULL DEFAULT '' COMMENT '推广平台',\n" +
                "    `product_id`         int(11)      NOT NULL DEFAULT '0' COMMENT '商品ID',\n" +
                "    `product_name`       varchar(100) NOT NULL DEFAULT '' COMMENT '商品名称',\n" +
                "    `pay_status`         tinyint(4)   NOT NULL DEFAULT '0' COMMENT '支付状态(1:已支付 0:待支付)',\n" +
                "    `pay_amount`         varchar(20)  NOT NULL DEFAULT '' COMMENT '支付金额',\n" +
                "    `total_amount`       varchar(20)  NOT NULL DEFAULT '' COMMENT '总金额',\n" +
                "    `ip`                 varchar(64)  NOT NULL DEFAULT '' COMMENT 'IP地址',\n" +
                "    `ip_address`         varchar(64)  NOT NULL DEFAULT '' COMMENT 'IP地理位置',\n" +
                "    `trace_id`           varchar(200) NOT NULL DEFAULT '' COMMENT '追踪ID',\n" +
                "    `source`             varchar(20)  NOT NULL DEFAULT '' COMMENT '来源标识',\n" +
                "    `create_time`        timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                "    `update_time`        timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n" +
                "    `delete_time`        timestamp    NULL     DEFAULT NULL COMMENT '删除时间',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `idx_log_id` (`log_id`),\n" +
                "    KEY `idx_order_no` (`order_no`),\n" +
                "    KEY `idx_create_time` (`create_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='会话追踪日志表'\n");

        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_exam_log",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`                 int(11)     NOT NULL AUTO_INCREMENT,\n" +
                "    `log_id`             varchar(64) NOT NULL DEFAULT '' COMMENT '日志ID',\n" +
                "    `device_fingerprint` varchar(64) NOT NULL DEFAULT '' COMMENT '设备指纹信息',\n" +
                "    `exam_id`            varchar(64) NOT NULL DEFAULT '' COMMENT '试卷ID',\n" +
                "    `tuid`               varchar(20) NOT NULL DEFAULT '' COMMENT '推广ID',\n" +
                "    `source`             varchar(20) NOT NULL DEFAULT '' COMMENT '来源标识',\n" +
                "    `platform_code`      varchar(20) NOT NULL DEFAULT '' COMMENT '投放平台',\n" +
                "    `order_no`           varchar(64) NOT NULL DEFAULT '' COMMENT '订单编号',\n" +
                "    `product_id`         int(11)     NOT NULL DEFAULT '0' COMMENT '商品ID',\n" +
                "    `product_name`       varchar(64) NOT NULL DEFAULT '' COMMENT '商品名称',\n" +
                "    `questions_num`      int(11)     NOT NULL DEFAULT '0' COMMENT '总题数',\n" +
                "    `exam_num`           int(11)     NOT NULL DEFAULT '0' COMMENT '已答题数',\n" +
                "    `exam_schedule`      varchar(10) NOT NULL DEFAULT '' COMMENT '考试进度',\n" +
                "    `goods_type`         tinyint(4)  NOT NULL DEFAULT '0' COMMENT '1:多语言 2：繁体',\n" +
                "    `pay_status`         tinyint(4)  NOT NULL DEFAULT '0' COMMENT '支付状态(1:已支付 0:待支付)',\n" +
                "    `pay_amount`         varchar(20) NOT NULL DEFAULT '' COMMENT '支付金额',\n" +
                "    `total_amount`       varchar(20) NOT NULL DEFAULT '' COMMENT '总金额',\n" +
                "    `ip`                 varchar(64) NOT NULL DEFAULT '' COMMENT 'IP地址',\n" +
                "    `ip_address`         varchar(64) NOT NULL DEFAULT '' COMMENT 'IP地理位置',\n" +
                "    `create_time`        timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                "    `update_time`        timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n" +
                "    `delete_time`        timestamp   NULL     DEFAULT NULL COMMENT '删除时间',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `idx_log_id` (`log_id`),\n" +
                "    KEY `idx_order_no` (`order_no`),\n" +
                "    KEY `idx_exam_id` (`exam_id`),\n" +
                "    KEY `idx_tuid` (`tuid`),\n" +
                "    KEY `idx_create_time` (`create_time`),\n" +
                "    KEY `idx_log_exam` (`log_id`, `exam_id`)\n" +
                ") ENGINE = InnoDB COMMENT ='做题日志表'\n");

        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_ad_backfill_record",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`            int(11)             NOT NULL AUTO_INCREMENT COMMENT '主键ID',\n" +
                "    `log_id`        varchar(64)         NOT NULL DEFAULT '' COMMENT '日志ID',\n" +
                "    `order_id`      varchar(64)         NOT NULL DEFAULT '' COMMENT '订单ID',\n" +
                "    `order_no`      varchar(64)         NOT NULL DEFAULT '' COMMENT '订单编号',\n" +
                "    `tuid`          varchar(64)         NOT NULL DEFAULT '' COMMENT '推广ID',\n" +
                "    `trace_id`      varchar(64)         NOT NULL DEFAULT '' COMMENT '追踪ID',\n" +
                "    `tg_id`         int(11)             NOT NULL DEFAULT '0' COMMENT '推广表对应ID',\n" +
                "    `product_id`    int(11)             NOT NULL DEFAULT '0' COMMENT '产品ID',\n" +
                "    `product_name`  varchar(20)         NOT NULL DEFAULT '' COMMENT '产品名称',\n" +
                "    `event_type`    varchar(50)         NOT NULL DEFAULT '' COMMENT '事件类型',\n" +
                "    `pay_status`    tinyint(4)          NOT NULL DEFAULT '0' COMMENT '支付状态(0:未支付 1:已支付)',\n" +
                "    `pay_amount`    varchar(20)         NOT NULL DEFAULT '' COMMENT '支付金额',\n" +
                "    `order_time`    timestamp           NULL     DEFAULT NULL COMMENT '订单时间/答题时间',\n" +
                "    `page_type`     tinyint(4)          NOT NULL DEFAULT '0' COMMENT '页面类型',\n" +
                "    `platform_code` varchar(50)         NOT NULL DEFAULT '' COMMENT '平台代码',\n" +
                "    `front_url`     varchar(500)        NOT NULL DEFAULT '' COMMENT '前端URL',\n" +
                "    `ip`            varchar(50)         NOT NULL DEFAULT '' COMMENT 'ip',\n" +
                "    `params`        varchar(500)        NOT NULL DEFAULT '' COMMENT '扩展参数（广告值）',\n" +
                "    `send_status`   varchar(20)         NOT NULL DEFAULT '' COMMENT '已发送/未发送',\n" +
                "    `status`        tinyint(4)          NOT NULL DEFAULT '0' COMMENT '发送状态 1:发送成功 -1:发送失败',\n" +
                "    `create_time`   timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                "    `update_time`   timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n" +
                "    `delete_time`   timestamp           NULL     DEFAULT NULL COMMENT '删除时间',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `idx_log_id` (`log_id`),\n" +
                "    KEY `idx_page_order_trace` (`order_no`, `trace_id`, `page_type`),\n" +
                "    KEY `idx_order_no` (`order_no`),\n" +
                "    KEY `idx_create_time` (`create_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='广告回传记录表'\n");

        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_event_log",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`            int(11)     NOT NULL AUTO_INCREMENT,\n" +
                "    `log_id`        varchar(64) NOT NULL DEFAULT '' COMMENT '日志ID',\n" +
                "    `goods_id`      int(11)     NOT NULL DEFAULT '0' COMMENT '商品id',\n" +
                "    `goods_type`    tinyint(4)  NOT NULL DEFAULT '0' COMMENT '商品类型,1:多语言,2:繁体',\n" +
                "    `tgid`          varchar(20) NOT NULL DEFAULT '' COMMENT '推广id',\n" +
                "    `source`        varchar(20) NOT NULL DEFAULT '' COMMENT '来源标识',\n" +
                "    `event_code`    varchar(64) NOT NULL DEFAULT '' COMMENT '事件code',\n" +
                "    `event_name`    varchar(64) NOT NULL DEFAULT '' COMMENT '事件名称',\n" +
                "    `platform_code` varchar(64) NOT NULL DEFAULT '' COMMENT '推广平台',\n" +
                "    `create_time`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                "    `update_time`   timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n" +
                "    `delete_time`   timestamp   NULL     DEFAULT NULL COMMENT '删除时间',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `idx_log_id` (`log_id`),\n" +
                "    KEY `idx_create_time` (`create_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='事件日志表'\n" +
                "\n");


        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_email_order_success",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`            int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'pk',\n" +
                "    `order_id`      int(20) unsigned NOT NULL DEFAULT '0' COMMENT '订单表id',\n" +
                "    `out_trade_no`  varchar(50)      NOT NULL DEFAULT '' COMMENT '订单号',\n" +
                "    `payment_time`  timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '支付时间',\n" +
                "    `language_code` varchar(10)      NOT NULL DEFAULT 'en' COMMENT '语言代码',\n" +
                "    `goods_id`      int(11)          NOT NULL DEFAULT '0' COMMENT '商品id',\n" +
                "    `goods_type`    tinyint(4)       NOT NULL DEFAULT '1' COMMENT '商品类型1:多语言,2:繁体',\n" +
                "    `domain`        varchar(254)     NOT NULL DEFAULT '' COMMENT '域名',\n" +
                "    `email`         varchar(254)     NOT NULL DEFAULT '' COMMENT '用户填写的邮箱',\n" +
                "    `third_email`   varchar(254)     NOT NULL DEFAULT '' COMMENT '三方订单提供的邮箱',\n" +
                "    `create_time`   timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                "    `update_time`   timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    `delete_time`   int(11)          NOT NULL DEFAULT '0',\n" +
                "    PRIMARY KEY (`id`),\n" +
                "    KEY `email_idx` (`email`),\n" +
                "    KEY `third_email_idx` (`third_email`),\n" +
                "    KEY `time_idx` (`payment_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='已支付邮箱表'\n");

        TABLE_CREATE.put("po_email_order_unpaid",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`                int(11)          NOT NULL AUTO_INCREMENT COMMENT '主键ID',\n" +
                "    `email`             varchar(20)      NOT NULL DEFAULT '' COMMENT '邮箱',\n" +
                "    `order_create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',\n" +
                "    `order_id`          int(20) unsigned NOT NULL DEFAULT '0' COMMENT '订单id',\n" +
                "    `out_trade_no`      varchar(64)      NOT NULL DEFAULT '' COMMENT '订单号',\n" +
                "    `goods_id`          int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',\n" +
                "    `goods_type`        int(11)          NOT NULL DEFAULT '1' COMMENT '商品类型,1:多语言,2:繁体',\n" +
                "    `domain`            varchar(255)     NOT NULL DEFAULT '' COMMENT '域名',\n" +
                "    `language_code`     varchar(10)      NOT NULL DEFAULT 'en' COMMENT '语言代码',\n" +
                "    `create_time`       timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                "    `update_time`       timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',\n" +
                "    `delete_time`       int(11)          NOT NULL DEFAULT '0' COMMENT '删除时间',\n" +
                "    PRIMARY KEY (`id`) USING BTREE,\n" +
                "    KEY `out_trade_no_idx` (`out_trade_no`) USING BTREE,\n" +
                "    KEY `time_idx` (`order_create_time`),\n" +
                "    KEY `order_id_idx` (`order_id`)\n" +
                ") ENGINE = InnoDB COMMENT ='未支付邮件发送记录表'\n");

        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_email_promotion_send_record",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`                          int(20) unsigned    NOT NULL AUTO_INCREMENT COMMENT '主键',\n" +
                "    `title`                       varchar(100)        NOT NULL DEFAULT '' COMMENT '邮件标题',\n" +
                "    `pro_obj`                     tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '推送对象1:已支付邮箱  2：未支付邮箱',\n" +
                "    `name`                        varchar(20)         NOT NULL DEFAULT '' COMMENT '模板名称',\n" +
                "    `email_promotion_template_id` int(11)             NOT NULL DEFAULT '0' COMMENT '推广模板id',\n" +
                "    `total_num`                   int(11)             NOT NULL DEFAULT '0' COMMENT '推送总量',\n" +
                "    `success_num`                 int(11)             NOT NULL DEFAULT '0' COMMENT '成功数量',\n" +
                "    `fail_num`                    int(11)             NOT NULL DEFAULT '0' COMMENT '失败数量',\n" +
                "    `pro_person`                  varchar(20)         NOT NULL DEFAULT '' COMMENT '推送人',\n" +
                "    `create_time`                 timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                "    `update_time`                 timestamp           NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    `delete_time`                 int(11)             NOT NULL DEFAULT '0',\n" +
                "    PRIMARY KEY (`id`) USING BTREE\n" +
                ") ENGINE = InnoDB COMMENT ='推广邮件记录'\n" +
                "\n");

        //表名用``包裹避免sql注入
        TABLE_CREATE.put("po_email_send_record",
            tableName -> "CREATE TABLE IF NOT EXISTS `" + tableName + "`\n" +
                "(\n" +
                "    `id`                int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'pk',\n" +
                "    `title`             varchar(100)     NOT NULL DEFAULT '' COMMENT '邮件标题',\n" +
                "    `content`           text COMMENT '邮件内容',\n" +
                "    `email_template_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '邮件模板id',\n" +
                "    `send_status`       tinyint(4)       NOT NULL DEFAULT '0' COMMENT '发送状态1：成功，0失败',\n" +
                "    `email`             varchar(254)     NOT NULL DEFAULT '' COMMENT '收件人邮箱',\n" +
                "    `error_info`        text COMMENT '错误信息',\n" +
                "    `language_code`     varchar(10)      NOT NULL DEFAULT '' COMMENT '语言编码',\n" +
                "    `order_id`          int(20)          NOT NULL DEFAULT '0' COMMENT '订单id',\n" +
                "    `out_trade_no`      varchar(55)      NOT NULL DEFAULT '' COMMENT '订单号',\n" +
                "    `order_create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',\n" +
                "    `click_time`        timestamp        NULL     DEFAULT NULL COMMENT '点击时间',\n" +
                "    `create_time`       timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,\n" +
                "    `update_time`       timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    `delete_time`       int(11)          NOT NULL DEFAULT '0',\n" +
                "    PRIMARY KEY (`id`) USING BTREE,\n" +
                "    KEY `order_index` (`order_id`),\n" +
                "    KEY `idx_email` (`email`),\n" +
                "    KEY `out_trande_no_idx` (`out_trade_no`),\n" +
                "    KEY `order_time_idx` (`order_create_time`)\n" +
                ") ENGINE = InnoDB COMMENT ='通知邮件记录'");

    }

}
