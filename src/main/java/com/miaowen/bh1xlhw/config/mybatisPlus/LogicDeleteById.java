package com.miaowen.bh1xlhw.config.mybatisPlus;

import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;

/**
 * 通过主键 逻辑删除
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public class LogicDeleteById extends AbstractMethod {


    public LogicDeleteById() {
        this(MybatisPlusSqlMethod.LOGIC_DELETE_BY_ID.getMethod());
    }

    /**
     * @param name 方法名
     * @since 3.5.0
     */
    public LogicDeleteById(String name) {
        super(name);
    }

    @Override
    public MappedStatement injectMappedStatement(Class<?> mapperClass, Class<?> modelClass, TableInfo tableInfo) {
        MybatisPlusSqlMethod sqlMethod = MybatisPlusSqlMethod.LOGIC_DELETE_BY_ID;

        String sql = String.format(sqlMethod.getSql(),
            tableInfo.getTableName(),
            "set delete_time = UNIX_TIMESTAMP()",
            tableInfo.getKeyColumn(),
            ENTITY_DOT + tableInfo.getKeyProperty(),
            "and delete_time = 0");
        SqlSource sqlSource = languageDriver.createSqlSource(configuration, sql, modelClass);
        return this.addUpdateMappedStatement(mapperClass, modelClass, methodName, sqlSource);
    }
}
