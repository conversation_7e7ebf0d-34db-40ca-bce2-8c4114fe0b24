package com.miaowen.bh1xlhw.config.security;

import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.ObjectPostProcessor;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.access.intercept.FilterSecurityInterceptor;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.NullSecurityContextRepository;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.Collections;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Configuration
@AllArgsConstructor
public class WebSecurityConfigurer extends WebSecurityConfigurerAdapter {
    private final JwtAuthenticationTokenFilter jwtAuthenticationTokenFilter;
    private final WebLogoutSuccessHandler webLogoutSuccessHandler;
    private final AuthenticationSecurityConfig authenticationSecurityConfig;
    private final WebLogoutHandler webLogoutHandler;
    private final AccessDecisionManager accessDecisionManager;

    private final AccessDeniedHandler accessDeniedHandler;

    @Override
    public void configure(HttpSecurity http) throws Exception {
        http
                // 配置权限规则（修复顺序）
                .authorizeRequests()
                .antMatchers(
                        "/api/verify",
                        "/verify",
                        "/accPermission/clear",
                        "/api/accPermission/clear",
                        "/api/accUser/getMyInfo",
                        "/accUser/getMyInfo",
                        "/oauth/logout",
                        "/api/adminLogin",
                        "/public/oa/**",
                        "/api/public/oa/**",
                        "/accUser/getMyMenu",
                        "/refreshToken",
                        "/adminLogin",
                        "/folder/listAll",
                        "/file/list",
                        "/file/upload",
                        "/dictionary/type/list",
                        "/language/listAll",
                        "/currency/listAll",
                        "/country/listAll",
                        "/merchant/listAll",
                        "/operation/platform/all",
                        "/operation/agent/all",
                        "/operation/user/manager/all",
                        "/operation/user/all",
                        "/operation/domain/all",
                        "/goods/multilingual/all",
                        "/good/type/multilingual/all",
                        "/accUser/updatePassword",
                        "/goods/category/traditional/all",
                        "/goods/category/multilingual/all",
                        "/goods/traditional/all"
                ).permitAll()
                .antMatchers(HttpMethod.OPTIONS, "/**").permitAll()
                //accessDecisionManager 覆盖了默认权限控制
                //在 .anyRequest().authenticated().accessDecisionManager(...) 的链式调用中，accessDecisionManager 会强制所有请求（包括白名单）经过自定义鉴权逻辑。
                .anyRequest().authenticated().accessDecisionManager(accessDecisionManager)
                .and().authorizeRequests()
                .and()
                // 其他配置保持不变
                .logout()
                .addLogoutHandler(webLogoutHandler)
                .logoutSuccessHandler(webLogoutSuccessHandler)
                .and()
                .exceptionHandling()
                .accessDeniedHandler(accessDeniedHandler)
                .and()
                .apply(authenticationSecurityConfig)
                .and()
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                .and()
                .requestCache().disable()
                .securityContext().securityContextRepository(new NullSecurityContextRepository())
                .and()
                .cors().configurationSource(corsConfigurationSource())
                .and()
                .csrf().disable()
                // 添加JWT过滤器
                .addFilterBefore(jwtAuthenticationTokenFilter, UsernamePasswordAuthenticationFilter.class);
    }

    @Override
    @Bean
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(BCryptPasswordEncoder.BCryptVersion.$2Y);
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        // 使用模式匹配替代通配符
        configuration.setAllowedOrigins(Collections.singletonList("*"));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS"));
        configuration.setAllowedHeaders(Collections.singletonList("*"));
        configuration.setAllowCredentials(false);
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
}
