package com.miaowen.bh1xlhw.config.security;


import com.miaowen.bh1xlhw.properties.AdminLoginProperties;
import com.miaowen.bh1xlhw.service.auth.UsernameAuthenticationProvider;
import lombok.AllArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.stereotype.Component;

/**
 * 登录的相关处理配置
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Component
@AllArgsConstructor
public class AuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {
    private final StringRedisTemplate stringRedisTemplate;
    private final UserDetailsService userDetailsService;
    private final AdminLoginProperties adminLoginProperties;


    @Override
    public void configure(HttpSecurity http) {
        UsernameAuthenticationProvider usernameAuthenticationProvider = new UsernameAuthenticationProvider(userDetailsService, stringRedisTemplate, adminLoginProperties);
        http.authenticationProvider(usernameAuthenticationProvider);
    }
}
