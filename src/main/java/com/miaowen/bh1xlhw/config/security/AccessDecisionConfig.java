package com.miaowen.bh1xlhw.config.security;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.AccessDecisionManager;
import org.springframework.security.access.vote.AffirmativeBased;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;

import java.util.Collections;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class AccessDecisionConfig {

    @Bean
    public AccessDecisionManager accessDecisionManager() {
        return new AffirmativeBased(Collections.singletonList(new UrlAccessDecisionVoter()));
    }
}
