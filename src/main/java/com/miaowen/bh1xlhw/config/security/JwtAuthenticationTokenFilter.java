package com.miaowen.bh1xlhw.config.security;


import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.AccPermissionMapper;
import com.miaowen.bh1xlhw.model.entity.AccRole;
import com.miaowen.bh1xlhw.model.entity.AccUserRole;
import com.miaowen.bh1xlhw.model.vo.system.PermissionVO;
import com.miaowen.bh1xlhw.model.vo.system.UserPermissionVO;
import com.miaowen.bh1xlhw.repository.AccRoleService;
import com.miaowen.bh1xlhw.repository.AccUserRoleService;
import com.miaowen.bh1xlhw.repository.AccUserService;
import com.miaowen.bh1xlhw.service.system.SystemService;
import com.miaowen.bh1xlhw.utils.WriteJsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.annotation.Resource;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.common.vo.ResultEnum.TOKEN_EXPIRE;
import static com.miaowen.bh1xlhw.common.vo.ResultEnum.TOKEN_INVALID;


/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@Component("jwtAuthenticationTokenFilter")
@AllArgsConstructor
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private final JwtTokenConfig jwtTokenConfig;
    public final static String HEADER = "CookieToken";

    public static final ConcurrentHashMap<Integer, List<SimpleGrantedAuthority>> cache = new ConcurrentHashMap<>();

    @Resource
    private final AccRoleService accRoleService;

    @Resource
    private final AccUserRoleService accUserRoleService;

    @Resource
    private final SystemService systemService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws ServletException, IOException {
        log.debug("Start resolving the token.................");
        if ("OPTIONS".equals(request.getMethod())) {
            chain.doFilter(request, response);
            return;
        }

        String base64Token = request.getHeader(HEADER);
        //请求头没有就从请求参数拿
        if (!StringUtils.hasText(base64Token)){
            base64Token = request.getParameter(HEADER);
        }
        //从请求头中获取token
        log.debug("request getMethod :{}", request.getMethod());
        //Token不存在,或为空,
        if (!StringUtils.hasText(base64Token)) {
            chain.doFilter(request, response);
            return;
        }
        String token = new String(Base64.getUrlDecoder().decode(base64Token));
        
        //Token是否有效
        if (!isTokenValid(token)) {
            WriteJsonUtil.writeJson(request,response, ResultVO.fail(TOKEN_INVALID));
            return;
        }
        //从token中获取username
        Long id = getUserIdFromToken(token);

        //校验redis中user是否过期
        if (isTokenExpired(id)) {
            WriteJsonUtil.writeJson(request,response, ResultVO.fail(TOKEN_EXPIRE));
            return;
        }

        //设置认证信息
        saveUserDetails(token);

        chain.doFilter(request, response);
        log.debug("End resolving the token.................");
    }

    /**
     * 刷新redis中的token
     *
     * @param id    用户名
     * @param token token
     */
    private void refreshToken(Long id, String token) {
        jwtTokenConfig.refreshToken(id, token);
    }

    /**
     * 将用户信息存到内存中
     */
    private void saveUserDetails(String token) {
        OAuthUser oAuthUser = jwtTokenConfig.getInfoFromToken(token);
        // 将用户信息存入 authentication,这里只存了username,token中不保存密码
        UsernamePasswordAuthenticationToken authentication =
            new UsernamePasswordAuthenticationToken(
                oAuthUser,
                null,
                oAuthUser.getAuthorities()
            );
        if (!cache.containsKey(oAuthUser.getUserId())) {
            cache.put(oAuthUser.getUserId(), getAuthorities(oAuthUser.getUserId()));
        }
        authentication.setDetails(oAuthUser);
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }



    List<SimpleGrantedAuthority> getAuthorities(Integer userId) {
        List<AccUserRole> userRoles = accUserRoleService.getRolesByUserId(userId);
        List<Integer> roleIds = userRoles.stream().map(AccUserRole::getRoleId).collect(Collectors.toList());
        List<AccRole> roles = accRoleService.getRolesByIds(roleIds);
        if (CollectionUtils.isEmpty(roles)) {
            return Collections.emptyList();
        }


        // 3. 判断是否包含超级管理员角色（使用Stream优化循环）
        boolean isSuperAdmin = roles.stream()
                .anyMatch(role -> BooleanEnum.TRUE.getValue().equals(role.getIsSuperAdmin()));

        Set<String> permissions = new HashSet<>();
        List<SimpleGrantedAuthority> simpleGrantedAuthorities = new ArrayList<>();
        if (!isSuperAdmin) {
            UserPermissionVO myMenu = systemService.getMyMenu(userId);
            permissions.addAll(myMenu.getButtonList().stream().filter(Objects::nonNull)
                    .map(PermissionVO::getApiUrl)
                    .filter(StringUtils::hasText).collect(Collectors.toSet()));

            permissions.addAll(myMenu.getMenuList().stream().filter(Objects::nonNull)
                    .map(PermissionVO::getApiUrl)
                    .filter(StringUtils::hasText).collect(Collectors.toSet()));

        }else {
            permissions.add(SystemConstant.SUPER_ADMIN);
        }
        permissions.forEach(apiUrl -> simpleGrantedAuthorities.add(new SimpleGrantedAuthority(apiUrl)));
        return simpleGrantedAuthorities;
    }


    /**
     * 账号是否已在别处登录
     * 在别处登陆过返回true
     *
     * @param id    用户名 token
     * @param token
     */
    private boolean isLoginElsewhere(Long id, String token) {
        return !jwtTokenConfig.exist(id, token);
    }

    /**
     * 从token中获取username
     *
     * @param token
     * @return:
     */
    private Long getUserIdFromToken(String token) {
        return jwtTokenConfig.getUserIdFromToken(token);
    }

    /**
     * 检验token是否过期
     * 过期返回true
     *
     * @param id
     * @return:
     */
    private boolean isTokenExpired(Long id) {
        return jwtTokenConfig.isTokenExpired(id);
    }

    /**
     * 检验token是否有效
     * 有效返回true
     *
     * @param token
     * @return:
     */
    private boolean isTokenValid(String token) {
        return jwtTokenConfig.validateToken(token);
    }

}
