package com.miaowen.bh1xlhw.config.security;

import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.AccessDecisionVoter;
import org.springframework.security.access.ConfigAttribute;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.web.FilterInvocation;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@Component
public class UrlAccessDecisionVoter implements AccessDecisionVoter<FilterInvocation> {
    // 日志唯一标识
    public static final String LOG_PREFIX = "[UrlVoter]";

    private final AntPathMatcher antPathMatcher = new AntPathMatcher();

    @Override
    public boolean supports(ConfigAttribute attribute) {
        return true;
    }

    @Override
    public boolean supports(Class<?> clazz) {
        return FilterInvocation.class.isAssignableFrom(clazz);
    }

    @Override
    public int vote(Authentication authentication, FilterInvocation fi, Collection<ConfigAttribute> attributes) {
        //1. 获取当前请求的URL和方法
        String requestUri = fi.getRequest().getServletPath();
        String methodType = fi.getRequest().getMethod();
        // 请求唯一标识
        String requestId = methodType + " " + requestUri;

        log.debug("{} Start voting for: {}", LOG_PREFIX, requestId);

        // 遍历所有的ConfigAttribute，检查是否有permitAll标记
        for (ConfigAttribute attr : attributes) {
            // 如果找到permitAll标记，则允许访问
            if (SystemConstant.PERMIT_ATTRIBUTE.equals(attr.toString())) {
                log.debug("{} PermitAll granted for: {}", LOG_PREFIX, requestId);
                return ACCESS_GRANTED;
            }
        }

        // 2. 认证信息类型检查
        if (!(authentication.getPrincipal() instanceof OAuthUser)) {
            log.error("{} Unexpected principal type: {} for {}", LOG_PREFIX,
                    authentication.getPrincipal().getClass().getName(), requestId);
            return ACCESS_DENIED;
        }
        OAuthUser user = (OAuthUser) authentication.getPrincipal();
        Integer userId = user.getUserId();
        log.debug("{} Processing user: {}", LOG_PREFIX, userId);

        // 3. 权限缓存检查
        List<SimpleGrantedAuthority> list = JwtAuthenticationTokenFilter.cache.getOrDefault(userId, new ArrayList<>());
        if (CollectionUtils.isEmpty(list)) {
            log.error("{} EMPTY permissions for user: {} accessing {}", LOG_PREFIX, userId, requestId);
            return ACCESS_DENIED;
        }

        // 4. 权限匹配流程
        log.debug("{} Checking {} permissions for: {}", LOG_PREFIX, list.size(), userId);
        for (SimpleGrantedAuthority authority : list) {
            String authString = authority.getAuthority();

            // 4.1 超级管理员检查
            if (SystemConstant.SUPER_ADMIN.equals(authString)) {
                log.debug("{} SuperAdmin access granted for: {}", LOG_PREFIX, requestId);
                return ACCESS_GRANTED;
            }

            // 4.2 权限格式验证
            String[] parts = authString.split(":");
            if (parts.length < 2) {
                log.error("{} INVALID permission format: '{}' for user: {}", LOG_PREFIX, authString, userId);
                continue;
            }

            // 提取方法和URL部分（支持包含冒号的URL）
            String cacheMethod = parts[0];
            String fullUrl = authString.substring(cacheMethod.length() + 1);

            // 处理完整URL（提取路径部分）
            String cacheUrl = extractPathFromUrl(fullUrl);
            if (cacheUrl == null) {
                log.error("{} INVALID URL format: '{}' for user: {}", LOG_PREFIX, fullUrl, userId);
                continue;
            }

            // 4.3 权限匹配检查
            boolean urlMatch = antPathMatcher.match(cacheUrl, requestUri);
            boolean methodMatch = "any".equalsIgnoreCase(cacheMethod) || cacheMethod.equalsIgnoreCase(methodType);

            log.debug("{} Matching: [{}] vs [{}] -> URL:{} Method:{}", LOG_PREFIX,
                    cacheMethod + ":" + cacheUrl, requestId, urlMatch, methodMatch);

            if (urlMatch && methodMatch) {
                log.debug("{} Permission granted by: '{}'", LOG_PREFIX, authString);
                return ACCESS_GRANTED;
            }
        }

        // 5. 拒绝访问日志
        log.debug("{} ACCESS DENIED. User:{} has no permission for: {}", LOG_PREFIX, userId, requestId);
        return ACCESS_DENIED;
    }

    /**
     * 从完整URL中提取路径部分
     *
     * @param fullUrl 完整URL（可能包含协议和域名）
     * @return 路径部分（如/api/accUser/getMyInfo），或原始URL（如果不是http/https）
     */
    public static String extractPathFromUrl(String fullUrl) {
        try {
            // 仅处理http/https协议的URL
            if (fullUrl.startsWith("http://") || fullUrl.startsWith("https://")) {
                URI uri = new URI(fullUrl);
                String path = uri.getPath();
                return StringUtils.hasText(path) ? path : "/";
            }
            // 非http/https URL直接返回
            return fullUrl;
        } catch (URISyntaxException e) {
            log.error("{} URL parsing error: {}", LOG_PREFIX, fullUrl, e);
            return null;
        }
    }

}
