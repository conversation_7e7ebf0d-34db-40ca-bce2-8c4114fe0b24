package com.miaowen.bh1xlhw.config.security;


import com.alibaba.fastjson.JSON;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Component
public class WebLogoutSuccessHandler implements LogoutSuccessHandler {
    @Override
    public void onLogoutSuccess(HttpServletRequest request,
                                HttpServletResponse response,
                                Authentication authentication) throws IOException {
        PrintWriter out = response.getWriter();
        out.write(JSON.toJSONString(ResultVO.success("退出成功")));
    }
}