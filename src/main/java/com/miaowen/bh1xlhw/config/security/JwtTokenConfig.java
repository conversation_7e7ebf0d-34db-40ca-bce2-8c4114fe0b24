package com.miaowen.bh1xlhw.config.security;


import com.alibaba.fastjson2.JSONObject;
import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.model.vo.login.TokenVO;
import com.miaowen.bh1xlhw.properties.AdminLoginProperties;
import com.miaowen.bh1xlhw.utils.GsonUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.SecretKey;
import java.io.EOFException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * JWT生成令牌、验证令牌、获取令牌
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */

@Component
@Slf4j
public class JwtTokenConfig {
    private final AdminLoginProperties adminLoginProperties;
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 私钥
     **/
    private final SecretKey SECRET_KEY;

    /**
     * 业务名--redis中key前缀
     **/
    public static final String KEY_PRE = "authentication";
    public JwtTokenConfig(StringRedisTemplate stringRedisTemplate, AdminLoginProperties adminLoginProperties) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.adminLoginProperties = adminLoginProperties;
        this.SECRET_KEY = Keys.hmacShaKeyFor(adminLoginProperties.getSecretKey().getBytes(StandardCharsets.UTF_8));
    }


    /**
     * 生成令牌
     *
     * @param userDetails 用户
     * @return 令牌
     */
    public TokenVO generateToken(OAuthUser userDetails) {
        Map<String, Object> claims = new HashMap<>(2);
        claims.put(Claims.SUBJECT, userDetails.getUserId());
        claims.put(Claims.ISSUED_AT, new Date().toString());
        claims.put("info", JSONObject.toJSONString(userDetails));
        return generateToken(claims, userDetails);
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public Long getUserIdFromToken(String token) {
        Long id = null;
        try {
            Claims claims = getClaimsFromToken(token);
            id = Long.parseLong(claims.getSubject());
        } catch (Exception e) {
            log.info("令牌中获取用户id，token解析错误:{}", e.getMessage());
        }
        return id;
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token 令牌
     * @return 用户名
     */
    public OAuthUser getInfoFromToken(String token) {
        OAuthUser oAuthUser = null;
        try {
            Claims claims = getClaimsFromToken(token);
            oAuthUser = GsonUtil.gsonToBean(claims.get("info", String.class), OAuthUser.class);
        } catch (Exception e) {
            log.info("令牌中获取用户信息，token解析错误:{}", e.getMessage());
        }
        return oAuthUser;
    }

    /**
     * 判断令牌是否过期
     *
     * @param id 令牌
     * @return 是否过期
     */
    public boolean isTokenExpired(Long id) {
        //从redis中判断id是否过期
        String key = KEY_PRE + ":" + id;
        Long expire = stringRedisTemplate.getExpire(key);
        return expire <= 0;
    }

    /**
     * 更新redis中令牌时间
     *
     * @param id    id
     * @param token 令牌
     */
    public void refreshToken(Long id, String token) {
        ValueOperations<String, String> valueOperations = stringRedisTemplate.opsForValue();
        String key = KEY_PRE + ":" + id;
        valueOperations.set(key, token, adminLoginProperties.getTokenExp(), TimeUnit.SECONDS);
        valueOperations.set(token, id.toString(), adminLoginProperties.getTokenExp(), TimeUnit.SECONDS);
    }



    /**
     * 验证令牌
     *
     * @param token 令牌
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            getClaimsFromToken(token);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims      数据声明
     * @param userDetails
     * @return 令牌
     */
    private TokenVO generateToken(Map<String, Object> claims, OAuthUser userDetails) {
        long currentTimeMillis = System.currentTimeMillis();
        Date expirationDate = new Date(currentTimeMillis + adminLoginProperties.getTokenValidExp() * 1000);
        String token = Jwts.builder()
            .setClaims(claims)
            .setExpiration(expirationDate)
            .signWith(SECRET_KEY)
            .compact();
        //用户id作为key,token作为value存入redis
        ValueOperations<String, String> valueOperations = stringRedisTemplate.opsForValue();
        String key = KEY_PRE + ":" + claims.get(Claims.SUBJECT);
        valueOperations.set(key, token, adminLoginProperties.getTokenExp(), TimeUnit.MINUTES);
        valueOperations.set(token, userDetails.getUserId().toString(), adminLoginProperties.getTokenExp(), TimeUnit.MINUTES);
        TokenVO tokenVO = new TokenVO();
        tokenVO.setToken(token);
        tokenVO.setExpiration((int)(currentTimeMillis / 1000) + adminLoginProperties.getTokenValidExp());
        return tokenVO;
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims getClaimsFromToken(String token) {
        Claims claims;
        claims = Jwts.parserBuilder()
            .setSigningKey(SECRET_KEY)
            .build()
            .parseClaimsJws(token)
            .getBody();
        return claims;
    }

    /**
     * id是否存在
     *
     * @param id
     * @param token
     * @return: boolean
     **/
    public boolean exist(Long id, String token) {
        String key = KEY_PRE + ":" + id;
        String redisToken = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.hasText(redisToken)) {
            return redisToken.equals(token);
        }
        return false;
    }
}
