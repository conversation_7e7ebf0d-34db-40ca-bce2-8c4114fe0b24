package com.miaowen.bh1xlhw.config.security.login;

/**
 * 
 * MobileAuthenticationToken : 复制的UsernamePasswordAuthenticationToken,手机验证码可类比账号密码
 * 
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2021-11-22
 */
/*
 * Copyright 2004, 2005, 2006 Acegi Technology Pty Limited
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.SpringSecurityCoreVersion;
import org.springframework.util.Assert;

import java.util.Collection;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public class UsernameAuthenticationToken extends AbstractAuthenticationToken {

    private static final long serialVersionUID = SpringSecurityCoreVersion.SERIAL_VERSION_UID;
    private String username;
    private String password;
    private String verification;
    private String verificationUuid;


    /**
     * This constructor can be safely used by any code that wishes to create a
     * <code>UsernamePasswordAuthenticationToken</code>, as the {@link #isAuthenticated()}
     * will return <code>false</code>.
     */
    public UsernameAuthenticationToken(String username, String password, String verification, String verificationUuid) {
        super(null);
        this.username = username;
        this.password = password;
        this.verification = verification;
        this.verificationUuid = verificationUuid;
        setAuthenticated(false);
    }

    /**
     * This constructor should only be used by <code>AuthenticationManager</code> or
     * <code>AuthenticationProvider</code> implementations that are satisfied with
     * producing a trusted (i.e. {@link #isAuthenticated()} = <code>true</code>)
     * authentication token.
     *
     * @param principal   用户信息，登陆时只有手机号，登录成功后被Spring Security内部通过配置的MobileUserDetailsService中的loadUserByMobile
     *                    设置成UserDetails对象
     * @param credentials 验证码
     * @param authorities 权限
     */
    public UsernameAuthenticationToken(String principal, Object credentials,
                                       Collection<? extends GrantedAuthority> authorities, String username, String password, String verification, String verificationUuid) {
        super(authorities);
        this.username = username;
        this.password = password;
        this.verification = verification;
        this.verificationUuid = verificationUuid;
        // must use super, as we override
        super.setAuthenticated(true);
    }

    public UsernameAuthenticationToken(Collection<? extends GrantedAuthority> authorities) {
        super(authorities);
    }

    @Override
    public boolean isAuthenticated() {
        return true;
    }

    public String getVerificationUuid() {
        return verificationUuid;
    }

    @Override
    public Object getCredentials() {
        return null;
    }

    @Override
    public Object getPrincipal() {
        return this.username;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public String getVerification() {
        return verification;
    }

    @Override
    public void setAuthenticated(boolean isAuthenticated) throws IllegalArgumentException {
        Assert.isTrue(!isAuthenticated,
            "Cannot set this token to trusted - use constructor which takes a GrantedAuthority list instead");
        super.setAuthenticated(false);
    }

    @Override
    public void eraseCredentials() {
        super.eraseCredentials();
    }

}
