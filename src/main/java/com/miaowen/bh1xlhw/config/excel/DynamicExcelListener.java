package com.miaowen.bh1xlhw.config.excel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.util.ListUtils;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

public class DynamicExcelListener extends AnalysisEventListener<Map<Integer, String>> {

    // 表头映射（列索引 -> 表头名称）
    private Map<Integer, String> headerMap;

    // 存储解析结果
    private final List<Map<String, String>> data = ListUtils.newArrayList();

    // 表头行索引（通常为0）
    private int headerRowIndex = 0;

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 保存表头映射
        this.headerMap = headMap;
        this.headerRowIndex = context.readRowHolder().getRowIndex();
    }

    @Override
    public void invoke(Map<Integer, String> rowData, AnalysisContext context) {
        // 获取当前行索引
        int currentRowIndex = context.readRowHolder().getRowIndex();

        // 跳过表头行（如果表头行被重复解析）
        if (currentRowIndex == headerRowIndex) {
            return;
        }

        // 创建新行数据映射（保持列顺序）
        Map<String, String> rowMap = new LinkedHashMap<>();

        // 处理所有列（包括空单元格）
        for (Map.Entry<Integer, String> header : headerMap.entrySet()) {
            int colIndex = header.getKey();
            String headerName = header.getValue();

            // 如果该列在行数据中存在，则取值；否则设为空字符串
            String value = rowData.containsKey(colIndex) ?
                    rowData.get(colIndex) :
                    "";

            rowMap.put(headerName, value);
        }

        data.add(rowMap);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理只有表头行的情况
        if (data.isEmpty() && headerMap != null && !headerMap.isEmpty()) {
            // 创建一个空数据行
            Map<String, String> emptyRow = new LinkedHashMap<>();
            for (String header : headerMap.values()) {
                emptyRow.put(header, "");
            }
            data.add(emptyRow);
        }
    }

    public List<Map<String, String>> getData() {
        return data;
    }
}