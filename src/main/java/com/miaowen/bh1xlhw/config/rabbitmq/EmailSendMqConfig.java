package com.miaowen.bh1xlhw.config.rabbitmq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.CustomExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * Order2HUnPaidConfig :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-16
 */
@Configuration
public class EmailSendMqConfig {

    public final static String QUEUE = "promotion.email.delayed.queue";
    public final static String EXCHANGE = "promotion.email.delayed.exchange";
    public final static String KEY = "promotion.email.delay.key";

    /**
     * 延时邮件交换器（x-delayed-message类型）
     * 需要确保RabbitMQ已安装延迟消息插件：
     * 1. <a href="https://github.com/rabbitmq/rabbitmq-delayed-message-exchange">下载插件</a>
     * 2. 复制到插件目录：$RABBITMQ_HOME/plugins/
     * 3. 启用插件：rabbitmq-plugins enable rabbitmq_delayed_message_exchange
     */
    @Bean
    public CustomExchange delayedEmailExchange() {
        // 定义底层路由类型
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(EXCHANGE, "x-delayed-message", true, false, args);
    }

    /**
     * 延时邮件队列（持久化）
     */
    @Bean
    public Queue delayedEmailQueue() {
        // 惰性队列（适合高延迟场景）
        return new Queue(QUEUE,
            true, false, false,
            new HashMap<String, Object>() {{
                put("x-queue-mode", "lazy");
            }});
    }

    /**
     * 绑定延时队列到交换器
     */
    @Bean
    public Binding delayedEmailBinding() {
        return BindingBuilder.bind(delayedEmailQueue())
            .to(delayedEmailExchange())
            .with(KEY)
            .noargs();
    }


}
