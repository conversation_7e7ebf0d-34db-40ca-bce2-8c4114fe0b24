package com.miaowen.bh1xlhw.config.rabbitmq;

import org.springframework.amqp.core.*;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName RabbitMQConfig
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/13 9:24
 */
//@Configuration
public class RabbitMQConfig {

    /*
    在Spring Boot集成RabbitMQ时，队列必须显式声明，否则消息将无法被正确路由和消费。以下是针对策略模式代码的队列声明补充方案

    一、队列未声明的风险
        消息丢失：交换机无法将消息路由到不存在的队列

        消费者失效：消费者监听的队列不存在时自动创建临时队列（非持久化）

        管理混乱：无法通过管理界面监控队列状态
    */



    // JSON消息转换器
    @Bean
    public MessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }




    /**
     * 延时邮件交换器（x-delayed-message类型）
     * 需要确保RabbitMQ已安装延迟消息插件：
     * 1. 下载插件：https://github.com/rabbitmq/rabbitmq-delayed-message-exchange
     * 2. 复制到插件目录：$RABBITMQ_HOME/plugins/
     * 3. 启用插件：rabbitmq-plugins enable rabbitmq_delayed_message_exchange
     */
    @Bean
    public CustomExchange delayedEmailExchange() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");  // 定义底层路由类型

        return new CustomExchange(
                "email.delayed.exchange",
                "x-delayed-message",  // 固定类型标识
                true,    // 持久化
                false,   // 不自动删除
                args
        );
    }

    /**
     * 延时邮件队列（持久化）
     */
    @Bean
    public Queue delayedEmailQueue() {
        return new Queue("email.delayed.queue",
                true,   // 持久化
                false,   // 非排他
                false,   // 不自动删除
                new HashMap<String, Object>() {{
                    put("x-queue-mode", "lazy");  // 惰性队列（适合高延迟场景）
                }});
    }

    /**
     * 绑定延时队列到交换器
     */
    @Bean
    public Binding delayedEmailBinding() {
        return BindingBuilder.bind(delayedEmailQueue())
                .to(delayedEmailExchange())
                .with("email.delay.key")
                .noargs();
    }

    /**
     * 死信队列配置（备用方案）
     */
    @Bean
    public Queue dlxQueue() {
        return new Queue("email.dlx.queue", true);
    }

    @Bean
    public DirectExchange dlxExchange() {
        return new DirectExchange("email.dlx.exchange");
    }

    @Bean
    public Binding dlxBinding() {
        return BindingBuilder.bind(dlxQueue())
                .to(dlxExchange())
                .with("email.dlx.key");
    }

}