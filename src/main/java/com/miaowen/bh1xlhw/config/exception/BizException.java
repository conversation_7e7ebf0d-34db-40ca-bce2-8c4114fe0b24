package com.miaowen.bh1xlhw.config.exception;

import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@Data
public class BizException extends RuntimeException {
    private ResultEnum resultEnum;
    private int code;
    private String msg;

    public BizException(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public BizException(ResultEnum resultEnum){
        this.code = resultEnum.getCode();
        this.msg = resultEnum.getMsg();
    }

    public BizException(String msg){
        this.code = 400;
        this.msg = msg;
    }

}
