package com.miaowen.bh1xlhw.config.exception;


import com.miaowen.bh1xlhw.common.vo.ResultEnum;
import com.miaowen.bh1xlhw.common.vo.ResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.validation.BindException;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.ValidationException;
import java.io.IOException;

import static com.miaowen.bh1xlhw.common.vo.ResultEnum.API_PERMISSIONS;


/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    /**
     * 处理空指针的异常
     */
    @ExceptionHandler(NullPointerException.class)
    public ResultVO<?> nullPointerHandler(HttpServletRequest req, NullPointerException e) {
        printErrorInterfaceInfo();
        log.error("空指针异常,原因是-------->", e);
        return ResultVO.fail();
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResultVO<?> httpRequestMethodNotSupportedException(HttpServletRequest req, Exception e) {
        log.error("405,原因是-------->" + "请求方法错误");
        return ResultVO.fail(ResultEnum.REQUEST_METHOD_NOT_SUPPORTED);
    }


    /**
     * 参数异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResultVO<?> methodArgumentException(MethodArgumentNotValidException e) {
        // 拿ObjectError错误信息 只拿一条
        ObjectError objectError = e.getBindingResult().getAllErrors().get(0);
        // 返回错误提示信息
        return ResultVO.fail(ResultEnum.PARAM_ERROR, "参数错误:" + objectError.getDefaultMessage());

    }

    /**
     * 参数异常
     */
    @ExceptionHandler(BadSqlGrammarException.class)
    public ResultVO<?> badSqlGrammarException(BadSqlGrammarException e) {
        log.error("sql报错,error:{}", e.getCause().getMessage(), e);
        // 返回错误提示信息
        return ResultVO.fail(ResultEnum.SQL_ERROR);

    }

    /**
     * 参数转换错误
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ResultVO<?> httpMessageNotReadableException(HttpMessageNotReadableException httpMessageNotReadableException) {
        log.error("请求参数的数据类型有误", httpMessageNotReadableException);
        return ResultVO.fail(ResultEnum.PARAM_ERROR, "请求参数的数据类型有误");
    }

    @ExceptionHandler(AuthenticationException.class)
    public ResultVO<?> authException(AuthenticationException authenticationException) {
        return ResultVO.fail(ResultEnum.API_PERMISSIONS, authenticationException.getMessage());
    }

    /**
     * 参数异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResultVO<?> directMethodArgumentException(ConstraintViolationException e) {
        // 拿ObjectError错误信息 只拿一条
        ConstraintViolation<?> error = e.getConstraintViolations().iterator().next();
        // 返回错误提示信息
        return ResultVO.fail(ResultEnum.PARAM_ERROR, "参数错误:" + error.getMessage());

    }

    /**
     * 校验参数异常
     */
    @ExceptionHandler(BindException.class)
    public ResultVO<?> validBindExceptionHandle(BindException e) {
        // 拿ObjectError错误信息 只拿一条
        ObjectError objectError = e.getBindingResult().getAllErrors().get(0);
        // 返回错误提示信息
        return ResultVO.fail(ResultEnum.PARAM_ERROR, "参数错误:" + objectError.getDefaultMessage());
    }


    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public ResultVO<?> exceptionHandler(HttpServletRequest req, Exception e) {
        printErrorInterfaceInfo();
        log.error("未知异常,原因是-------->", e);
        return ResultVO.fail();
    }

    /**
     * IO异常
     */
    @ExceptionHandler(IOException.class)
    public ResultVO<?> ioExceptionHandler(HttpServletRequest req, IOException e) {
        printErrorInterfaceInfo();
        log.error("IO异常,原因是-------->", e);
        return ResultVO.fail();
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(BizException.class)
    public ResultVO<?> bizExceptionHandler(BizException e) {
        return ResultVO.fail(ResultEnum.FAIL, e.getMsg());
    }

    private void printErrorInterfaceInfo() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        assert servletRequestAttributes != null;
        HttpServletRequest request = servletRequestAttributes.getRequest();
        StringBuffer requestUrl = request.getRequestURL();
        String method = request.getMethod();
        log.error("URL : {}  Method :{}", requestUrl, method);
    }



    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResultVO<Void> handleValidationException(ValidationException e) {
        return ResultVO.fail(ResultEnum.FAIL, e.getMessage());
    }
}


