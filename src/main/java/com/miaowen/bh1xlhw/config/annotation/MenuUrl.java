package com.miaowen.bh1xlhw.config.annotation;

import cn.hutool.core.text.AntPathMatcher;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * 自定义校验注解：用于验证菜单URL字段格式
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MenuUrl.MethodTypeValidator.class)
public @interface MenuUrl {
    // -------------------- 注解配置参数 --------------------
    /** 校验失败时的默认提示信息 */
    String message() default "字段校验失败";

    /** 是否必填字段 */
    boolean required() default false;

    // JSR-303规范要求的参数
    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 校验器实现类
     * 核心功能：当请求方法与路径匹配时，验证字段值的合法性
     */
    @Slf4j
    class MethodTypeValidator implements ConstraintValidator<MenuUrl, Object> {
        // -------------------- 配置参数缓存 --------------------
        private boolean isRequired;

        /**
         * 初始化校验器参数
         * @param constraint 注解配置实例
         */
        @Override
        public void initialize(MenuUrl constraint) {
            this.isRequired = constraint.required();
        }

        /**
         * 核心校验方法
         * @param value 被校验字段的值
         * @param context 校验上下文
         * @return 是否通过校验
         */
        @Override
        public boolean isValid(Object value, ConstraintValidatorContext context) {
            // 获取当前HTTP请求对象
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes == null) {
                log.debug("非Web请求环境，跳过校验");
                // 非Web请求场景不做校验
                return true;
            }

            return validateFieldValue(value);
        }


        /**
         * 执行字段值校验
         */
        private boolean validateFieldValue(Object value) {
            boolean result;
            if (!isRequired){
                if (value==null){
                    result = true;
                }else {
                    result = checkRequiredField(value);
                }
            }else {
                result = checkRequiredField(value);
            }
            return result;
        }

        /**
         * 必填字段校验逻辑
         */
        private boolean checkRequiredField(Object value) {

            if (value instanceof String) {
                String strValue = (String) value;


                if (strValue.isEmpty()) {
                    return true;
                }

                if (!StringUtils.hasText(strValue)) {
                    return false;
                }

                // 格式校验：方法类型:接口路径（示例：get:/api/users）
                return isValidMethodUrlFormat(strValue);
            }
            return true; // 非String类型只要有值即视为有效
        }

        /**
         * 验证字符串格式是否为"方法类型:URL路径"
         */
        private boolean isValidMethodUrlFormat(String value) {
            String[] parts = value.split(":", 2);
            if (parts.length != 2) {
                return false;
            }

            String method = parts[0].toLowerCase();
            String url = parts[1];

            // 严格校验方法类型必须存在于白名单
            if (!isValidHttpMethod(method)) {
                log.warn("非法HTTP方法类型: {}", method);
                return false;
            }

            return StringUtils.hasText(url);
        }


        /**
         * 校验方法类型合法性
         * @param method 待校验方法（需已转为小写）
         * @return 是否为合法方法类型
         */
        private boolean isValidHttpMethod(String method) {
            return SystemConstant.METHOD_TYPE_LIST.contains(method);
        }

    }
}
