package com.miaowen.bh1xlhw.config.filter;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.Map;

/**
 * 打印请求的信息
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since  2025/3/18 15:05
 */
@Slf4j
@Configuration
public class RequestFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        if (isMultipartRequest(httpRequest)) {
            // 只记录基础信息
            try {
                extracted(httpRequest);
            } catch (Exception e) {
                log.error("打印日志报错",e);
            }
            chain.doFilter(request, response);
        } else {
            // 完整记录非文件请求
            BodyReaderWrapper wrappedRequest = new BodyReaderWrapper(httpRequest);
            try {
                logRequest(wrappedRequest);
            } catch (Exception e) {
                log.error("打印日志报错",e);
            }
            chain.doFilter(wrappedRequest, response);
        }

    }

    private void extracted(HttpServletRequest httpRequest) {
        logRequestBasicInfo(httpRequest);
    }

    private boolean isMultipartRequest(HttpServletRequest request) {
        return request.getContentType() != null
            && request.getContentType().startsWith("multipart/form-data");
    }
    private void logRequestBasicInfo(HttpServletRequest request) {
        StringBuilder info = new StringBuilder("\n=== Multipart Request ===\n")
            .append("Method: ").append(request.getMethod()).append("\n")
            .append("URL: ").append(request.getRequestURL()).append("\n")
            .append("Headers:\n");

        Enumeration<String> headers = request.getHeaderNames();
        while (headers.hasMoreElements()) {
            String name = headers.nextElement();
            info.append("  ").append(name).append(": ")
                .append(request.getHeader(name)).append("\n");
        }

        log.info(info.toString());
    }


    private void logRequest(BodyReaderWrapper request) {
        StringBuilder requestInfo = new StringBuilder();

        // 添加请求基本信息
        requestInfo.append("\n=== Request Info ===\n");
        requestInfo.append("Method: ").append(request.getMethod()).append("\n");
        requestInfo.append("URL: ").append(request.getRequestURL()).append("\n");
        // 添加请求头
        requestInfo.append("Headers:\n");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            if ("cookietoken".equals(headerName)) {
                continue;
            }
            requestInfo.append("  ").append(headerName).append(": ").append(request.getHeader(headerName)).append("\n");
        }

        // 添加查询参数
        requestInfo.append("Query Parameters:\n");
        Map<String, String[]> parameterMap = request.getParameterMap();
        if (!parameterMap.isEmpty()) {
            parameterMap.forEach((key, values) -> {
                requestInfo.append("  ").append(key).append(": ");
                for (int i = 0; i < values.length; i++) {
                    if (i > 0) {
                        requestInfo.append(", ");
                    }
                    requestInfo.append(values[i]);
                }
                requestInfo.append("\n");
            });
        } else {
            requestInfo.append("  No query parameters\n");
        }

        // 添加请求体
        requestInfo.append("Body:\n");
        String body = new String(request.getRequestBody(), StandardCharsets.UTF_8);
        if (!body.isEmpty()) {
            requestInfo.append(body).append("\n");
        } else {
            requestInfo.append("  Empty body\n");
        }

        requestInfo.append("=== End Request Info ===");

        log.info(requestInfo.toString());
    }

}
