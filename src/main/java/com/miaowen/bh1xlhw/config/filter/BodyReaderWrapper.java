package com.miaowen.bh1xlhw.config.filter;

import lombok.Getter;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Getter
public class BodyReaderWrapper extends HttpServletRequestWrapper {
    private final byte[] requestBody;
    private final Map<String, String[]> parameterMap;

    public BodyReaderWrapper(HttpServletRequest request) throws IOException {
        super(request);
        // 读取并保存请求体
        requestBody = StreamUtils.copyToByteArray(request.getInputStream());

        // 解析并保存参数
        if ("POST".equalsIgnoreCase(request.getMethod()) && StringUtils.hasText(request.getContentType()) && request.getContentType().toLowerCase().startsWith("application/x-www-form-urlencoded")) {
            // 对于表单编码的POST请求，解析参数
            parameterMap = parseParameters(new String(requestBody, StandardCharsets.UTF_8));
        } else {
            // 其他情况使用原始参数
            parameterMap = super.getParameterMap();
        }
    }

    private Map<String, String[]> parseParameters(String queryString) {
        if (queryString == null || queryString.isEmpty()) {
            return Collections.emptyMap();
        }

        Map<String, String[]> result = new HashMap<>();
        String[] pairs = queryString.split("&");

        for (String pair : pairs) {
            int idx = pair.indexOf("=");
            String key = idx > 0 ? decodeURLComponent(pair.substring(0, idx)) : pair;
            String value = idx > 0 && pair.length() > idx + 1 ? decodeURLComponent(pair.substring(idx + 1)) : "";

            // 将参数添加到map中
            result.computeIfAbsent(key, k -> new String[0]);
            String[] values = result.get(key);
            String[] newValues = new String[values.length + 1];
            System.arraycopy(values, 0, newValues, 0, values.length);
            newValues[values.length] = value;
            result.put(key, newValues);
        }

        return result;
    }

    // URL解码
    private String decodeURLComponent(String s) {
        try {
            return java.net.URLDecoder.decode(s, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public ServletInputStream getInputStream() {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(requestBody);

        return new ServletInputStream() {
            @Override
            public int read() {
                return byteArrayInputStream.read();
            }

            @Override
            public boolean isFinished() {
                return byteArrayInputStream.available() == 0;
            }

            @Override
            public boolean isReady() {
                return true;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
            }
        };
    }

    @Override
    public BufferedReader getReader() throws IOException {
        return new BufferedReader(new InputStreamReader(getInputStream()));
    }

    @Override
    public Map<String, String[]> getParameterMap() {
        return parameterMap;
    }

    @Override
    public String getParameter(String name) {
        String[] values = parameterMap.get(name);
        return values != null && values.length > 0 ? values[0] : null;
    }

    @Override
    public Enumeration<String> getParameterNames() {
        return Collections.enumeration(parameterMap.keySet());
    }

    @Override
    public String[] getParameterValues(String name) {
        return parameterMap.get(name);
    }
}
