package com.miaowen.bh1xlhw.config.aspect;


import com.miaowen.bh1xlhw.config.annotation.Sharding;
import com.miaowen.bh1xlhw.config.annotation.ShardingParam;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.Objects;

/**
 * 分表切面
 * 自动处理分表上下文的设置和清理
 * 支持通用的分表后缀生成策略
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Aspect
@Component
@Order(1) // 确保在事务切面之前执行
@Slf4j
public class ShardingAspect {

    @Around("@annotation(sharding)")
    public Object around(ProceedingJoinPoint joinPoint, Sharding sharding) throws Throwable {
        try {
            // 优先通过 @ShardingParam 注解获取分表参数
            Object paramValue = getShardingParamValue(joinPoint);

            if (Objects.nonNull(paramValue)) {
                String suffix = paramValue.toString();
                MonthShardingTableNameHandler.setContext(suffix);
                log.debug("设置分表后缀: {}, 方法: {}", suffix, joinPoint.getSignature().toShortString());
            } else {
                log.error("未找到任何可用的分表参数, 方法: {}", joinPoint.getSignature().toShortString());
            }

            // 执行目标方法
            return joinPoint.proceed();

        } finally {
            // 清理分表上下文
            MonthShardingTableNameHandler.clearContext();
        }
    }

    /**
     * 获取带有 @ShardingParam 注解的参数值
     */
    private Object getShardingParamValue(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();

        for (int i = 0; i < parameters.length; i++) {
            Parameter parameter = parameters[i];
            // 检查参数是否有 @ShardingParam 注解
            if (parameter.isAnnotationPresent(ShardingParam.class)) {
                log.debug("找到 @ShardingParam 注解的参数[{}]: 值={}", i, args[i]);
                return args[i];
            }
        }

        log.debug("未找到带有 @ShardingParam 注解的参数");
        return null;
    }

}
