package com.miaowen.bh1xlhw.config.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.miaowen.bh1xlhw.config.security.login.OAuthUser;
import com.miaowen.bh1xlhw.constant.CommonConstant;
import com.miaowen.bh1xlhw.model.entity.SystemLogs;
import com.miaowen.bh1xlhw.repository.ISystemLogsService;
import com.miaowen.bh1xlhw.utils.IpUtil;
import com.miaowen.bh1xlhw.utils.SecurityUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.*;

import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;

/**
 * @Description 操作日志写数据库
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Configuration
public class SystemLogsAspect {

    private final ThreadLocal<SystemLogs> logsThreadLocal = new ThreadLocal<>();

    @Resource
    ISystemLogsService iSystemLogsService;

    @Value("${spring.application.name}")
    String server;

    @Value("${server.port}")
    String port;

    private static final Set<Class<?>> EXCLUDE_TYPES = new HashSet<>(Arrays.asList(
        HttpServletRequest.class,
        HttpServletResponse.class,
        MultipartFile.class,
        MultipartHttpServletRequest.class
    ));
    private Object getSimpleDescription(Object obj) {
        if (obj instanceof MultipartFile) {
            MultipartFile file = (MultipartFile) obj;
            Map<String, Object> fileInfo = new HashMap<>();
            fileInfo.put("fileName", file.getOriginalFilename());
            fileInfo.put("fileSize", file.getSize());
            fileInfo.put("contentType", file.getContentType());
            return fileInfo;
        }
        return "[" + obj.getClass().getSimpleName() + "]";
    }

    // 解析参数值
    private Object parseParameter(Object arg) throws Exception {
        if (arg == null) {
            return null;
        }

        // 基本类型直接返回
        if (arg.getClass().isPrimitive() ||
            Number.class.isAssignableFrom(arg.getClass()) ||
            CharSequence.class.isAssignableFrom(arg.getClass()) ||
            Date.class.isAssignableFrom(arg.getClass())) {
            return arg;
        }

        // 集合类型处理
        if (arg instanceof Collection) {
            List<Object> list = new ArrayList<>();
            for (Object item : (Collection<?>) arg) {
                list.add(parseParameter(item));
            }
            return list;
        }

        // Map类型处理
        if (arg instanceof Map) {
            Map<Object, Object> map = new LinkedHashMap<>();
            for (Map.Entry<?, ?> entry : ((Map<?, ?>) arg).entrySet()) {
                map.put(entry.getKey(), parseParameter(entry.getValue()));
            }
            return map;
        }

        // 其他对象尝试JSON序列化
        try {
            return JSON.parseObject(JSON.toJSONString(arg));
        } catch (Exception e) {
            return arg.toString();
        }
    }


    @Pointcut("(execution(* com.miaowen.bh1xlhw.controller..*(..)))")
    public void logPointCut() {
    }

    private boolean shouldExclude(Class<?> clazz) {
        return EXCLUDE_TYPES.stream().anyMatch(exclude -> exclude.isAssignableFrom(clazz));
    }


    @Before("logPointCut()")
    public void before(JoinPoint joinPoint) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        LocalDateTime startTime = LocalDateTime.now();


        SystemLogs systemLogs = new SystemLogs();
        try {
            String jsonData = getJsonData(joinPoint);

            systemLogs = SystemLogs.builder()
                .method(request.getMethod())
                .uri(request.getRequestURI())
                .body(jsonData)
                .ip(IpUtil.getIpFromRequest(request))
                .server(server)
                .port(port)
                .startTime(startTime)
                .build();
            OAuthUser oAuthUser = SecurityUtil.currentUser();
            if (Objects.nonNull(oAuthUser)){
                systemLogs.setAdminUserId(oAuthUser.getUserId());
                systemLogs.setAdminUsername(oAuthUser.getUsername());
            }
            systemLogs.setAgent(request.getHeader("User-Agent"));
            if (StringUtils.hasText(request.getHeader("Authorization"))) {
                systemLogs.setToken(request.getHeader("Authorization").toLowerCase().replace("bearer ", ""));
            }
        } catch (Exception e) {
            //只记录，不影响线上
            log.warn(e.getMessage(), e);
        }

        logsThreadLocal.set(systemLogs);
    }

    private String getJsonData(JoinPoint joinPoint) {
        String jsonData;
        try {
            jsonData = CommonConstant.EMPTY_STRING;
            Object[] args = joinPoint.getArgs();

            if (args != null && args.length > 0) {
                Map<String, Object> params = new LinkedHashMap<>();

                // 获取方法参数名(需要编译时开启-parameters或使用@Param注解)
                MethodSignature signature = (MethodSignature) joinPoint.getSignature();
                String[] paramNames = signature.getParameterNames();

                for (int i = 0; i < args.length; i++) {
                    Object arg = args[i];
                    String paramName = (paramNames != null && i < paramNames.length) ? paramNames[i] : "arg" + i;
                    if (arg == null) {
                        params.put(paramName, "");
                    } else if (shouldExclude(arg.getClass())) {
                        params.put(paramName, getSimpleDescription(arg));
                    } else {
                        try {
                            params.put(paramName, parseParameter(arg));
                        } catch (Exception e) {
                            params.put(paramName, "[参数解析失败: " + e.getMessage() + "]");
                        }
                    }
                }
                jsonData = JSON.toJSONString(params);
            }
        } catch (Exception e) {
            return CommonConstant.EMPTY_STRING;
        }
        return jsonData;
    }

    @AfterReturning(pointcut = "logPointCut()", returning = "ret")
    public void doAfterReturning(Object ret) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            SystemLogs systemLogs = logsThreadLocal.get();
            Long ms = Duration.between(systemLogs.getStartTime(), endTime).toMillis();
            systemLogs.setEndTime(endTime);
            systemLogs.setMs(ms+"ms");
            iSystemLogsService.save(systemLogs);
        } catch (Exception e) {
           log.warn("日志记录报错", e);
        } finally {
            logsThreadLocal.remove();
        }

    }

    @AfterThrowing(pointcut = "logPointCut()", throwing = "e")
    public void doAfterThrowable(Throwable e) {
        try {
            LocalDateTime endTime = LocalDateTime.now();
            SystemLogs systemLogs = logsThreadLocal.get();
            Long ms = Duration.between(systemLogs.getStartTime(), endTime).toMillis();
            systemLogs.setEndTime(endTime);
            systemLogs.setMs(ms+"ms");
            systemLogs.setAbnormal(e.getMessage());
            iSystemLogsService.save(systemLogs);
        }catch (Exception exception){
            log.warn("日志记录报错", exception);
        }
        finally {
            logsThreadLocal.remove();

        }
    }

}
