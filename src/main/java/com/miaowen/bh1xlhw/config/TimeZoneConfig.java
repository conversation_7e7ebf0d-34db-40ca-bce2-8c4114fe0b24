package com.miaowen.bh1xlhw.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.TimeZone;

/**
 * 时区配置类
 * 确保整个应用程序使用东八区时间
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-25
 */
@Slf4j
@Configuration
public class TimeZoneConfig {

    /**
     * 东八区时区常量
     */
    public static final String ASIA_SHANGHAI = "Asia/Shanghai";
    public static final TimeZone ASIA_SHANGHAI_TIMEZONE = TimeZone.getTimeZone(ASIA_SHANGHAI);

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 应用启动时设置默认时区
     */
    @PostConstruct
    public void configureTimeZone() {
        // 设置JVM默认时区
        TimeZone.setDefault(ASIA_SHANGHAI_TIMEZONE);
        
        // 设置系统属性
        System.setProperty("user.timezone", ASIA_SHANGHAI);
        
        // 配置Jackson ObjectMapper的时区
        if (objectMapper != null) {
            objectMapper.setTimeZone(ASIA_SHANGHAI_TIMEZONE);
            objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        }
        
        log.info("时区配置完成 - 默认时区: {}, 时区偏移: {}", 
                TimeZone.getDefault().getID(), 
                TimeZone.getDefault().getRawOffset() / (1000 * 60 * 60));
    }

    /**
     * 创建配置了东八区的ObjectMapper Bean
     */
    @Bean
    @Primary
    public ObjectMapper customObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.setTimeZone(ASIA_SHANGHAI_TIMEZONE);
        mapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        return mapper;
    }

    /**
     * 获取东八区时区
     */
    public static TimeZone getAsiaShanghai() {
        return ASIA_SHANGHAI_TIMEZONE;
    }

    /**
     * 获取当前是否为东八区
     */
    public static boolean isAsiaShanghai() {
        return ASIA_SHANGHAI.equals(TimeZone.getDefault().getID());
    }
}
