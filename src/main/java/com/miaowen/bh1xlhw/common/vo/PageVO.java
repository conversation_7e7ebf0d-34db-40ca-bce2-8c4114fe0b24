package com.miaowen.bh1xlhw.common.vo;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 分页详情
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PageVO<T> {
    /**
     * 当前第几页
     */
    private Integer pageInt = -1;
    /**
     * 一页多少数据
     */
    private Integer pageSize = 10;
    /**
     * 总数量
     */
    private Long totalCount = 0L;
    /**
     * 总页数
     */
    private Long pageTotal = 0L;
    private List<T> records = Collections.emptyList();

    public PageVO(Page<?> pages, Class<T> clazz) {
        this.totalCount = pages.getTotal();
        this.pageInt = (int)pages.getCurrent();
        this.pageTotal = pages.getPages();
        this.pageSize = (int)pages.getSize();
        this.records = BeanUtil.copyToList(pages.getRecords(), clazz);
    }

    public PageVO(Page<?> pages) {
        this.totalCount = pages.getTotal();
        this.pageInt = (int)pages.getCurrent();
        this.pageTotal = pages.getPages();
        this.pageSize = (int)pages.getSize();
        this.records = new ArrayList<>();
    }

    public PageVO<T> convert(List<?> list, Class<T> clazz) {
        this.records = BeanUtil.copyToList(list, clazz);
        return this;
    }

    public PageVO<T> convert(List<T> list) {
        this.records = list;
        return this;
    }

}
