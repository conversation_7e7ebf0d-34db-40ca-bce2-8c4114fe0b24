package com.miaowen.bh1xlhw.common.vo;

import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description 统一返回结果类
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2024/3/19 15:04
 */
@Data
public class ResultVO<T> implements Serializable {
    private int code;

    private String msg;

    private T data;

    public ResultVO(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResultVO(ResultEnum result) {
        this.code = result.getCode();
        this.msg = result.getMsg();
    }

    public ResultVO(ResultEnum result, T data) {
        this.code = result.getCode();
        this.msg = result.getMsg();
        this.data = data;
    }

    public ResultVO(ResultEnum result, String msg, T data) {
        this.code = result.getCode();
        this.msg = msg;
        this.data = data;
    }

    public ResultVO(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public ResultVO() {
    }

    public static <T> ResultVO<T> success() {
        return new ResultVO<>(ResultEnum.SUCCESS);
    }

    public static <T> ResultVO<T> success(T data) {
        return new ResultVO<>(ResultEnum.SUCCESS, data);
    }

    public static <T> ResultVO<T> fail(ResultEnum resultEnum, String message) {
        return new ResultVO<>(resultEnum.getCode(), message, null);
    }

    public static <T> ResultVO<T> fail(ResultEnum resultEnum, T data) {
        return new ResultVO<>(resultEnum.getCode(), resultEnum.getMsg(), data);
    }

    public static <T> ResultVO<T> fail(ResultEnum resultEnum){
        return new ResultVO<>(resultEnum.getCode(), resultEnum.getMsg(), null);
    }


    public static <T> ResultVO<T> fail() {
        return new ResultVO<>(ResultEnum.FAIL);
    }

    public static <T> ResultVO<PageVO<T>> successForPage(List<T> record, Long totalCount, Integer pageInt,
                                                         Integer pageSize) {
        Long pageTotal = totalCount % pageSize == 0 ? totalCount/pageSize : totalCount/pageSize + 1;
        PageVO<T> pageVo = new PageVO<T>(pageInt, pageSize, totalCount, pageTotal, record);
        return ResultVO.success(pageVo);
    }

    public static <T> ResultVO<PageVO<T>> successForPage(List<T> record, IPage<?> page) {
        return successForPage(record, page.getTotal(), (int)page.getCurrent(), (int) page.getSize());
    }

    public static <T> ResultVO<PageVO<T>> successForPage(IPage<T> iPage) {
        return ResultVO.successForPage(iPage.getRecords(), iPage.getTotal(), (int) iPage.getPages(), (int) iPage.getSize());
    }


}
