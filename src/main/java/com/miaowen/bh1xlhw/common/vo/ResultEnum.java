package com.miaowen.bh1xlhw.common.vo;

import lombok.Getter;

/**
 * 统一返回结果枚举类
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2024/3/19 15:20
 */
@Getter
public enum ResultEnum {
    SUCCESS(200,"SUCCESS"),
    PARAM_ERROR(400,"参数错误"),
    SQL_ERROR(410,"sql报错,请联系管理员查看"),

    API_PERMISSIONS(403, "接口无权限"),
    STORE_DISABLED(10001, "账号禁用"),
    TOKEN_INVALID(401, "token无效"),
    TOKEN_EXPIRE(401, "token过期"),
    FAIL(500, "服务错误"),

    REQUEST_METHOD_NOT_SUPPORTED(405, "请求方法不支持"),
    /**
     * 未识别到的异常
     */
    AuthFail(401,"校验失败"),
    DataCheckFail(402,"数据冲突"),
    ResourceNotFound(404, "资源失效"),
    PasswordError(433,"账户或密码错误"),
    NotFoundUser(435,"用户不存在"),
    INVALID_ROLE_IDS(40020, "包含无效的角色ID"),
    WEAK_PASSWORD(40010, "密码强度不足"),
    PERMISSION_DENIED(40300, "权限不足"),
    PASSWORD_ERROR(40011, "新密码与确认密码不一致"),
    NO_AUTHORITY(403, "接口无权限，请联系管理员"),
    FREQUENT_OPERATION(40001, "点击太频繁了,稍等等"),

    /// --------------- 运营管理 ---------------
    PLATFORM_NOT_EXIST(1000001,"平台不存在"),
    WORK_NO_EXIST(1000002, "工号已存在"),

    /// --------------- 字典管理 ---------------
    DICTIONARY_TYPE_EXIST(2000001,"字典类型已存在"),
    DICTIONARY_EXIST(2000002,"字典已存在"),

    /// --------------- 商品管理 ---------------
    GOODS_PROMOTION_NOT_EXIST(3000001, "复制的推广商品不存在或者已被删除"),
    /// --------------- 订单 ---------------
    ORDER_NOT_EXIST(4000001, "订单不存在"),
    MBTI_ORDER_CREATED(4000002, "已创建该赠送订单"),
    MBTI_ORDER_REPORT_NOT_EXIST(4000003, "该赠送报告不存在"),
    /// --------------- 退款 ---------------
    REFUND_FAILED(5000001, "退款失败"),
    REFUND_FINISH(5000002, "退款已完成"),
    FILE_UPLOAD_FAILED(5000003, "文件上传失败"),

    ;

    private final int code;
    private String msg;

    ResultEnum(int code) {
        this.code = code;
    }

    ResultEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

}
