package com.miaowen.bh1xlhw;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @Description admin服务启动类
 * <AUTHOR>
 */
@Slf4j
@EnableAsync
@EnableCaching
@EnableAspectJAutoProxy
@MapperScan("com.miaowen.bh1xlhw.mapper")
@EnableTransactionManagement
@SpringBootApplication
public class AdminApp {


    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(AdminApp.class);
        ConfigurableApplicationContext context = app.run(args);
    }


}
