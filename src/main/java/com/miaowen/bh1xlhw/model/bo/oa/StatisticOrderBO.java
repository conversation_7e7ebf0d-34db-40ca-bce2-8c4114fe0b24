package com.miaowen.bh1xlhw.model.bo.oa;

import lombok.Data;

import java.time.LocalDate;


/**
 * StatisticOrderBo :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@Data
public class StatisticOrderBO {
    /**
     * tgId
     */
    private String tgId;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 支付时间
     */
    private LocalDate paymentDate;

    /**
     * 总订单数量
     */
    private Integer totalOrderNum;
    /**
     * 支付订单数量
     */
    private Integer paidOrderNum;
    /**
     * 总支付金额
     */
    private Integer totalPayAmount;
    /**
     * 原价支付数量
     */
    private Integer originalPriceOrderNum;
    /**
     * 原价支付金额
     */
    private Integer originalPriceAmount;
    /**
     * 红包1支付数量
     */
    private Integer redPackage1OrderNum;
    /**
     *  红包1支付金额
     */
    private Integer redPackage1Amount;
    /**
     * 红包2支付数量
     */
    private Integer redPackage2OrderNum;
    /**
     * 红包2支付金额
     */
    private Integer redPackage2Amount;
}
