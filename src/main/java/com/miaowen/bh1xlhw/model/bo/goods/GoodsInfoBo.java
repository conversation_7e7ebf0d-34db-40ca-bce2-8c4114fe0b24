package com.miaowen.bh1xlhw.model.bo.goods;

import lombok.Data;

/**
 * GoodsInfoBo :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-14
 */
@Data
public class GoodsInfoBo {
    private Integer id;
    /**
     * 中文名称
     */
    private String name;
    /**
     * 价格id
     */
    private Integer priceId;
    /**
     * 商品分类id
     */
    private Integer goodsCategoryId;
    /**
     * 缩略图
     */
    private String thumbImage;
    /**
     * 原价支付通道,0表示默认
     */
    private Integer pricePaymentChannel;

    /**
     * 红包1支付通道,0表示默认
     */
    private Integer red1PaymentChannel;

    /**
     * 红包2支付通道,0表示默认
     */
    private Integer red2PaymentChannel;
    /**
     * 前端站点名字
     */
    private String webPackageName;


}
