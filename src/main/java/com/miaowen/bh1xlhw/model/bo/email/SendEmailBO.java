package com.miaowen.bh1xlhw.model.bo.email;

import com.miaowen.bh1xlhw.model.bo.EmailParamBO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * SendEmailBO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendEmailBO {
    /**
     * 模板类型
     */
    private String emailTemplate;
    /**
     * 邮件类型
     */
    private String emailType;
    /**
     * 语言
     */
    private String languageCode;
    /**
     * 发送人
     */
    private String toEmail;
    /**
     * 邮件参数
     */
    private EmailParamBO emailParam;
    /**
     * 退订地址
     */
    private String unsubscribeUrl;

    /**
     * 订单id
     */
    private Long orderId;
    /**
     * 订单号
     */
    private String outTradeNo;
    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;


    /**
     * 商品分类
     */
    private Integer goodsCategoryId;
    /**
     * 商品类型1:多语言,2:繁体,
     */
    private Integer goodsType;

}

