package com.miaowen.bh1xlhw.model.bo.payment;

import lombok.Data;

/**
 * WechatConfigBO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-03
 */
@Data
public class WechatConfigBO {
    /**
     * appid
     */
    private String wechatAppid;
    /**
     * Secret
     */
    private String wechatAppSecret;
    /**
     * 商户Id
     */
    private String wechatMchId;
    /**
     * 商户Key
     */
    private String wechatMchKey;
    /**
     * 商户V3Key
     */
    private String wechatV3MchKey;
    /**
     * 证书序列号
     */
    private String wechatCertSn;
    /**
     * 支付证书地址
     */
    private String wechatApiClientCert;
    /**
     * 证书秘钥地址
     */
    private String wechatApiClientKey;
    /**
     * 微信支付平台证书
     */
    private String wechatPlatformKey;
    /**
     * 微信商户授权域名
     */
    private String wechatAuthUrl;

}
