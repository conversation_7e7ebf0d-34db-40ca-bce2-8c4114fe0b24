package com.miaowen.bh1xlhw.model.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 价格方案表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_price
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_price")
@Data
public class Price extends BasicsEntity {
    /**
     * 价格方案名称
     */
    private String name;

    /**
     * 价格类型,1:单价,2:双价,3:3价
     */
    private Integer type;

    /**
     * 低档价格,美分单位
     */
    private Integer price1;

    /**
     * 低档红包1价格,美分单位
     */
    private Integer red1Price1;

    /**
     * 低档红包2价格,美分单位
     */
    private Integer red2Price1;

    /**
     * 低档划线价格,美分单位
     */
    private Integer originPrice1;

    /**
     * 中档价格,美分单位
     */
    private Integer price2;

    /**
     * 中档红包1价格,美分单位
     */
    private Integer red1Price2;

    /**
     * 中档红包2价格,美分单位
     */
    private Integer red2Price2;

    /**
     * 中档划线价格,美分单位
     */
    private Integer originPrice2;

    /**
     * 高档价格,美分单位
     */
    private Integer price3;

    /**
     * 高档红包1价格,美分单位
     */
    private Integer red1Price3;

    /**
     * 高档红包2价格,美分单位
     */
    private Integer red2Price3;

    /**
     * 高档划线价格,美分单位
     */
    private Integer originPrice3;

    /**
     * roi基数
     */
    private BigDecimal baseRoi;

    /**
     * roi目标值
     */
    private BigDecimal targetRoi;

    /**
     * 红包1
     */
    private Integer redPackage1;

    /**
     * 红包2
     */
    private Integer redPackage2;

    /**
     * 红包1是否回传
     */
    private Integer redPackageBack1;

    /**
     * 红包2是否回传
     */
    private Integer redPackageBack2;

    /**
     * 邮箱回传
     */
    private Integer emailBack;

    @Getter
    @AllArgsConstructor
    public enum Type {
        /**
         * 单价
         */
        ONE(1),
        /**
         * 双价
         */
        TWO(2),
        /**
         * 三价
         */
        THREE(3),
        ;
        private final Integer value;

    }
}
