package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代理商
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_agents
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_agents")
@Data
public class Agents extends BasicsEntity {
    /**
     * 合作公司名称
     */
    private String name;

    /**
     * 结算方式1:月结,2:预付
     */
    private Integer settleType;

    /**
     * 平台表的主键
     */
    private Integer platformId;
    /**
     * 平台类型
     */
    private String platform;

    /**
     * 运营方式1:自运营，2:代运营
     */
    private Integer operationType;

    /**
     * 返点
     */
    private BigDecimal rebate;
}
