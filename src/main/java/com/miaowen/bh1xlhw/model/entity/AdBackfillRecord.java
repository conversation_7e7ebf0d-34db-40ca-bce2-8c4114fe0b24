package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description 广告回传记录实体类
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 10:22
 */
@Data
@EqualsAndHashCode()
@TableName("po_ad_backfill_record")
public class AdBackfillRecord {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 推广ID
     */
    private String tuid;

    /**
     * 追踪ID
     */
    private String traceId;

    /**
     * 推广表对应ID
     */
    private Integer tgId;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 支付状态 0初始化 1下单 2成功 3等待 4失败 5退款
     */
    private Integer payStatus;

    /**
     * 支付金额
     */
    private String payAmount;

    /**
     * 订单时间/答题时间
     */
    private Date orderTime;

    /**
     * 页面类型
     */
    private Integer pageType;

    /**
     * 平台代码
     */
    private String platformCode;

    /**
     * 前端URL
     */
    private String frontUrl;

    /**
     * ip
     */
    private String ip;

    /**
     * 扩展参数（广告值）
     */
    private String params;

    /**
     * 发送状态(0：未发送  1：已发送)
     */
    private String sendStatus;

    /**
     * 发送状态 1:发送成功 -1:发送失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;
}
