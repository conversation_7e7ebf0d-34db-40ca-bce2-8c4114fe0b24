package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 权限表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName acc_permission
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_acc_permission")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccPermission extends BasicsEntity {
    /**
     * 菜单名称，按钮名称
     */
    private String title;

    /**
     * 名字
     */
    private String name;

    /**
     * 路径
     */
    private String path;

    /**
     * 组件
     */
    private String component;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 权限（前台使用，做接口鉴权）
     */
    private String sign;

    /**
     * 权限（后台使用，做接口鉴权）
     */
    private String apiUrl;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 父节点(对应当前表的所属父节点的ID)
     */
    private Integer pid;

    /**
     * 权限类型1一级菜单2二级菜单3按钮
     */
    private Integer type;

    /**
     * 页面状态0显示1隐藏
     */
    private Integer hidden;

    @Getter
    @AllArgsConstructor
    public enum Type {
        FIRST(1),
        SECOND(2),
        BUTTON(3);
        private final int value;
    }
}
