package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 后台退款记录表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_refund_record
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_refund_record")
@Data
public class RefundRecord extends BasicsEntity {
    /**
     * 退款订单号
     */
    private Long orderId;


    private String outTradeNo;

    /**
     * 退款订单号
     */
    private String refundTradeNo;

    /**
     * 三方退款id
     */
    private String thirdRefundTradeNo;

    /**
     * 美元汇率
     */
    private BigDecimal usdExchangeRate;

    /**
     * 退款金额
     */
    private Integer amount;

    /**
     * 退款货币
     */
    private String currency;

    /**
     * 1pending,2成功,3失败
     */
    private Integer status;

    @Getter
    @AllArgsConstructor
    public enum StatusEnum {
        UNDEFINED(0,"undefined"),
        PENDING(1,"pending"),
        SUCCEEDED(2,"succeeded"),
        FAILED(3,"failed"),
        ;
        private final Integer status;
        private final String value;
        public static StatusEnum getByValue(String value) {
            for (StatusEnum statusEnum : StatusEnum.values()) {
                if (statusEnum.getValue().equals(value)) {
                    return statusEnum;
                }
            }
            return UNDEFINED;
        }
    }
}
