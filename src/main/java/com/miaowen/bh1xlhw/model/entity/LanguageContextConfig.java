package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 多语言配置表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_language_context_config
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_language_context_config")
@Data
public class LanguageContextConfig extends BasicsEntity {
    /**
     * 关联id
     */
    private Integer relationId;

    /**
     * 关联类型1,多语言商品
     */
    private Integer relationType;

    /**
     * 语言code
     */
    private String languageCode;

    /**
     * 内容
     */
    private String context;

    @Getter
    @AllArgsConstructor
    public enum RelationType{
        /**
         * 多语言商品
         */
        GOODS_MULTILINGUAL(1),

        ;
        private final int value;
    }
}
