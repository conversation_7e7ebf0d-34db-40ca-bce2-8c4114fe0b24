package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 错题解析实体类
 */
@Data
@EqualsAndHashCode()
@TableName("po_result_wrong_analysis")
public class ResultWrongAnalysis {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间(软删除)
     */
    private Integer deleteTime;

    /**
     * 分类标签
     */
    private String categoryTag;

    /**
     * 题号
     */
    private String examNum;

    /**
     * 正确答案
     */
    private String examAnswer;

    /**
     * 错题解析
     */
    private String examExplain;


    /**
     * 语言类型
     */
    private String langType;

    /**
     * 1：多语言 2：繁体
     */
    private Integer type;

    /**
     * 分数
     */
    private int score;
}