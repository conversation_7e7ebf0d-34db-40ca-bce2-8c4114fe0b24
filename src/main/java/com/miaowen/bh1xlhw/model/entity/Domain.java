package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 域名管理
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_domin
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_domain")
@Data
public class Domain extends BasicsEntity {
    /**
     * 域名
     */
    private String domain;
    /**
     * 平台id
     */
    private Integer platformId;
    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 商品分类
     */
    private Integer goodsCategoryId;
    /**
     * 邮箱id
     */
    private String emailIds;
    /**
     * 备注
     */
    private String remark;

}
