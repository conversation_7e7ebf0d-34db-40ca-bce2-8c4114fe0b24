package com.miaowen.bh1xlhw.model.entity.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * mbti子订单表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_order_mbti
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_order_mbti")
@Data
public class OrderMbti extends BaseOrder {

    /**
     * 主订单id
     */
    private Long mainOrderId;

    /**
     * 主订单号
     */
    private String mainOutTradeNo;
    /**
     * mbti人格
     */
    private String mbti;


    /**
     * 子商品类型,1:mbti职场优势,2:mbti八维报告3:mbti恋爱报告
     */
    private Integer type;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品类型多1,语言商品,2繁体商品
     */
    private Integer goodsType;

}