package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/18 17:39
 */
@Data
@Builder
@TableName(value = "po_email_template_content")
@NoArgsConstructor
@AllArgsConstructor
public class EmailTemplateContent extends BasicsEntity {

    //语言编码
    private String languageCode;

    //邮件模板id
    private Integer emailTemplateId;

    //邮件标题
//    @NotBlank
    @Length(max = 30)
    private String title;

    //邮件内容
//    @NotBlank
    private String content;

}
