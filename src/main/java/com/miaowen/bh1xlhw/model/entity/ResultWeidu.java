package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 维度表
  * @company 武汉秒闻网络科技有限公司

 */
@Data
@TableName("po_result_weidu")
public class ResultWeidu implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 0：多语言 1：繁体
     */
    private Integer type;

    /**
     * 维度类型
     */
    private String weiduType;

    /**
     * 名称
     */
    private String name;

    /**
     * 题目序号
     */
    private String questionNums;

    /**
     * 总分
     */
    private Integer score;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
