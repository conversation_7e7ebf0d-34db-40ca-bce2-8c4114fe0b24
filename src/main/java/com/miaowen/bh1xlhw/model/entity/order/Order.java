package com.miaowen.bh1xlhw.model.entity.order;

import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 交易订单表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_order
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_order")
@Data
public class Order extends BaseOrder {
    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 用户logid
     */
    private String logId;

    /**
     * 推广id
     */
    private String tgId;

    /**
     * 商品类型多1,语言商品,2繁体商品
     */
    private Integer goodsType;

    /**
     * 商品版本,1:mbti男版本,2:女版本
     */
    private Integer goodsVersion;
    /**
     * 原价用户货币,单位分
     */
    private Integer originalAmount;
    /**
     * 美金原价 美分单位
     */
    private Integer usdOriginalAmount;
    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 推广商品id
     */
    private Integer goodsPromotionId;

    /**
     * 投放平台类型
     */
    private String platformType;

    /**
     * 代理商id
     */
    private Integer agentId;

    /**
     * 运营人员id
     */
    private Integer operationUserId;

    /**
     * 价格方案id
     */
    private Integer priceId;

    /**
     * 权益等级,1:低档,2:中档,3:高档
     */
    private Integer priceLevel;

    /**
     * 来源
     */
    private String source;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 语言代码
     */
    private String languageCode;

    /**
     * 支付日期
     */
    private LocalDate paymentDate;

    /**
     * 红包使用类型 0原价 1红包1 2红包2
     */
    private Integer redPackUseType;

    /**
     * 红包领取类型 0未领取 1红包1 2红包2
     */
    private Integer redPackGetType;

    /**
     * 是否查看结果
     */
    private Integer isReadResult;

    /**
     * 是否通过邮箱过来的
     */
    private Integer isEmail;

    /**
     * 邮件发送状态,0失败,1成功
     */
    private Integer emailSendStatus;

    /**
     * 支付环境
     */
    private String paymentEnv;

    /**
     * 创建日期
     */
    private LocalDate createDate;

}
