package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 推广邮件记录
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
@TableName("po_email_send_record")
public class EmailSendRecord extends BasicsEntity {
    private static final long serialVersionUID = 1L;


    /**
     * 邮件标题
     */
    private String title;

    /**
     * 邮件内容
     */
    private String content;

    /**
     * 邮件模板id
     */
    private Integer emailTemplateId;

    /**
     * 发送状态1：成功，0失败
     */
    private Integer sendStatus ;

    /**
     * 收件人邮箱
     */
    private String email;


    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;

    /**
     * 订单号
     */
    private String outTradeNo;
    /**
     * 错误信息
     */
    private String errorInfo;

    /**
     * 语言
     */
    private String languageCode;

    /**
     * 订单id
     */
    private Long orderId;


    /**
     *点击时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime clickTime;
}
