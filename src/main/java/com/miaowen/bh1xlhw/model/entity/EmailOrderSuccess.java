package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/21 14:20
 */
@Data
@TableName("po_email_order_success")
public class EmailOrderSuccess extends BasicsEntity {


    /**
     * 订单表id
     */
    private Long orderId;

    /**
     * 订单号
     */
    private String outTradeNo;

    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;

    /**
     * 语言代码
     */
    private String languageCode;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;
    /**
     * 域名
     */
    private String domain;

    /**
     * 用户填写的邮箱
     */
    private String email;

    /**
     * 三方订单提供的邮箱
     */
    private String thirdEmail;

}
