package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 角色权限关联表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName acc_role_permission
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_acc_role_permission")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccRolePermission extends BasicsEntity {
    /**
     * 角色id
     */
    private Integer roleId;

    /**
     * 权限id
     */
    private Integer permissionId;
}
