package com.miaowen.bh1xlhw.model.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 交易订单环境表,交易订单的水平拆分表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_order_environmental
 */
@TableName(value ="po_order_environmental")
@Data
public class OrderEnvironmental{
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 临时用户id
     */
    private String logId;

    /**
     * 浏览器版本（示例：Chrome *********）
     */
    private String browserVersion;

    /**
     * 完整User-Agent
     */
    private String userAgent;

    /**
     * 设备型号（示例：Windows 10）
     */
    private String deviceModel;

    /**
     * 屏幕分辨率（示例：2560x1440）
     */
    private String screenResolution;

    /**
     * 网络类型: 4G/5G/Wi-Fi
     */
    private String networkType;

    /**
     * 客户端IP（支持IPv6）
     */
    private String ipAddress;

    /**
     * IP归属地（示例：中国武汉）
     */
    private String ipRegion;

    /**
     * 用户语言（示例：CN）
     */
    private String languageCode;

    /**
     * 时区（示例：Asia/Shanghai）
     */
    private String timezone;

    /**
     * 时区偏移（单位：小时）
     */
    private Integer timezoneOffset;

    /**
     * 购买页面URL
     */
    private String webSite;

    /**
     * 支付API站点URL
     */
    private String apiSite;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;

    private Integer deleteTime;
}
