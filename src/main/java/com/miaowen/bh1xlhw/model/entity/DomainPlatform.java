package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 记录域名平台使用情况
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_domain_platform
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_domain_platform")
@Data
public class DomainPlatform extends BasicsEntity {
    /**
     * 域名id
     */
    private Integer domainId;

    /**
     * 推广商品id
     */
    private Integer tgId;
}
