package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 用户角色关联表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName acc_user_role
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_acc_user_role")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccUserRole extends BasicsEntity {
    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 角色id
     */
    private Integer roleId;
}
