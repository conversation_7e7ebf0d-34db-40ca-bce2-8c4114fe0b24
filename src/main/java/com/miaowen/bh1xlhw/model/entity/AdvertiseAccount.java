package com.miaowen.bh1xlhw.model.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 广告账户表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_advertise_account
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_advertise_account")
@Data
public class AdvertiseAccount extends BasicsEntity {
    /**
     * 账户名称
     */
    private String name;

    /**
     * 平台类型
     */
    private String platformType;


    /**
     * 价格方案id
     */
    private Integer priceId;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 运营人员id
     */
    private Integer operationId;
}
