package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 角色表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_exchange_rate_logs
 */
@TableName(value ="po_exchange_rate_logs")
@Data
public class PoExchangeRateLog {

    /**
     * 货币单位
     */
    private String currencyUnit;

    /**
     * 汇率日期
     */
    private Date rateDate;

    /**
     * 货币与美元的汇率
     */
    private BigDecimal exchangeRate;


    @TableId(type = IdType.AUTO)
    private Integer id;


    private Integer createTime;


    private Integer updateTime;

    private Integer deleteTime;



}
