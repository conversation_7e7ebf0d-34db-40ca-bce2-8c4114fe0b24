package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 多语言商品表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_goods_multilingual
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value = "po_goods_multilingual")
@Data
public class GoodsMultilingual extends BasicsEntity {
    /**
     * 中文名称
     */
    private String name;

    /**
     * 商品分类id
     */
    private Integer goodsCategoryId;

    /**
     * 试卷题目数量
     */
    private Integer paperNum;
    /**
     * 价格id
     */
    private Integer priceId;


    /**
     * 文章id 逗号分割
     */
    private String articleId;

    /**
     * 风格名称
     */
    private String styleName;
    /**
     * 缩略图
     */
    private String thumbImage;

    /**
     * 前端包名
     */
    private String webPackageName;

    /**
     * 原价支付通道,0表示默认
     */
    private Integer pricePaymentChannel;

    /**
     * 红包1支付通道,0表示默认
     */
    private Integer red1PaymentChannel;

    /**
     * 红包2支付通道,0表示默认
     */
    private Integer red2PaymentChannel;

    /**
     * 商品权益信息
     */
    private String benefitInfo;
}
