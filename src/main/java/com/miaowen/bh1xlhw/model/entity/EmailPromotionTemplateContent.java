package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

/**
 * 推广邮件内容表
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/18 17:39
 */
@Data
@Builder
@TableName(value = "po_email_promotion_template_content")
@NoArgsConstructor
@AllArgsConstructor
public class EmailPromotionTemplateContent extends BasicsEntity {

    //语言编码
    private String languageCode;

    //邮件模板id
    private Integer emailPromotionTemplateId;

    //邮件标题
//    @NotBlank
    @Length(max = 30)
    private String title;

    //邮件内容
//    @NotBlank
    private String content;

}
