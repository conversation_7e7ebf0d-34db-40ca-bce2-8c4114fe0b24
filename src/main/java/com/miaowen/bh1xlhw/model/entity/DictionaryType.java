package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;


/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/20 10:10
 */
@Data
@TableName("po_system_dictionary_type")
@EqualsAndHashCode(callSuper = true)
public class DictionaryType extends BasicsEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 备注
     */
    private String description;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态0禁用1启用
     */
    private Integer status;

}
