package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Description 做题日志实体
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 10:22
 */
@Data
@EqualsAndHashCode()
@TableName("po_exam_log")
public class ExamLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 试卷ID
     */
    private String examId;

    /**
     * 设备指纹信息
     */
    private String deviceFingerprint;

    /**
     * 推广ID
     */
    private String tuid;

    /**
     * 来源标识
     */
    private String source;

    /**
     * 投放平台
     */
    private String platformCode;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品ID
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 总题数
     */
    private Integer questionsNum;

    /**
     * 已答题数
     */
    private Integer examNum;

    /**
     * 考试进度
     */
    private String examSchedule;

    /**
     * 1:多语言 2：繁体
     */
    private Integer goodsType;

    /**
     * 支支付状态 0初始化 1下单 2成功 3等待 4失败 5退款
     */
    private Integer payStatus;

    /**
     * 支付金额
     */
    private String payAmount;

    /**
     * 总金额
     */
    private String totalAmount;

    /**
     * IP地址
     */
    private String ip;

    /**
     * IP地理位置
     */
    private String ipAddress;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;
}
