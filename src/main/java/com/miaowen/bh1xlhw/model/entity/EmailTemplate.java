package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 商户管理表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_merchant
 */
@TableName(value ="po_email_template")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailTemplate extends BasicsEntity{


    /**
     * 变量说明
     */
    private String variableDescription;

    /**
     * 名称
     */
    private String name;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 邮件类型
     */
    private String emailType;

    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;

    /**
     * 商品分类id
     */
    private Integer goodsTagId;

    /**
     * 商品名称
     */
    private Integer goodsId;
    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;

    // Getters and setters are generated by Lombok @Data annotation
}
