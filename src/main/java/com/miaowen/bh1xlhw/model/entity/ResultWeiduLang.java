package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 维度语言表
  * @company 武汉秒闻网络科技有限公司

 */
@Data
@TableName("po_result_weidu_lang")
public class ResultWeiduLang implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 维度ID
     */
    private Integer weiduId;

    /**
     * 语言类型
     */
    private String lang;

    /**
     * 名称
     */
    private String name;

    /**
     * 结果内容
     */
    private String content;



    /**
     * 建议
     */
    private String proposal;


    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
