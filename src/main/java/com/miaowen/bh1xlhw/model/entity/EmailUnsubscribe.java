package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_email_unsubscribe")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailUnsubscribe extends BasicsEntity{
    /**
     * 邮箱
     */
    private String email;

    /**
     * 前端包名
     */
    private String webPackageName;

    /**
     * 退订/取消退订时间
     */
    private LocalDateTime time;

    /**
     * 退订状态，1退订,0未退订
     */
    private Integer status;

}
