package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运营主管
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_operation_manager
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_operation_manager")
@Data
public class OperationManager extends BasicsEntity {
    /**
     * 工号
     */
    private String workNo;

    /**
     * 主管名称
     */
    private String name;
}
