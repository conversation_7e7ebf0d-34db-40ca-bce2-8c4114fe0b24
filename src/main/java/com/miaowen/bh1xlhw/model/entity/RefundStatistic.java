package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 退款统计表,每天统计前一天的数据
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_refund_statistic
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_refund_statistic")
@Data
public class RefundStatistic extends BasicsEntity {
    /**
     * 退款金额
     */
    private Integer refundAmount;

    /**
     * 统计日期
     */
    private LocalDate statisticDate;
}
