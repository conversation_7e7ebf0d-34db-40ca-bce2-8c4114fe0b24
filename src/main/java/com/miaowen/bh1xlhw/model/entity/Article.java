package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 分值说明表实体类
 */
@Data
@TableName("po_article")
public class Article extends BasicsEntity {

    /**
     * 标题
     */
    private String title;

    /**
     * 文章类型
     */
    private String articleType;

    /**
     * 商品类型  1:多语言,2:繁体
     */
    private Integer type;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态  状态 0禁用 1启用
     */
    private Integer status;


}