package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 运营人员表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_operation_user
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_operation_user")
@Data
public class OperationUser extends BasicsEntity {
    /**
     * 工号
     */
    private String workNo;

    /**
     * 员工名称
     */
    private String name;

    /**
     * 领导id 对应运营主管表的用户id
     */
    private Integer pid;
}
