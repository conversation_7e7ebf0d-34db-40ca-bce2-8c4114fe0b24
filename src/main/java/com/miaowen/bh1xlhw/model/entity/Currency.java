package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;

/**
 * 角色表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_currency
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_currency")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Currency extends BasicsEntity {
    /**
     * 货币名
     */
    private String name;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 货币单位
     */
    private String currencyUnit;

    /**
     * 货币符号
     */
    private String currencySymbol;
    /**
     * 精度
     */
    private Integer degreeAccuracy;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;


    /**
     * 状态0停用1正常
     */
    private Integer status;


}
