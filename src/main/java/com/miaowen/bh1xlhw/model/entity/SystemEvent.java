package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @description: 时间实体
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/7 10:51
 */
@EqualsAndHashCode()
@TableName(value = "po_track_event")
@Data
public class SystemEvent {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 事件名称
     */
    private String name;

    /**
     * 事件代码
     */
    private String code;

    /**
     * 事件类型code
     */
    private String typeCode;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Boolean status;

}
