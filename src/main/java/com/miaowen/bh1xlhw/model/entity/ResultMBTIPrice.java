package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * mbti价格设置
 */
@Data
@TableName("po_result_mbti_price")
public class ResultMBTIPrice extends BasicsEntity {

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 0：多语言 1：繁体
     */
    private Integer type;

    /**
     * 职场优势价格
     */
    private Integer careerAdvantagesPrice;

    /**
     * 八维报告价格
     */
    private Integer eightDimensionalReportPrice;

    /**
     * 恋爱报告价格
     */
    private Integer loveReportPrice;


}