package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/21 14:28
 */
@Data
@TableName("po_email_promotion_send_record")
public class EmailPromotionSendRecord extends BasicsEntity {

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 推送对象 1:已支付邮箱  2：未支付邮箱
     */
    private Integer proObj;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 推广模板id
     */
    private Integer emailPromotionTemplateId;

    /**
     * 推送总量
     */
    private Integer totalNum;

    /**
     * 成功数量
     */
    private Integer successNum;

    /**
     * 失败数量
     */
    private Integer failNum;

    /**
     * 推送人
     */
    private String proPerson;
}
