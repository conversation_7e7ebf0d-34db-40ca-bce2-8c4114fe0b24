package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 角色表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName acc_role
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_acc_role")
@Data
public class AccRole extends BasicsEntity {
    /**
     * 角色名
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否是超管0普通用户1超管
     */
    private Integer isSuperAdmin;

    /**
     * 角色状态0停用1正常
     */
    private Integer status;


}
