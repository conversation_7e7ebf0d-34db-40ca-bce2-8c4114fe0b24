package com.miaowen.bh1xlhw.model.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * BaseOrder :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-29
 */
@Data
public class BaseOrder {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 内部唯一订单号
     */
    private String outTradeNo;

    /**
     * 捕获id，用于退款
     */
    private String captureId;

    /**
     * 提供给三方的内部订单号在out_trade_no后拼一串随机数
     */
    private String outTradePayNo;

    /**
     * 第三方订单号
     */
    private String thirdOutTradeNo;

    /**
     * pc:二维码支付，MWEB:h5支付
     */
    private String tradeType;

    /**
     * 付款美元金额,美分单位
     */
    private Integer usdAmount;
    /**
     * 美元汇率
     */
    private BigDecimal usdExchangeRate;
    /**
     * 商品金额,单位分
     */
    private Integer amount;

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 支付金额,单位分
     */
    private Integer paymentAmount;

    /**
     * 退款金额,单位分
     */
    private Integer refundAmount;

    /**
     * 支付货币类型
     */
    private String paymentCurrency;


    /**
     * 支付状态 0初始化 1下单 2成功 3等待 4失败 5退款
     */
    private Integer paymentStatus;


    /**
     * 支付时间
     */
    private LocalDateTime paymentTime;


    /**
     * 收款的商户id
     */
    private Integer merchantId;

    /**
     * 支付方式代码
     */
    private String paymentCode;

    /**
     * 商户类型code
     */
    private String merchantTypeCode;

    /**
     * 支付方式id
     */
    private Integer paymentTypeId;

    /**
     * 支付后四位卡号
     */
    private String cardNumber;

    /**
     * 第三方订单邮件
     */
    private String thirdEmail;


    /**
     *
     */
    private LocalDateTime createTime;

    /**
     *
     */
    private LocalDateTime updateTime;

    /**
     *
     */
    private Integer deleteTime;


    @Getter
    @AllArgsConstructor
    public enum PayStatusEnum {
        /**
         * 创建订单
         */
        INIT(0),
        /**
         * 拉起支付
         */
        CREATE(1),
        /**
         * 支付成功
         */
        SUCCESS(2),
        /**
         * 等待用户支付
         */
        PENDING(3),
        /**
         * 支付失败
         */
        FAILED(4),
        /**
         * 已退款
         */
        REFUND(5),
        /**
         * 后台赠送成功订单
         */
        ADMIN_SUCCESS(6),
        ;
        private final Integer value;
    }

}
