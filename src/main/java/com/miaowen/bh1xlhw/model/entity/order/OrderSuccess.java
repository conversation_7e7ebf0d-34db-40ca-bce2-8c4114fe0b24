package com.miaowen.bh1xlhw.model.entity.order;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 交易订单成功表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_order_success
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_order_success")
@Data
public class OrderSuccess extends Order {

}
