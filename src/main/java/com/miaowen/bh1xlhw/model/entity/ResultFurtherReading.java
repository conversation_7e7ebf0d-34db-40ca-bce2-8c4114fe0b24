package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 错题解析实体类
 */
@Data
@EqualsAndHashCode()
@TableName("po_result_further_reading")
public class ResultFurtherReading {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间(软删除)
     */
    private Date deleteTime;

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 0：多语言 1：繁体
     */
    private Integer type;;

    /**
     * 题号
     */
    private String title;

    /**
     * 结果内容
     */
    private String content;

    /**
     * 语言类型
     */
    private String lang;
}