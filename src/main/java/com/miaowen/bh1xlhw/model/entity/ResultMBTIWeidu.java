package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * mbti维度
 */
@Data
@TableName("po_result_mbti_weidu")
public class ResultMBTIWeidu extends BasicsEntity {

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 0：多语言 1：繁体
     */
    private Integer type;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 维度名称
     */
    private String name;

    /**
     * 分值
     */
    private String score;

    /**
     * 基本画像
     */
    private String basicPortrait;

    /**
     * 个人成长
     */
    private String personalGrowth;

    /**
     * 职场小结
     */
    private String workplaceSummary;

    /**
     * 恋爱锦囊
     */
    private String loveTips;

    /**
     * 职场优势
     */
    private String careerAdvantages;

    /**
     * 八维报告
     */
    private String eightDimensionalReport;

    /**
     * 恋爱报告
     */
    private String loveReport;

}