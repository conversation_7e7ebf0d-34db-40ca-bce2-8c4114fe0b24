package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 句子表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_sentence
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_sentence")
@Data
public class Sentence extends BasicsEntity {
    //句子内容
    private String content;

    //前台显示句子内容
    private String frontContent;

    //排序
    private Integer sort;

}
