package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代理商id
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_agent
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_agent")
@Data
public class Agent extends BasicsEntity {
    /**
     * 代理商名字
     */
    private String name;

    /**
     * 结算方式1:月结,2:预付
     */
    private Integer settleType;
}