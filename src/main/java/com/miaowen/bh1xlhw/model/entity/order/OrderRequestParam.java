package com.miaowen.bh1xlhw.model.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单广告请求参数
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_order_request_param
 */
@TableName(value ="po_order_request_param")
@Data
public class OrderRequestParam {
    /**
     * pk
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 广告请求参数
     */
    private String adParam;

    /**
     * 
     */
    private LocalDateTime createTime;

    /**
     * 
     */
    private LocalDateTime updateTime;

    /**
     * 
     */
    private Integer deleteTime;
}
