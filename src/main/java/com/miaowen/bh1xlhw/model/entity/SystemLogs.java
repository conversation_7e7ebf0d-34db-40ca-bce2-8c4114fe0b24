package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import lombok.*;

/**
 * 操作日志表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_logs
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_logs")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SystemLogs extends BasicsEntity {
    /**
     * 管理员id
     */
    private Integer adminUserId;

    /**
     * 管理员用户名
     */
    private String adminUsername;

    /**
     * token
     */
    private String token;

    /**
     * 负责响应的服务名
     */
    private String server;

    /**
     * 负责响应的端口地址
     */
    private String port;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求uri
     */
    private String uri;

    /**
     * 请求body
     */
    private String body;

    /**
     * 响应时间
     */
    private String ms;

    /**
     * 请求代理
     */
    private String agent;

    /**
     * 请求ip
     */
    private String ip;

    /**
     * 异常
     */
    private String abnormal;

    /**
     * 请求开始时间
     */
    private LocalDateTime startTime;

    /**
     * 请求结束时间
     */
    private LocalDateTime endTime;
}
