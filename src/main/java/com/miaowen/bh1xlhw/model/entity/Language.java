package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 角色表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_language
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_language")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Language extends BasicsEntity {
    //语言名称
    private String name;

    //前台语言
    private String front;

    //语言代码
    @TableField(value = "code")
    private String code;

    //排序值
    private Integer sort;

    //是否默认 0不默认 1默认
    private Integer isDefault;

    //状态 0禁用 1正常使用
    private Integer status;


}
