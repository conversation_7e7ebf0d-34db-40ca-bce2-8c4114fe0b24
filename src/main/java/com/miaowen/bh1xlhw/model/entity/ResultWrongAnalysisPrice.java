package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 错题解析实体类
  * @company 武汉秒闻网络科技有限公司

 */
@Data
@TableName("po_result_wrong_analysis_price")
public class ResultWrongAnalysisPrice extends BasicsEntity{


    /**
     * 分类标签
     */
    private String tag;

    /**
     * 名称
     */
    private String priceName;

    /**
     * 描述
     */
    private String priceDesc;

    /**
     * 说明
     */
    private String priceExplain;


    /**
     * 语言类型
     */
    private String lang;

    /**
     * 1：多语言 2：繁体
     */
    private Integer type;


    /**
     * 售价 （美元）目前只有美元一种
     */
    private BigDecimal sale;

}
