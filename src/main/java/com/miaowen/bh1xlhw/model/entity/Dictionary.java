package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;




/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/20 10:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("po_system_dictionary")
@EqualsAndHashCode(callSuper = true)
public class Dictionary extends BasicsEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 数据值
     */
    private String code;

    /**
     * 备注
     */
    private String description;

    /**
     * 关联字典类型Id
     */
    private Integer dictionaryTypeId;

    /**
     * 关联字典类型Id
     */
    private Integer sort;

    /**
     * 状态0禁用1启用
     */
    private Integer status;

}
