package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分值管理详情表
  * @company 武汉秒闻网络科技有限公司

 */
@Data
@TableName("po_score_management_detail")
public class ScoreManagementDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 分值管理ID
     */
    private Integer scoreId;

    /**
     * 选项
     */
    private Integer examOption;

    /**
     * 结果
     */
    private String examResult;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
