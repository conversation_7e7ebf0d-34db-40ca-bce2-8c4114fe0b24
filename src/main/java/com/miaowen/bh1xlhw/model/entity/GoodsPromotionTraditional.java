package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 推广商品繁体
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_goods_promotion_traditional
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_goods_promotion_traditional")
@Data
public class GoodsPromotionTraditional extends BasicsEntity {
    /**
     * 推广id
     */
    private String tgId;

    /**
     * 用途
     */
    private String application;

    /**
     * 商品分类id
     */
    private Integer goodsTypeId;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 对应繁体商品id
     */
    private Integer goodsId;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 运营主管id
     */
    private Integer operationManagerId;

    /**
     * 运营人员id
     */
    private Integer operationId;

    /**
     * 域名id
     */
    private Integer domainId;

    /**
     * 广告账户id
     */
    private Integer advertiseAccountId;

    /**
     * 推广状态0否，1是
     */
    private Integer promotionStatus;

    /**
     * 价格配置id
     */
    private Integer priceId;


    /**
     * 文章id 逗号分割
     */
    private String articleId;

    /**
     * 原价支付通道,0表示默认
     */
    private Integer pricePaymentChannel;

    /**
     * 红包1支付通道,0表示默认
     */
    private Integer red1PaymentChannel;

    /**
     * 红包2支付通道,0表示默认
     */
    private Integer red2PaymentChannel;

    /**
     * 广告配置信息
     */
    private String advertiseConfigInfo;


}
