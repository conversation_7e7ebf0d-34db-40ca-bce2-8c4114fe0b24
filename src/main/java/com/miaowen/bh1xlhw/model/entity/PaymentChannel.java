package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付通道配置
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_payment_channel
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_payment_channel")
@Data
public class PaymentChannel extends BasicsEntity {
    /**
     * 支付方式名称
     */
    private String name;
    /**
     * 支付方式id
     */
    private Integer cardPaymentId;

    /**
     * 苹果支付支付方式id
     */
    private Integer applePaymentId;

    /**
     * 谷歌支付方式id
     */
    private Integer googlePaymentId;
    /**
     * PayPal支付方式
     */
    private Integer paypalPaymentId;
    /**
     * 支付宝支付方式
     */
    private Integer alipayPaymentId;
    /**
     * 微信支付方式
     */
    private Integer wechatPayPaymentId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 支付方式排序
     */
    private String paymentSort;

    /**
     * 默认状态 1默认,0非默认,所有记录只能又一个状态为1的
     */
    private Integer defaultStatus;

    /**
     * 预览图
     */
    private String imageUrl;
}
