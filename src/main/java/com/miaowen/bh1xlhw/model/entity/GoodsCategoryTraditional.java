package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 商品类型多语言表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_goods_category_traditional
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_goods_category_traditional")
@Data
public class GoodsCategoryTraditional extends BasicsEntity {
    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类标签
     */
    private String type;
}
