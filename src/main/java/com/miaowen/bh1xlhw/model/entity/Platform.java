package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 代理商
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_platform
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_platform")
@Data
public class Platform extends BasicsEntity {
    /**
     * 代理商id
     */
    private Integer agentId;
    /**
     * 平台名称
     */
    private String name;

    /**
     * 平台类型
     */
    private String platformType;
    /**
     * 来源
     */
    private String source;

    /**
     * 运营方式1:自运营，2:代运营
     */
    private Integer operationType;

    /**
     * 返点
     */
    private BigDecimal rebate;
}
