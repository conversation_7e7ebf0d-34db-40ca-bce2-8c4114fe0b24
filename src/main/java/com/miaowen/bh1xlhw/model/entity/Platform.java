package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 平台管理
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_platform
 */
@TableName(value ="po_platform")
@Data
public class Platform  {

    @TableId(type = IdType.AUTO)
    private Integer id;


    /**
     * 平台名称
     */
    private String name;

    /**
     * 平台类型Google,Facebook,TikTok
     */
    private String type;

    /**
     * 来源
     */
    private String source;

    private LocalDateTime createTime;

    private LocalDateTime updateTime;

    private Integer deleteTime;

}
