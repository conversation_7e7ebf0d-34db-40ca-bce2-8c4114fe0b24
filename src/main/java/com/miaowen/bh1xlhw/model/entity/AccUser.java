package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 后台用户表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName acc_user
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_acc_user")
@Data
public class AccUser extends BasicsEntity {
    /**
     * 用户名
     */
    private String username;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 密码强度
     */
    private Integer passwordRank;

    /**
     * 用户状态0停用1正常
     */
    private Integer status;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像
     */
    private String avatar;
}
