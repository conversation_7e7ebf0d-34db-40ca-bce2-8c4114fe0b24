package com.miaowen.bh1xlhw.model.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 事件日志实体类
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08
 */
@Data
@EqualsAndHashCode()
@TableName("po_event_log")
public class EventLog {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品类型,1:多语言,2:繁体
     */
    private Integer goodsType;

    /**
     * 推广id
     */
    private String tgid;

    /**
     * 来源标识
     */
    private String source;


    /**
     * 事件code
     */
    private String eventCode;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 推广平台
     */
    private String platformCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;
}
