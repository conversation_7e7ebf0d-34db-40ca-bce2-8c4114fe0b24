package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 未支付邮件发送记录表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_email_order_un_paid
 */
@TableName(value ="po_email_order_unpaid")
@Data
public class EmailOrderUnpaid extends BasicsEntity {
    /**
     * 邮箱
     */
    private String email;

    /**
     * 订单创建时间
     */
    private LocalDateTime orderCreateTime;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 订单号
     */
    private String outTradeNo;

    /**
     * 订单语言
     */
    private String languageCode;

    /**
     * 域名
     */
    private String domain;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品类型,1:多语言,2:繁体
     */
    private Integer goodsType;
}
