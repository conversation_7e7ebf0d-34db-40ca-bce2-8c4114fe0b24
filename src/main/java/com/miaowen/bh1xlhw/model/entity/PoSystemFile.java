package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 文件表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_file
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_file")
@Data
public class PoSystemFile extends BasicsEntity {
    private Integer folderId;
    private String name;
    private String contentType;
    private String url;
    private Integer sort;
    private Integer isDefault;
    private Integer status;
}
