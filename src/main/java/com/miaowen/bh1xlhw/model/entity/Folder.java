package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/24 16:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("po_system_folder")
@EqualsAndHashCode(callSuper = true)
public class Folder extends BasicsEntity {
    //组名字
    private String name;

    /**
     * 文件分组id
     */
    private Integer folderId;

    //排序值
    private Integer sort;

    //是否默认 0不默认 1默认
    @NotNull
    @Range(min = 0, max = 1)
    private Integer isDefault;

    //状态 0禁用 1正常使用
    @NotNull
    @Range(min = 0, max = 1)
    private Integer status;

}
