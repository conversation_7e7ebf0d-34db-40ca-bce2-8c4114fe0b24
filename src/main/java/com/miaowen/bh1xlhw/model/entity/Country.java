package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 角色表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_currency
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_country")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Country extends BasicsEntity {
    /**
     * 国家名称
     */
    private String name;

    /**
     * 国家代码
     */
    private String code;

    /**
     * 所属大洲代码
     */
    private String continentCode;


    /**
     * 排序值
     */
    private Integer sort;


    /**
     * 状态0停用1正常
     */
    private Integer status;


}
