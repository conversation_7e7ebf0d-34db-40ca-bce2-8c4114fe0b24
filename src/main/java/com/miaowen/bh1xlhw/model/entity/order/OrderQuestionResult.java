package com.miaowen.bh1xlhw.model.entity.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 交易订单答题结果,交易订单的水平拆分表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_order_question_result
 */
@TableName(value ="po_order_question_result")
@Data
public class OrderQuestionResult{
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 支付状态
     */
    private Integer paymentStatus;

    /**
     * 临时用户id
     */
    private String logId;

    /**
     * 用户昵称
     */
    private String username;

    /**
     * 分数
     */
    private String score;


    /**
     * 答题列表,按顺序,分割id
     */
    private String resultQuestion;

    /**
     * 答题选项列表,按顺序,分割id
     */
    private String resultAnswer;

    /**
     * 答题完成时间
     */
    private Integer completionTime;

    /**
     *
     */
    private LocalDateTime createTime;

    /**
     *
     */
    private LocalDateTime updateTime;

    /**
     *
     */
    private Integer deleteTime;
}
