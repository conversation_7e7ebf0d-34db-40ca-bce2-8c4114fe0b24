package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 支付方式表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_payment
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_payment")
@Data
public class SystemPayment extends BasicsEntity {
    /**
     * 支付名称
     */
    private String name;

    /**
     * 商户ID
     */
    private Integer merchantId;

    /**
     * 支付方式的code
     */
    private String paymentCode;

    /**
     * 商户类型
     */
    private String merchantTypeCode;

    /**
     * logo url
     */
    private String logo;

    /**
     * 支付简介
     */
    private String description;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 是否默认 0不默认 1默认
     */
    private Integer isDefault;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;

    /**
     * 国家代码，用,分割
     */
    private String countryJson;

    /**
     * 货币代码用，分割
     */
    private String currencyJson;
}
