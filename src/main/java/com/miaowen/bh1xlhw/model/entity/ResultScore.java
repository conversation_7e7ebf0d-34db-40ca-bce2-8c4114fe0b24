package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分值说明表实体类
  * @company 武汉秒闻网络科技有限公司

 */
@Data
@TableName("po_result_score")
public class ResultScore implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 0：多语言 1：繁体
     */
    private Integer type;

    /**
     * 标题
     */
    private String name;

    /**
     * 最小得分
     */
    private Integer minScore;

    /**
     * 最高得分
     */
    private Integer maxScore;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
