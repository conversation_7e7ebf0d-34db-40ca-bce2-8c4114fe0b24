package com.miaowen.bh1xlhw.model.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@TableName(value ="po_email")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Email extends BasicsEntity {

    //邮箱名称
    @Length(max = 20)
    private String name;
    //发件人名称

    @Length(max = 20)
    private String sendName;

    @Length(max = 30)
    private String company;

    //邮箱类型0公司1个人
    @Range(min = 0, max = 1)
    private Integer type;

    @Length(max = 30)
    private String smtp;

    @Range(min = 1, max = 65535)
    private Integer port;

    @Length(max = 30)
    private String email;

    @Length(max = 30)
    private String password;
    //状态0禁用1启用

    @Range(min = 0, max = 1)
    private Integer status;
    //发送类型0账单报告邮箱1未支付邮箱

    //发送次数
    private Integer sendNumbers;
}
