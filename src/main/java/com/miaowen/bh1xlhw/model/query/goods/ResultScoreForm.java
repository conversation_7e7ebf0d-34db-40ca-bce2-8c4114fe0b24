package com.miaowen.bh1xlhw.model.query.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 分值说明入参
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 16:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultScoreForm {

    /**
     * id 传id代表修改
     */
    private Integer id;

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 名称（以中文名称为准填入）
     */
    private String name;

    /**
     * 最高得分
     */
    private Integer maxScore;

    /**
     * 最小得分
     */
    private Integer minScore;

    /**
     * 创建时间(新增或修改可不传)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private Date createTime;

}
