package com.miaowen.bh1xlhw.model.query.payment;

import lombok.Data;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 09:50:56
 */

@Data
public class PaymentChannelForm {
    /**
     * 支付通道名称
     */
    private String name;
    /**
     * 支付方式id
     */
    private Integer cardPaymentId;

    /**
     * 苹果支付支付方式id
     */
    private Integer applePaymentId;

    /**
     * 谷歌支付方式id
     */
    private Integer googlePaymentId;
    /**
     * paypal支付方式
     */
    private Integer paypalPaymentId;
    /**
     * 支付宝支付方式
     */
    private Integer alipayPaymentId;
    /**
     * 微信支付方式
     */
    private Integer wechatPayPaymentId;
    /**
     * 排序
     */
    private Integer sort;

    /**
     * 支付方式排序
     */
    private List<String> paymentCodeSort;

    /**
     * 默认状态 1默认,0非默认,所有记录只能又一个状态为1的
     */
    private Integer defaultStatus;

    /**
     * 预览图
     */
    private String imageUrl;

}
