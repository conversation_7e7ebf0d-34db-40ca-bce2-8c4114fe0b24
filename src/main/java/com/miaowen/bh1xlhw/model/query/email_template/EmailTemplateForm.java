package com.miaowen.bh1xlhw.model.query.email_template;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商户管理表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_merchant
 */
@Data
public class EmailTemplateForm {

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 删除时间
     */
    private Integer deleteTime;

    /**
     * 变量说明
     */
    private String variableDescription;

    /**
     * 名称
     */
    private String name;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 邮件类型
     */
    private String emailType;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;
    // Getters and setters are generated by Lombok @Data annotation
}
