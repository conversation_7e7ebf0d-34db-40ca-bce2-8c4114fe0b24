package com.miaowen.bh1xlhw.model.query.auth;

import com.miaowen.bh1xlhw.config.annotation.MenuUrl;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * PermissionForm :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-06
 */
@Data
public class PermissionForm {
    private Integer id;
    /**
     * 菜单名称，按钮名称
     */
    @NotBlank
    private String title;

    /**
     * 名字
     */
    private String name;

    /**
     * 路径
     */
    private String path;

    /**
     * 组件
     */
    private String component;

    /**
     * 排序值
     */
    @NotNull
    private Integer sort;

    /**
     * 权限（前台使用，做接口鉴权）
     */
    private String sign;

    /**
     * 权限（后端使用，做接口鉴权）
     */
    // 使用自定义校验注解
    @MenuUrl( message = "URL格式必须为[方法类型:路径]，如 get:/api/users")
    private String apiUrl;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 父节点(对应当前表的所属父节点的ID)
     */
    @NotNull
    private Integer pid;

    /**
     * 权限类型1一级菜单2二级菜单3按钮
     */
    @NotNull
    private Integer type;

    /**
     * 页面状态0显示1隐藏
     */
    @NotNull
    private Integer hidden;

}
