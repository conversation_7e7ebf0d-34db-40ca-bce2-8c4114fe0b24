package com.miaowen.bh1xlhw.model.query.language;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 语言包表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_language_pack
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_language_pack")
@Data
public class LanguagePackForm extends BasicsEntity {
    /**
     * 句子id
     */
    private String sentenceId;

    /**
     * 句子
     */
    private String content;

    /**
     * 语言代码
     */
    private String languageCode;



}
