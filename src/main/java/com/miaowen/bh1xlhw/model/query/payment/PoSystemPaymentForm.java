package com.miaowen.bh1xlhw.model.query.payment;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;

/**
 * System Payment Entity
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
@TableName("po_system_payment")
public class PoSystemPaymentForm extends BasicsEntity {


    /**
     * Payment name
     */
    private String name;

    /**
     * Merchant ID
     */
    private Integer merchantId;

    /**
     * Payment method code
     */
    private String paymentCode;

    /**
     * Merchant type code
     */
    private String merchantTypeCode;

    /**
     * Logo URL
     */
    private String logo;

    /**
     * Payment description
     */
    private String description;

    /**
     * Sort value
     */
    private Integer sort;

    /**
     * Is default (0-not default, 1-default)
     */
    private Integer isDefault;

    /**
     * Status (0-disabled, 1-enabled)
     */
    private Integer status;

    /**
     * Country JSON data
     */
    private String countryJson;

    /**
     * Currency JSON data
     */
    private String currencyJson;
}
