package com.miaowen.bh1xlhw.model.query.logManagement;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/30 14:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BrowseCountForm {

    /**
     * 开始时间
     */
    @NonNull
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @NonNull
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 推广id
     */
    @NonNull
    private String tgid;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 来源标识
     */
    private String source;
}
