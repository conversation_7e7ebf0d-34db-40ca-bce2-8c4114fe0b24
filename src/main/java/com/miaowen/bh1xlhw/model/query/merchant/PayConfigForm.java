package com.miaowen.bh1xlhw.model.query.merchant;


/**
 * @company 武汉秒闻网络科技有限公司
 */
import cn.hutool.json.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PayConfigForm {

    @NotNull
    private Integer merchantId;

    private JSONObject configJsonData;

}
