package com.miaowen.bh1xlhw.model.query.goods;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/23 17:17
 */
@Data
public class ResultWrongAnalysisPriceForm {

    /**
     * 标签
     */
    private String tag;

    /**
     * 价格
     */
    private BigDecimal sale;

    private List<Form> form;

    @Data
    public static class Form{
        /**
         * 名称
         */
        private String name;

        /**
         * 描述
         */
        private String desc;

        /**
         * 说明
         */
        private String explain;

        /**
         * 语言类型
         */
        private String lang;
    }
}
