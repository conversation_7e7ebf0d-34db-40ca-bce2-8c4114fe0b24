package com.miaowen.bh1xlhw.model.query.email_template;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/18 17:39
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmailTemplateContentForm  {

    private Integer id;

    //语言id
    @NotNull
    private Integer languageId;

    //邮件模板id
    private Integer emailTemplateId;

    //邮件标题
//    @NotBlank
    @Length(max = 30)
    private String title;

    //邮件内容
//    @NotBlank
    private String content;

}
