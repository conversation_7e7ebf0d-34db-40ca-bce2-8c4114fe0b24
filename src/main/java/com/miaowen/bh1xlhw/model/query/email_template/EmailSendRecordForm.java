package com.miaowen.bh1xlhw.model.query.email_template;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

import java.util.Date;

/**
 * 通知邮件记录
  * @company 武汉秒闻网络科技有限公司

 */
@Data
public class EmailSendRecordForm extends PageForm {


    /**
     * 邮件标题
     */
    private String title;

    /**
     * 收件人邮箱
     */
    private String email;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 邮件类型(通知场景)
     */
    private String goodsTypeCode;

    /**
     * 发送状态1：成功，0失败
     */
    private Integer sendStatus;

    /**
     * 开始时间
     */
    private String startDate;
    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 订单创建月份
     */
    private String month;

    /**
     * 订单号
     */
    private String outTradeNo;



}
