package com.miaowen.bh1xlhw.model.query.email_template;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商户管理表
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_merchant
 */
@Data
public class EmailTemplatePageForm extends PageForm {

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 删除时间
     */
    private Integer deleteTime;

    /**
     * 名称
     */
    private String name;

    /**
     * 模板类型
     */
    private String templateType;


    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;

    /**
     * 商品分类id
     */
    private String goodsTag;

    /**
     * 商品id 此处传的是id
     */
    private String goodsName;

    /**
     * 通知场景
     */
    private String goodsTypeCode;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;
    // Getters and setters are generated by Lombok @Data annotation
}
