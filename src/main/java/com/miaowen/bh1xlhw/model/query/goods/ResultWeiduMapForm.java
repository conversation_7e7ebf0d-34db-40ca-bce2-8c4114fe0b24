package com.miaowen.bh1xlhw.model.query.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 16:52
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultWeiduMapForm {
    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 维度ID
     */
    private Integer weiduId;

    /**
     * 名称
     */
    private String name;

    /**
     * 总分
     */
    private Integer score;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private Date createTime;
}
