package com.miaowen.bh1xlhw.model.query.goods;

import lombok.Data;
import org.apache.catalina.LifecycleState;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/6/4 16:18
 */
@Data
public class ScoreManagementForm {

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 分值设置
     */
    private List<Score> scores;


    @Data
    public static class Score{

        /**
         * 题号  题号：选项  一对多
         */
        private Integer examNo;

        private List<Detail> details;
    }

    @Data
    public static class Detail{
        /**
         * 选项
         */
        private Integer examOption;

        /**
         * 答案/结果分值
         */
        private String examResult;
    }

}
