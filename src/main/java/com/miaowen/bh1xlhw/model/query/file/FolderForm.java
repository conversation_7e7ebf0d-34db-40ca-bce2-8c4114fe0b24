package com.miaowen.bh1xlhw.model.query.file;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.*;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/24 16:31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("po_system_folder")
@EqualsAndHashCode(callSuper = true)
public class FolderForm extends BasicsEntity {

    //组名字
    private String name;

    /**
     * 文件分组id
     */
    private String folderId;

    //排序值
    private Integer sort;

    //是否默认 0不默认 1默认
    @Range(min = 0, max = 1)
    private Integer isDefault;

    //状态 0禁用 1正常使用
    @NotNull
    @Range(min = 0, max = 1)
    private Integer status;

}
