package com.miaowen.bh1xlhw.model.query.adManagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 删除表单
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 9:33
 */
@Data
public class AdBackfillDeleteForm {
    /**
     * 删除id
     */
    private Integer id;
    /**
     * 数据创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
