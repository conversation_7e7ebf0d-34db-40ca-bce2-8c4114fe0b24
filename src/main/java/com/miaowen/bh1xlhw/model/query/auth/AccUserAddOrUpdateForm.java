package com.miaowen.bh1xlhw.model.query.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 新增或修改用户专用传参Form
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/18 15:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccUserAddOrUpdateForm {

    private Integer id;

    /**
     * 真实姓名
     */
    @NotBlank
    private String realName;

    /**
     * 用户名
     */
    @NotBlank
    @Pattern(regexp = "^[^@]*$", message = "非法字符：@")
    private String username;

    /**
     * 邮箱
     */
    @Email
    private String email;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 用户状态
     */
    private Integer status;

    /**
     * 绑定角色
     */
    private List<Integer> roles;

    /**
     * 头像
     */
    private String avatar;

}
