package com.miaowen.bh1xlhw.model.query.language;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * @ClassName UpdateIsDefaultForm
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 17:01
 */
@Data
public class UpdateIsDefaultForm {

    private Integer id;

    //语言代码
    @Length(max = 20)
    private String code;

    private int isDefault;

    private int status;

}
