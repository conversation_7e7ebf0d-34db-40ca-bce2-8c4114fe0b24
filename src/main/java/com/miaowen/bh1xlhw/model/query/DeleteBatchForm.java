package com.miaowen.bh1xlhw.model.query;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * @Description 批量删除表单基类
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2024/3/19 15:28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteBatchForm {

    /**
     * 10万以下：可以直接使用 IN
     * 10-100万：建议分批，每批500-1000
     * 100万以上：建议分批 + 多线程 或 离线处理
     * 做个批量处理数据保护，如果需要大批量删除，那就扩展成分批次，但是每次必须要有个阈值，建议是500
     */
    @Size(max = 500)
    private List<Integer> ids;
}
