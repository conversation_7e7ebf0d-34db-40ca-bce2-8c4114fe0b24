package com.miaowen.bh1xlhw.model.query.email_template;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/21 15:11
 */
@Data
public class EmailOrderUnpaidForm extends PageForm {
    /**
     * 邮箱
     */
    private String email;

    /**
     * 订单语言
     */
    private String languageCode;

    /**
     * 商品类型 1:多语言,2:繁体
     */
    private Integer goodsType;

    /**
     * 商品分类id
     */
    private Integer goodsCategoryId;
    /**
     * 商品id
     */
    private Integer goodsId;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 时区
     */
    private String timezone;

    /**
     * 状态  1为退订 0 为正常
     */
    private String status;
}
