package com.miaowen.bh1xlhw.model.query.goods;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.miaowen.bh1xlhw.model.bo.goods.GoodsBenefitInfoBO;
import lombok.Data;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class GoodsMultilingualForm {
    /**
     * 商品分类id
     */
    private Integer goodsCategoryId;
    /**
     * 显示名称
     */
    private String name;

    /**
     * 试卷题目数量
     */
    private Integer paperNum;
    /**
     * 缩略图
     */
    private String thumbImage;

    /**
     * 风格名称
     */
    private String styleName;

    /**
     * 前端包名
     */
    private String webPackageName;
    /**
     * 价格方案id
     */
    private Integer priceId;

    /**
     * 文章id 逗号分割
     */
    private String articleId;
    /**
     * 商品权益列表
     */
    private List<GoodsBenefitInfoBO> goodsBenefitInfoList = new ArrayList<>();
    /**
     * 多语言列表
     */
    @Valid
    private List<GoodsLanguageForm> goodsLanguageList = new ArrayList<>();
}
