package com.miaowen.bh1xlhw.model.query.operation;

import com.miaowen.bh1xlhw.model.entity.Price;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DecimalUtil;
import lombok.Data;

import java.math.BigDecimal;

/**
 * PriceForm :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
public class PriceForm {
    /**
     * 价格方案名称
     */
    private String name;

    /**
     * 价格类型,1:单价,2:双价,3:三价
     */
    private Integer type;

    /**
     * 低档价格,美分单位
     */
    private BigDecimal price1;

    /**
     * 低档红包1价格,美分单位
     */
    private BigDecimal red1Price1;

    /**
     * 低档红包2价格,美分单位
     */
    private BigDecimal red2Price1;

    /**
     * 低档划线价格,美分单位
     */
    private BigDecimal originPrice1;

    /**
     * 中档价格,美分单位
     */
    private BigDecimal price2;

    /**
     * 中档红包1价格,美分单位
     */
    private BigDecimal red1Price2;

    /**
     * 中档红包2价格,美分单位
     */
    private BigDecimal red2Price2;

    /**
     * 中档划线价格,美分单位
     */
    private BigDecimal originPrice2;

    /**
     * 高档价格,美分单位
     */
    private BigDecimal price3;

    /**
     * 高档红包1价格,美分单位
     */
    private BigDecimal red1Price3;

    /**
     * 高档红包2价格,美分单位
     */
    private BigDecimal red2Price3;

    /**
     * 高档划线价格,美分单位
     */
    private BigDecimal originPrice3;

    /**
     * roi基数
     */
    private BigDecimal baseRoi;

    /**
     * roi目标值
     */
    private BigDecimal targetRoi;

    /**
     * 红包1 美分单位
     */
    private BigDecimal redPackage1;

    /**
     * 红包2 美分单位
     */
    private BigDecimal redPackage2;

    /**
     * 红包1是否回传
     */
    private Integer redPackageBack1;

    /**
     * 红包2是否回传
     */
    private Integer redPackageBack2;

    /**
     * 邮箱是否回传
     */
    private Integer emailBack;

    public Price transform() {
        Price price = BeanUtils.copy(this, Price.class);
        //低挡
        price.setPrice1(DecimalUtil.toStoredValue(this.getPrice1()));
        price.setOriginPrice1(DecimalUtil.toStoredValue(this.getOriginPrice1()));
        price.setRed1Price1(DecimalUtil.toStoredValue(this.getRed1Price1()));
        price.setRed2Price1(DecimalUtil.toStoredValue(this.getRed2Price1()));

        //中挡
        price.setPrice2(DecimalUtil.toStoredValue(this.getPrice2()));
        price.setOriginPrice2(DecimalUtil.toStoredValue(this.getOriginPrice2()));
        price.setRed1Price2(DecimalUtil.toStoredValue(this.getRed1Price2()));
        price.setRed2Price2(DecimalUtil.toStoredValue(this.getRed2Price2()));

        //高挡
        price.setPrice3(DecimalUtil.toStoredValue(this.getPrice3()));
        price.setOriginPrice3(DecimalUtil.toStoredValue(this.getOriginPrice3()));
        price.setRed1Price3(DecimalUtil.toStoredValue(this.getRed1Price3()));
        price.setRed2Price3(DecimalUtil.toStoredValue(this.getRed2Price3()));

        price.setRedPackage1(DecimalUtil.toStoredValue(this.getRedPackage1()));
        price.setRedPackage2(DecimalUtil.toStoredValue(this.getRedPackage2()));
        return price;
    }
}
