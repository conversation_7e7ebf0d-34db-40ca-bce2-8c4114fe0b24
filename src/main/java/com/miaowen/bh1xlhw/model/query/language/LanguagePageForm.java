package com.miaowen.bh1xlhw.model.query.language;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import java.time.LocalDateTime;

/**
 * @ClassName LanguageForm
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 17:01
 */
@Data
public class LanguagePageForm extends PageForm {

    private Integer id;

    //语言名称
    @Length(max = 20)
    private String name;

    //前台语言
    @Length(max = 20)
    private String front;

    //语言代码
    @Length(max = 20)
    private String code;

    //排序值
    private Integer sort;

    //是否默认 0不默认 1默认
    @Range(min = 0, max = 1)
    private Integer isDefault;

    //状态 0禁用 1正常使用
    @Range(min = 0, max = 1)
    private Integer status;

    private LocalDateTime createTime;
}
