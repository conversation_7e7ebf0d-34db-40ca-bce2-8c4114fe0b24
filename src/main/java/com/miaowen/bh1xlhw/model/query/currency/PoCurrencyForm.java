package com.miaowen.bh1xlhw.model.query.currency;

import lombok.Data;

import java.math.BigDecimal;

/**
 * AccRoleForm :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
public class PoCurrencyForm {

    private Integer id;
    /**
     * 角色名
     */
    private String name;
    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 货币单位
     */
    private String currencyUnit;

    /**
     * 货币符号
     */
    private String currencySymbol;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    /**
     * 精度
     */
    private Integer degreeAccuracy;

    /**
     * 角色状态0停用1正常
     */
    private Integer status;
}
