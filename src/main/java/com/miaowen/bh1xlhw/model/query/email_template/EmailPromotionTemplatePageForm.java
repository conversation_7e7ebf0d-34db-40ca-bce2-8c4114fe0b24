package com.miaowen.bh1xlhw.model.query.email_template;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 商户管理表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_merchant
 */
@Data
public class EmailPromotionTemplatePageForm extends PageForm {

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 删除时间
     */
    private Integer deleteTime;

    /**
     * 名称
     */
    private String name;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 商品分类
     */
    private Integer goodsTag;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;
    // Getters and setters are generated by Lombok @Data annotation
}
