package com.miaowen.bh1xlhw.model.query.system;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

/**
 * @Description 专门用于修改状态的form
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/20 9:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpdateStateForm {

    @NotNull
    private Integer id;

    @NotNull
    @Range(min = 0, max = 1)
    private Integer status;

}
