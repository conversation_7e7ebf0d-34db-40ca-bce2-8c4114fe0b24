package com.miaowen.bh1xlhw.model.query.goods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 14:47
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultReadingForm {

    /**
     * 分类标签
     */
    private String tag;

    private List<Read> details;

    @Data
    public static class Read {

        /**
         * 标题
         */
        private String title;

        /**
         * 内容
         */
        private String content;


        /**
         * 语言类型 zh-cn  | en ...
         */
        private String lang;
    }


}
