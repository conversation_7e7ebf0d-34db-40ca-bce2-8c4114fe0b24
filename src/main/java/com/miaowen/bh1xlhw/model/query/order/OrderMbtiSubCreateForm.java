package com.miaowen.bh1xlhw.model.query.order;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * OrderMbtiSubCreateForm :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-17
 */
@Data
public class OrderMbtiSubCreateForm {
    /**
     * 主订单号
     */
    @NotBlank(message = "主订单号不能为空")
    @NotNull(message = "主订单号不能为空")
    private String mainOutTradeNo;

    /**
     * 子商品类型,1:mbti职场优势,2:mbti八维报告3:mbti恋爱报告
     */
    private List<String> mbtiReportList;
}
