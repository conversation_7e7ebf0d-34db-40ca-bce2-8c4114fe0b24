package com.miaowen.bh1xlhw.model.query.goods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 维度详情
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 16:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultWeiduDetailForm extends ResultWeiduBaseForm {

    private List<WeiduLang> details;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WeiduLang {
        /**
         * 语言类型
         */
        private String lang;

        /**
         * 名称
         */
        private String name;

        /**
         * 结果内容
         */
        private String content;

        /**
         * 建议
         */
        private String proposal;


    }

}
