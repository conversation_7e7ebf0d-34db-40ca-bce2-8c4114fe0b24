package com.miaowen.bh1xlhw.model.query.email_template;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/21 15:11
 */
@Data
public class EmailOrderUnpaidQueryForm  {
    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private Date createTime;

}
