package com.miaowen.bh1xlhw.model.query.email_template;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Builder;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/18 17:39
 */
@Data
@Builder
public class EmailPromotionTemplateContentPageForm extends PageForm {

    //语言id
    @NotNull
    private Integer languageId;

    //邮件模板id
    private Integer emailPromotionTemplateId;

    //邮件标题
//    @NotBlank
    @Length(max = 30)
    private String title;

    //邮件内容
//    @NotBlank
    private String content;

}
