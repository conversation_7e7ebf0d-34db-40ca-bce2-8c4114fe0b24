package com.miaowen.bh1xlhw.model.query.adManagement;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 广告回传记录
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/12 9:26
 */
@Data
public class AdBackfillForm extends PageForm {
    /**
     * 推广id
     */
    private String tuid;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 投放平台编码
     */
    private String platformCode;

    /**
     * 广告值
     */
    private String params;

    /**
     * 页面类型
     */
    private String pageType;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
}
