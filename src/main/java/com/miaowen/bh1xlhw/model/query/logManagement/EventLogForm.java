package com.miaowen.bh1xlhw.model.query.logManagement;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description
 * @Author：huang<PERSON>
 * @Date：2025/5/9 10:38
 */
@Data
public class EventLogForm {
    /**
     * 日志id
     */
    private String logId;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
