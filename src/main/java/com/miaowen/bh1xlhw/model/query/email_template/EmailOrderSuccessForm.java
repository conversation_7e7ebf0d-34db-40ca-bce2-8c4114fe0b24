package com.miaowen.bh1xlhw.model.query.email_template;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 已支付成功邮箱出参
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/21 14:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailOrderSuccessForm extends PageForm {

    /**
     * 邮箱
     */
    private String email;
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 订单语言
     */
    private String languageCode;
    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;
    /**
     * 商品分类 id
     */
    private Integer goodsCategoryId;
    /**
     * 商品名称  传id
     */
    private Integer goodsId;

    /**
     * 状态  1为退订 0 为正常
     */
    private String status;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;


}
