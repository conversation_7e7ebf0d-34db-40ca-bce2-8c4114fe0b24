package com.miaowen.bh1xlhw.model.query.system;

import lombok.*;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/20 10:10
 */
@Data
public class DictionaryTypeForm {
    private Integer id;
    //名称
    @NotBlank
    @Length(max = 20)
    private String name;

    //编码
    @NotBlank
    @Length(max = 20)
    private String code;

    //备注
    @Length(max = 100)
    private String description;

    //排序值
    @NotNull
    private Integer sort;

    //状态0禁用1启用
    @NotNull
    @Range(min = 0, max = 1)
    private Integer status;

}
