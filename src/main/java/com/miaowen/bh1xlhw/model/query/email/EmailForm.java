package com.miaowen.bh1xlhw.model.query.email;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmailForm{

    @TableId(type = IdType.AUTO)
    private Integer id;

    //邮箱名称
    @NotBlank
    @Length(max = 50)
    private String name;
    //发件人名称
    @NotBlank
    @Length(max = 50)
    private String sendName;
    @NotBlank
    @Length(max = 30)
    private String company;
    //邮箱类型0公司1个人
    @NotNull
    @Range(min = 0, max = 1)
    private Integer type;
    @NotBlank
    @Length(max = 30)
    private String smtp;
    @NotNull
    @Range(min = 1, max = 65535)
    private Integer port;
    @NotBlank
    @Length(max = 30)
    private String email;
    @NotBlank
    @Length(max = 30)
    private String password;
    //状态0禁用1启用
    @NotNull
    @Range(min = 0, max = 1)
    private Integer status;

    //发送次数
    @Null
    private Integer sendNumbers;
}
