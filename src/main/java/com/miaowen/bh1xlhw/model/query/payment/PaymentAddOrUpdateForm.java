package com.miaowen.bh1xlhw.model.query.payment;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * System Payment Entity
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentAddOrUpdateForm{
    private Integer id;
    /**
     * 支付名称
     */
    private String name;

    /**
     * 商户ID
     */
    private Integer merchantId;

    /**
     * 支付方式的code
     */
    private String paymentCode;

    /**
     * 商户类型
     */
    private String merchantTypeCode;

    /**
     * logo url
     */
    private String logo;

    /**
     * 支付简介
     */
    private String description;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 是否默认 0不默认 1默认
     */
    private Integer isDefault;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;

    /**
     * 国家代码列表
     */
    private List<String> countryCodeList;

    /**
     * 货币代码列表
     */
    private List<String> currencyCodeList;


}
