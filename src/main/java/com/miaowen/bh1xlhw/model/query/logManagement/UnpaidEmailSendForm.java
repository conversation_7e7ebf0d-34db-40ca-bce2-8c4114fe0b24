package com.miaowen.bh1xlhw.model.query.logManagement;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description 未支付邮件发送 后台入参
 * @Author：huang<PERSON>
 * @Date：2025/5/8 8:49
 */
@Data
public class UnpaidEmailSendForm extends PageForm {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 邮件类型
     */
    private String emailType;
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;

}
