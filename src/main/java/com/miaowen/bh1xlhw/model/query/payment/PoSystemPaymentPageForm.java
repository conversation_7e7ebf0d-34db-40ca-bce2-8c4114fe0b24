package com.miaowen.bh1xlhw.model.query.payment;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

/**
 * System Payment Entity
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class PoSystemPaymentPageForm extends PageForm {


    /**
     * 支付名称
     */
    private String name;

    /**
     * 商户 ID
     */
    private Integer merchantId;

    /**
     * 货币单位
     */
    private String currencyUnit;


    /**
     * 支付类型
     */
    private String paymentCode;

    /**
     * 是否是回收站 1表示回收站
     */
    private Integer isRecycle = 0;
}
