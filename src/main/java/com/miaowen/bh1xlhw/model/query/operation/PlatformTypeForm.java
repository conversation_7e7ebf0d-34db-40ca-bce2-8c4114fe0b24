package com.miaowen.bh1xlhw.model.query.operation;


import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class PlatformTypeForm {
    /**
     * 平台名称
     */
    @NotNull(message = "平台名称不能为空")
    @Size(min = 1, max = 45, message = "平台名称不能超过45字")
    private String name;

    /**
     * 平台类型 Google,Facebook,TikTok
     */
    @NotNull(message = "平台类型不能为空")
    private String type;

    /**
     * 来源
     */
    @NotNull(message = "平台来源不能为空")
    @Size(min = 1, max = 45, message = "平台来源不能超过45字")
    private String source;

}
