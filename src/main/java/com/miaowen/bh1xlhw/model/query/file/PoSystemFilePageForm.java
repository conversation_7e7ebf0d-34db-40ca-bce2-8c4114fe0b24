package com.miaowen.bh1xlhw.model.query.file;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 文件表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_file
 */
@EqualsAndHashCode(callSuper = true)
@TableName(value ="po_system_file")
@Data
public class PoSystemFilePageForm extends BasicsEntity {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private Integer isDeleted;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;
    private Integer deleteTime;
    private Integer folderId;
    private String name;
    private String contentType;
    private String url;
    private Integer sort;
    private Integer isDefault;
    private Integer status;



}
