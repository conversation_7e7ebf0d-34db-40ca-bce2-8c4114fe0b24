package com.miaowen.bh1xlhw.model.query.goods;

import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/6/5 15:51
 */
@Data
public class ResultMBTIPriceForm extends BasicsEntity {


    /**
     * 分类标签
     */
    private String tag;

    /**
     * 0：多语言 1：繁体
     */
    private Integer type;

    /**
     * 职场优势
     */
    private BigDecimal careerAdvantagesPrice;

    /**
     * 八维报告
     */
    private BigDecimal eightDimensionalReportPrice;

    /**
     * 恋爱报告
     */
    private BigDecimal loveReportPrice;

}
