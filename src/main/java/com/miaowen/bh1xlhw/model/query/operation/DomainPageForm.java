package com.miaowen.bh1xlhw.model.query.operation;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/28 19:44
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DomainPageForm extends PageForm {
    /**
     * 域名
     */
    private String domain;
    /**
     * 平台 ID
     */
    private Integer platformId;

    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 商品分类
     */
    private String goodsCategory;
    /**
     * 备注
     */
    private String remark;
}
