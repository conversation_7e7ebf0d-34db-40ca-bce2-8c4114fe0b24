package com.miaowen.bh1xlhw.model.query.email_template;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Date;

/**
 * 邮件推送
 *
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/22 8:52
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailPromotionSendForm {

    /**
     * 推广id
     */
    @NonNull
    private Integer id;

    /**
     * 推送对象 1:已支付邮箱  2：未支付邮箱
     */
    @NonNull
    private Integer proObj;

    /**
     * 推送时间 yyyy-MM
     */
    @NonNull
    private String sendMonth;

    /**
     * 是否过滤用户 0:否  1：是
     */
    @NonNull
    private Integer filterUser;

}
