package com.miaowen.bh1xlhw.model.query.goods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 分值说明详情
 *
 * @Author：huanglong
 * @Date：2025/5/16 17:49
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultJobDetailForm extends ResultJobForm {

    private List<JobScore> details;

    @Data
    public static class JobScore {
        /**
         * 名称
         */
        private String name;
        /**
         * 语言类型 : zh-cn | en ...
         */
        private String lang;

    }
}
