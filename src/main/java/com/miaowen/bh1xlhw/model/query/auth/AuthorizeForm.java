package com.miaowen.bh1xlhw.model.query.auth;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色授权（授予菜单按钮权限）
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthorizeForm {

    /**
     *角色id
     */
    @NotNull
    private Integer roleId;
    /**
     * 权限菜单id
     */
    private List<Integer> permissionIds;

}
