package com.miaowen.bh1xlhw.model.query.language;

import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @ClassName LanguageForm
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 17:01
 */
@Data
public class LanguageForm {

    private Integer id;

    //语言名称
    @NotBlank(message = "语言名称不能为空")
    @Length(max = 20, message = "语言名称长度不能超过20个字符")
    private String name;

    //前台语言
    @NotBlank(message = "前台语言不能为空")
    @Length(max = 20, message = "前台语言长度不能超过20个字符")
    private String front;

    //语言代码
    @NotBlank(message = "语言代码不能为空")
    @Length(max = 20, message = "语言代码长度不能超过20个字符")
    private String code;

    //排序值
    @NotNull(message = "排序值不能为空")
    @Range(min = 0, message = "排序值不能小于0")
    private Integer sort;

    //是否默认 0不默认 1默认
    @NotNull(message = "是否默认不能为空")
    @Range(min = 0, max = 1, message = "是否默认值只能为0或1")
    private Integer isDefault;

    //状态 0禁用 1正常使用
    @NotNull(message = "状态不能为空")
    @Range(min = 0, max = 1, message = "状态值只能为0或1")
    private Integer status;
}
