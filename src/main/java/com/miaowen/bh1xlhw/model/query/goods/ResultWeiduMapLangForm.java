package com.miaowen.bh1xlhw.model.query.goods;

import com.miaowen.bh1xlhw.model.entity.ResultWeiduMapLang;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description
 * @Author：huang<PERSON>
 * @Date：2025/5/19 16:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultWeiduMapLangForm extends ResultWeiduMapForm {

    private List<WeiduMapLang> details;

    @Data
    public static class WeiduMapLang {

        /**
         * 语言类型
         */
        private String lang;

        /**
         * 结果名称
         */
        private String name;

    }
}
