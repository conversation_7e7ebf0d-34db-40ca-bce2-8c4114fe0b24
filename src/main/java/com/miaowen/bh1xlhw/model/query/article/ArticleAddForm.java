package com.miaowen.bh1xlhw.model.query.article;

import com.miaowen.bh1xlhw.model.entity.Article;
import com.miaowen.bh1xlhw.model.entity.ArticleLang;
import com.miaowen.bh1xlhw.model.entity.EmailTemplate;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/23 14:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ArticleAddForm {

    @Valid
    @NotNull
    private Article article;

    @Valid
    @NotNull
    private List<ArticleLang> detail;
}
