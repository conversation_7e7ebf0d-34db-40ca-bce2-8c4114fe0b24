package com.miaowen.bh1xlhw.model.query.goods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;

/**
 * 错题解析详情入参
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 11:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultSettingWrongDetailForm extends ResultSettingWrongBaseForm {

    private List<WrongDetail> details;

    /**
     * 错题解析详情
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class WrongDetail{
        /**
         * 语言类型
         */
        @NonNull
        private String langType;
        /**
         * 语言类型
         */
        private String examExplain;
    }

}
