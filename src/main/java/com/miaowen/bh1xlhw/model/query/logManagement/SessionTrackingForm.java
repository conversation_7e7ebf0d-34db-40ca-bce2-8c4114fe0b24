package com.miaowen.bh1xlhw.model.query.logManagement;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/8 11:43
 */
@Data
public class SessionTrackingForm extends PageForm {
    /**
     * 日志ID
     */
    private String logId;

    /**
     * 环境类型
     */
    private String envType;

    /**
     * 跟踪ID
     */
    private String traceId;

    /**
     * 推广id
     */
    private String tuid;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 来源
     */
    private String source;

    /**
     * 推广平台编码
     */
    private String platformCode;

    /**
     * 开始日期
     */
    private String startTime;

    /**
     * 结束日期
     */
    private String endTime;

    /**
     * 时区
     */
    private String timezone;
}
