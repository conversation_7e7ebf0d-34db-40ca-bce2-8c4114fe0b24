package com.miaowen.bh1xlhw.model.query.email;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

/**
 * 商户管理表
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_merchant
 */
@Data
public class EmailPageForm extends PageForm {

    //邮箱名称
    @Length(max = 20)
    private String name;

    //发件人名称
    @Length(max = 20)
    private String sendName;

    @Length(max = 30)
    private String company;

    //邮箱类型0公司1个人
    @Range(min = 0, max = 1)
    private Integer type;

    @Length(max = 30)
    private String smtp;

    @Range(min = 1, max = 65535)
    private Integer port;

    @Length(max = 30)
    private String email;

    @Length(max = 30)
    private String password;

    //状态0禁用1启用
    @Range(min = 0, max = 1)
    private Integer status;


    //发送次数
    private Integer sendNumbers;
}
