package com.miaowen.bh1xlhw.model.query.currency;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * AccRoleForm :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
public class PoCurrencyPageForm extends PageForm {

    private Integer id;

    /**
     * 货币名称
     */
    private String name;

    /**
     * 货币符号
     */
    private String currencySymbol;

    /**
     * 货币单位
     */
    private String currencyUnit;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 汇率
     */
    private String exchangeRate;

    /**
     * 角色状态0停用1正常
     */
    private Integer status;

    /**
     * 是否是回收站 1表示回收站
     */
    private Integer isRecycle = 0;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
