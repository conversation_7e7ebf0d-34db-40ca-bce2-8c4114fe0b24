package com.miaowen.bh1xlhw.model.query.goods;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 维度基础信息
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 16:48
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultWeiduBaseForm {
    /**
     * ID 传id代表修改
     */
    private Integer id;

    /**
     * 分类标签
     */
    private String tag;

    /**
     * 维度类型
     */
    private String weiduType;

    /**
     * 名称
     */
    private String name;

    /**
     * 题目序号
     */
    private String questionNums;

    /**
     * 总分
     */
    private Integer score;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private Date updateTime;
}
