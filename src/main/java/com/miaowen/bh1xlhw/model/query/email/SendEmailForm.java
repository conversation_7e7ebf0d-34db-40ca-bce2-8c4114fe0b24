package com.miaowen.bh1xlhw.model.query.email;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/19 10:10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SendEmailForm {

    @NotNull
    private Integer fromEmailId;

    //目标邮箱地址
    @NotBlank
    @Length(max = 30)
    private String toEmail;

}