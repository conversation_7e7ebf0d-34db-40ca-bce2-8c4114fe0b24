package com.miaowen.bh1xlhw.model.query.system;

import com.baomidou.mybatisplus.annotation.TableName;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.*;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * AccRoleForm :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
public class DictionaryForm
{
    private Integer id;

    //名称
    @NotBlank
    private String name;

    //数据值
    @NotBlank
    @Length(max = 20)
    private String code;

    //备注
    @Length(max = 100)
    private String description;

    //关联字典类型Id
    @NotNull
    private Integer dictionaryTypeId;

    //排序值
    @NotNull
    private Integer sort;

    //状态0禁用1启用
    @NotNull
    @Range(min = 0, max = 1)
    private Integer status;

}
