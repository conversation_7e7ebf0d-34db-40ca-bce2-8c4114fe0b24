package com.miaowen.bh1xlhw.model.query.order;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrderForm extends PageForm {
    /**
     * 推广Id
     */
    private String tgId;

    /**
     * 主订单号
     */
    private String mainOutTradeNo;

    /**
     * 内部订单号
     */
    private String outTradeNo;

    private String thirdOutTradeNo;

    /**
     * 用户注册邮箱（需符合RFC 5322标准）
     * <p>示例："<EMAIL>"</p>
     */
    private String email;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品分类
     */
    private Integer goodsType;

    /**
     * 时区
     */
    private String timezone = "Asia/Shanghai";

    /**
     * 当前订单状态
     * <p>0初始化 1下单 2成功 3等待 4失败 5退款</p>
     */
    private Integer orderStatus;

    /**
     * 支付方式
     * <p>示例：googlePay, applePay alipay wechat</p>
     */
    private String paymentCode;

    /**
     * 支付方式id
     */
    private Integer paymentId;

    /**
     * 用户使用状态
     */
    private Integer redPackUseStatus;

    /**
     * 红包领取状态
     */
    private Integer redPackGetStatus;

    /**
     * 广告投放的原始平台
     * <p>示例：TikTok, Google, Facebook</p>
     */
    private String platformType;

    /**
     * 支付邮箱
     *
     */
    private String thirdEmail;
    /**
     * 卡后四位
     *
     */
    private String cardNumber;

    /**
     * 来源/渠道
     */
    private String source;

    /**
     * 订单所属国家/地区（ISO 3166标准）
     * <p>示例：CN（中国）, US（美国）</p>
     */
    private String countryCode;

    /**
     * 订单开始时间
     * <p>
     * 格式：yyyy-MM-dd HH:mm:ss<br>
     * 示例："2023-10-15 14:30:00"
     * </p>
     */
    @NotNull(message = "查询开始时间不能为空")
    @NotBlank(message = "查询开始时间不能为空")
    private String startTime;

    /**
     * 订单结束时间
     * <p>
     * 格式：yyyy-MM-dd HH:mm:ss<br>
     * 示例："2023-10-15 14:30:00"
     * </p>
     */
    @NotNull(message = "查询结束时间不能为空")
    @NotEmpty(message = "查询结束时间不能为空")
    private String endTime;

    private Integer goodsId;

    /**
     * 商品id集合（后端用，前端勿传）
     */
    private String goodsIds;

    /**
     * 返回类型 0：正常  1：邮件
     */
    private Integer returnType;

}
