package com.miaowen.bh1xlhw.model.query.merchant;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

/**
 * 商户管理表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_merchant
 */
@Data
public class MerchantPageForm extends PageForm {
    /**
     * 商户名称
     */
    private String name;

    /**
     * 商户类型代码
     */
    private String typeCode;

    /**
     * 支付公司
     */
    private String company;

    /**
     * 支付显示名称
     */
    private String showName;

    /**
     * 配置数据
     */
    private String configData;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;

    /**
     * 商户是否可用
     */
    private Integer merchantEnableStatus;
    /**
     * 是否是回收站 1表示回收站
     */
    private Integer isRecycle = 0;
    // Getters and setters are generated by Lombok @Data annotation
}
