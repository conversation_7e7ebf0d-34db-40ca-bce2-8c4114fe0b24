package com.miaowen.bh1xlhw.model.query.goods;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

/**
 * 错题解析基础入参
 *
 * @Description
 * @Author：huang<PERSON>
 * @Date：2025/5/16 11:42
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultSettingWrongBaseForm {
    /**
     * 标签
     */
    @NonNull
    private String tag;

    /**
     * 题号
     */
    @NonNull
    private String examNum;

    /**
     * 答案
     */
    private String examAnswer;

    /**
     * 分数
     */
    private Integer score;

}
