package com.miaowen.bh1xlhw.model.query.logManagement;

import lombok.Data;

/**
 * @Description 日志追踪入参
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/12 17:08
 */
@Data
public class EventSessionLogParamVo {

    /**
     * 环境类型
     * 0：正常
     * 1：邮件
     */
    private Integer envType;

    /**
     * 事件代码
     */
    private String eventCode;

    /**
     *
     */
    private Integer isNew;

    /**
     * 语言代码
     * 示例: "zh-cn"
     */
    private String lang;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 产品ID
     * 0表示无特定产品
     */
    private Integer productId;

    /**
     * 来源标识
     * 示例: "test"
     */
    private String source;

    /**
     * 推广ID
     */
    private String tuid;
}
