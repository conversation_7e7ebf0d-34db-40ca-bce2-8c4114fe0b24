package com.miaowen.bh1xlhw.model.query.goods;

import com.miaowen.bh1xlhw.model.bo.ad.FacebookConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.GoogleConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.TikTokConfigInfoBO;
import lombok.Data;

/**
 * GoodsPromotionMultilingualForm :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
@Data
public class GoodsPromotionTraditionalForm {

    /**
     * 用途
     */
    private String application;

    /**
     * 商品分类id
     */
    private Integer goodsTypeId;

    /**
     * 对应多语言商品id
     */
    private Integer goodsId;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 广告账户id
     */
    private Integer advertiseAccountId;

    /**
     * 运营主管id
     */
    private Integer operationManagerId;

    /**
     * 运营人员id
     */
    private Integer operationId;

    /**
     * 域名id
     */
    private Integer domainId;

    /**
     * 推广状态0否，1是
     */
    private Integer promotionStatus;

    /**
     * 价格配置id
     */
    private Integer priceId;

    /**
     * 文章id 逗号分割
     */
    private String articleId;

    /**
     * 原价支付通道,0表示默认
     */
    private Integer pricePaymentChannel;

    /**
     * 红包1支付通道,0表示默认
     */
    private Integer red1PaymentChannel;

    /**
     * 红包2支付通道,0表示默认
     */
    private Integer red2PaymentChannel;

    /**
     * 当平台类型是Facebook时,传此参数,Facebook广告配置信息
     */
    private FacebookConfigInfoBO facebookConfigInfo;
    /**
     * 当平台类型是Google时,传此参数,Google广告配置信息
     */
    private GoogleConfigInfoBO googleConfigInfo;
    /**
     * 当平台类型是TikTik时,传此参数,TikTik广告配置信息
     */
    private TikTokConfigInfoBO tikTokConfigInfo;

}
