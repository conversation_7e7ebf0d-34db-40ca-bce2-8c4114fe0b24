package com.miaowen.bh1xlhw.model.query;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Objects;

/**
 * @Description 分页表单基类
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2024/3/19 15:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageForm {

    /**
     * 当前页数
     */
    @Min(value = 1)
    @NotNull(message = "参数pageInt不能为空")
    private Integer pageInt;

    /**
     * 每页数量
     */
    @Min(value = 1)
    @NotNull(message = "参数pageSize不能为空")
    private Integer pageSize;

    public <T> Page<T> qry() {
        if (Objects.isNull(pageInt)){
            pageInt = 1;
        }
        if (Objects.isNull(pageSize)){
            pageSize = 10;
        }

        return new Page<>(pageInt, pageSize);
    }
}
