package com.miaowen.bh1xlhw.model.query.oa;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * StatisticOrderQry :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@Data
public class StatisticOrderForm {
    /**
     * 产品名称
     */
    @Length(max = 50, message = "")
    private String productName;

    /**
     * 商品类型
     */
    private Integer productType;
    /**
     * 产品id
     */
    private Integer productId;
    /**
     * 平台类型 Google
     */
    @Length(max = 50, message = "")
    private String platform;
    /**
     * 运营名称
     */
    @Length(max = 10, message = "")
    private String operationName;

    private String operationUserCode;
    /**
     * 运营管理名称
     */
    @Length(max = 10, message = "")
    private String operationManagerName;
    /**
     * 运营管理id
     */
    private String operationManagerUserCode;

    /**
     * 时间类型 默认支付时间,1：支付时间，2创建时间
     */
    private Integer timeType;
    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @NotBlank(message = "开始时间不能为空")
    private String startDate;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @NotBlank(message = "结束时间不能为空")
    private String endDate;

}
