package com.miaowen.bh1xlhw.model.query.email_template;

import com.miaowen.bh1xlhw.model.entity.EmailPromotionTemplate;
import com.miaowen.bh1xlhw.model.entity.EmailPromotionTemplateContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailPromotionTemplateAddForm {

    @Valid
    @NotNull
    private EmailPromotionTemplate emailPromotionTemplate;

    @Valid
    @NotNull
    private List<EmailPromotionTemplateContent> emailPromotionTemplateContents;

}
