package com.miaowen.bh1xlhw.model.query.logManagement;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description 做题日志查询入参
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 10:29
 */
@Data
public class ExamLogForm extends PageForm {

    /**
     * 推广ID
     */
    private String tuid;
    /**
     * 日志id
     */
    private String logId;
    /**
     * 平台编码
     */
    private String platformCode;
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 试卷id
     */
    private String examId;

    /**
     * 时区
     */
    private String timezone;


    /**
     * 商品id
     */
    private String goodsId;

    /**
     * 1:多语言  2：繁体
     */
    private Integer goodsType;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;
}
