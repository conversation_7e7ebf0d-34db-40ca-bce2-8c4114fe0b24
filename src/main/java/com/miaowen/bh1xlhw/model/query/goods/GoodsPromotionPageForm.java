package com.miaowen.bh1xlhw.model.query.goods;

import com.miaowen.bh1xlhw.model.query.PageForm;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/28 15:28
 */
@Data
public class GoodsPromotionPageForm extends PageForm {
    /**
     * 推广id
     */
    private String tgId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 用途
     */
    private String application;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 运营主管id
     */
    private Integer operationManagerId;

    /**
     * 运营人员id
     */
    private Integer operationId;

    /**
     * 推广状态0否，1是
     */
    private Integer promotionStatus;

    private List<Integer> goodsIds;
    private List<Integer> goodsIds;

    /**
     * 是否是回收站 1表示回收站
     */
    private Integer isRecycle = 0;

}
