package com.miaowen.bh1xlhw.model.query.email_template;

import com.miaowen.bh1xlhw.model.entity.EmailTemplate;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailTemplateAddForm {

    @Valid
    @NotNull
    private EmailTemplate emailTemplate;

    @Valid
    @NotNull
    private List<EmailTemplateContent> emailTemplateContents;

}
