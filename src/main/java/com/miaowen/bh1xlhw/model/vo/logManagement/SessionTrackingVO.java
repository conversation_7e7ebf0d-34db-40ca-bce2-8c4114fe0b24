package com.miaowen.bh1xlhw.model.vo.logManagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Description 会话追踪出参VO
 * @Author：huanglong
 * @Date：2025/5/8 11:42
 */
@Data
public class SessionTrackingVO {

    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 设备指纹信息
     */
    private String deviceFingerprint;

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单编号
     */
    private Long orderId;

    /**
     * 环境类型
     */
    private Integer envType;

    /**
     * 推广ID
     */
    private String tuid;

    /**
     * 推广平台
     */
    private String platformCode;

    /**
     * 商品ID
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 支付状态 0初始化 1下单 2成功 3等待 4失败 5退款
     */
    private Integer payStatus;

    /**
     * 支付金额
     */
    private String payAmount;

    /**
     * 总金额
     */
    private String totalAmount;

    /**
     * IP地址
     */
    private String ip;

    /**
     * IP地理位置
     */
    private String ipAddress;

    /**
     * 追踪ID
     */
    private String traceId;

    /**
     * 来源标识
     */
    private String source;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;
}
