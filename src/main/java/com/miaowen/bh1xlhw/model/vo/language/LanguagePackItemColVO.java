package com.miaowen.bh1xlhw.model.vo.language;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName LanguagePackItemColVO
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 16:51
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LanguagePackItemColVO {

    //语言内容
    @NotBlank
    private String content;

    //语言代码
    @NotBlank
    private String languageCode;

    //语言名称
    @NotBlank
    private String languageName;

    private Integer languagePackId;

}

