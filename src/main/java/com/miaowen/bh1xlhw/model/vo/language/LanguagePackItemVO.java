package com.miaowen.bh1xlhw.model.vo.language;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName LanguagePackItemVO
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 16:51
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LanguagePackItemVO {

    //句子id
    @NotNull
    private Integer sentenceId;

    //语言内容
    @NotBlank
    private String content;

    //每列
    private List<LanguagePackItemColVO> cols;

}
