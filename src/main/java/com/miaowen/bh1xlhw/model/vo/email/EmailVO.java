package com.miaowen.bh1xlhw.model.vo.email;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;


import java.time.LocalDateTime;

/**
 * EmailVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-16
 */
@Data
public class EmailVO {

    private Integer id;

    /**
     * 邮箱名称
     */
    private String name;

    /**
     * 发件人名称
     */
    private String sendName;

    private String company;

    /**
     * 邮箱类型0公司1个人
     */
    private Integer type;

    private String smtp;

    private Integer port;

    private String email;

    private String password;

    /**
     * 状态0禁用1启用
     */
    private Integer status;
    /**
     * 发送次数
     */
    private Integer sendNumbers;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;
}
