package com.miaowen.bh1xlhw.model.vo.currency;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName CurrencyPageVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 11:35
 */
@Data
public class PoCurrencyPageVO {


    private Integer id;

    /**
     * 货币名称
     */
    private String name;

    /**
     * 货币符号
     */
    private String currencySymbol;


    /**
     * 货币单位
     */
    private String currencyUnit;

    /**
     * 国家代码
     */
    private String countryCode;

    /**
     * 汇率
     */
    private BigDecimal exchangeRate;

    /**
     * 精度
     */
    private Integer degreeAccuracy;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;


}
