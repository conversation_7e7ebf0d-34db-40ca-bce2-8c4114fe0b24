package com.miaowen.bh1xlhw.model.vo.email;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * UnsubscribeEmailVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-12
 */
@Data
public class UnsubscribeEmailVO {

    private Integer id;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 前端包名
     */
    private String webPackageName;

    /**
     * 退订时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime unsubscribeTime;

    /**
     * 取消退订时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime recoverTime;

    /**
     * 退订状态，1退订,0未退订
     */
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

}
