package com.miaowen.bh1xlhw.model.vo.oa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * PlatformAccountVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@Data
public class PlatformVO {
    @JsonProperty("id")
    private Integer id;


    /**
     * 平台名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 平台类型Google,Facebook,TikTok
     */
    @JsonProperty("type")
    private String type;

    /**
     * 来源
     */
    @JsonProperty("source")
    private String source;

    @JsonProperty("createTime")
    private LocalDateTime createTime;

    @JsonProperty("updateTime")
    private LocalDateTime updateTime;

    @JsonProperty("deleteTime")
    private Integer deleteTime;
}
