package com.miaowen.bh1xlhw.model.vo.goods;

import com.miaowen.bh1xlhw.model.bo.goods.GoodsBenefitInfoBO;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.GsonUtil;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * GoodsTypeEnVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09
 */
@Data
public class GoodsMultilingualVO {
    private Integer id;
    /**
     * 中文名称
     */
    private String name;
    /**
     * 多语言列表
     */
    private List<GoodsLanguageVO> goodsLanguageList;

    /**
     * 商品分类id
     */
    private Integer goodsCategoryId;

    /**
     * 商品类型
     */
    private String goodsCategoryName;

    /**
     * 分类标签
     */
    private String goodsTag;

    /**
     * 试卷题目数量
     */
    private Integer paperNum;

    /**
     * 风格名称
     */
    private String styleName;
    /**
     * 缩略图
     */
    private String thumbImage;

    /**
     * 关联文章标题
     */
    private List<Article> article;

    /**
     * 前端包名
     */
    private String webPackageName;

    /**
     * 权益信息
     */
    private List<GoodsBenefitInfoBO> goodsBenefitInfoList;

    /**
     * 价格方案
     */
    private PriceVO price;

    public static GoodsMultilingualVO transformVo(GoodsMultilingual record, List<LanguageContextConfig> languageContextConfigs,
                                                  Price price, GoodsCategoryMultilingual goodsCategoryMultilingual) {
        GoodsMultilingualVO goodsMultilingualVO = BeanUtils.copy(record, GoodsMultilingualVO.class);
        //语言配置列表
        List<GoodsLanguageVO> goodsLanguageVos =
            languageContextConfigs.stream().map(languageContextConfig -> {
                GoodsLanguageVO goodsLanguageVO = new GoodsLanguageVO();
                goodsLanguageVO.setLanguageCode(languageContextConfig.getLanguageCode());
                goodsLanguageVO.setName(languageContextConfig.getContext());
                return goodsLanguageVO;
            }).collect(Collectors.toList());
        //商品权益信息
        String benefitInfo = record.getBenefitInfo();
        if (Objects.nonNull(benefitInfo)){
            List<GoodsBenefitInfoBO> goodsBenefitInfoList = GsonUtil.gsonToList(benefitInfo, GoodsBenefitInfoBO.class);
            goodsMultilingualVO.setGoodsBenefitInfoList(goodsBenefitInfoList);
        }

        //商品价格信息
        PriceVO priceVO = PriceVO.transformVo(price);
        goodsMultilingualVO.setPrice(priceVO);
        if (Objects.nonNull(goodsCategoryMultilingual)){
            goodsMultilingualVO.setGoodsCategoryName(goodsCategoryMultilingual.getName());
            goodsMultilingualVO.setGoodsTag(goodsCategoryMultilingual.getType());
        }
        goodsMultilingualVO.setGoodsLanguageList(goodsLanguageVos);
        return goodsMultilingualVO;

    }
}
