package com.miaowen.bh1xlhw.model.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.entity.AccRole;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * RoleVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-06
 */
@Data
public class RoleVO {

    private Integer id;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;
    /**
     * 角色名
     */
    private String name;
    /**
     * 角色描述
     */
    private String description;
    /**
     * 是否是超管
     */
    private Integer isSuperAdmin;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 菜单列表
     */
    private List<Integer> permissionIds;

    public RoleVO(AccRole accRole) {
        this.id = accRole.getId();
//        this.isDeleted = accRole.getIsDeleted();
        this.createTime = accRole.getCreateTime();
        this.updateTime = accRole.getUpdateTime();

        this.name = accRole.getName();
        this.description = accRole.getDescription();
        this.isSuperAdmin = accRole.getIsSuperAdmin();
        this.status = accRole.getStatus();
    }
}
