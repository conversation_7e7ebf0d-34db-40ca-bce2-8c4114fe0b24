package com.miaowen.bh1xlhw.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *  :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-24
 */
@Data
public class AgentVO {
    private Integer id;


    /**
     * 代理商名字
     */
    private String name;

    /**
     * 结算方式1:月结,2:预付
     */
    private Integer settleType;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;
}
