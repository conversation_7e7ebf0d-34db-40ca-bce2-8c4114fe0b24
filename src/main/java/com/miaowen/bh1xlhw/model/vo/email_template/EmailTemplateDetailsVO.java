package com.miaowen.bh1xlhw.model.vo.email_template;

import com.miaowen.bh1xlhw.model.entity.EmailTemplate;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailTemplateDetailsVO {

    private EmailTemplateVo emailTemplate;
    private List<EmailTemplateContent> emailTemplateContents;

}
