package com.miaowen.bh1xlhw.model.vo.login;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Token :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-11
 */
@NoArgsConstructor
@Data
public class TokenVO {
    /**
     * token
     */
    @JsonProperty("access_token")
    private String token;
    /**
     * 到期时间戳
     */
    @JsonProperty("expires_in")
    private Integer expiration;
    /**
     * 用户昵称
     */
    @JsonProperty("username")
    private String username;
    /**
     * 邮箱
     */
    @JsonProperty("email")
    private String email;
}
