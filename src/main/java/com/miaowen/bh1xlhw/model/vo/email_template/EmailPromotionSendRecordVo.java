package com.miaowen.bh1xlhw.model.vo.email_template;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/21 14:28
 */
@Data
public class EmailPromotionSendRecordVo  {

    private Integer id;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 推送对象 1:已支付邮箱  2：未支付邮箱
     */
    private Integer proObj;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;
    /**
     * 商品分类
     */
    private String goodsTag;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品分类
     */
    private Integer goodsTagId;

    /**
     * 商品名称
     */
    private Integer goodsId;

    /**
     * 推送总量
     */
    private Integer totalNum;

    /**
     * 成功数量
     */
    private Integer successNum;

    /**
     * 失败数量
     */
    private Integer failNum;

    /**
     * 推送人
     */
    private String proPerson;

    /**
     * 推送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
