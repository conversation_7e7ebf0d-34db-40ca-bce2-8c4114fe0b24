package com.miaowen.bh1xlhw.model.vo.oa;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * StatisticOrderVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@Data
public class StatisticOrderVO {
    /**
     * tgid
     */
    private String tgId;
    /**
     * 商品id
     */
    private Integer goodsId;
    /**
     * 商品类型 1:多语言2:繁体
     */
    private Integer productType;

    /**
     * 付款日期
     */
    private LocalDate paymentDate;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 平台 Google
     */
    private String platform;

    /**
     * 运营人员工号
     */
    private String operationUserCode;
    /**
     * 运营人员名称
     */
    private String operationName;
    /**
     * 运营人员主管工号
     */
    private String operationManagerUserCode;
    /**
     * 运营主管名字
     */
    private String operationManagerName;
    /**
     * 总订单数量
     */
    private Integer totalOrderNum;
    /**
     * 支付订单数量
     */
    private Integer paidOrderNum;
    /**
     * 总支付金额
     */
    private BigDecimal totalPayAmount;
    /**
     * 原价支付数量
     */
    private Integer originalPriceOrderNum;
    /**
     * 原价支付金额
     */
    private BigDecimal originalPriceAmount;
    /**
     * 红包1支付数量
     */
    private Integer redPackage1OrderNum;
    /**
     * 红包1支付金额
     */
    private BigDecimal redPackage1Amount;
    /**
     * 红包2支付数量
     */
    private Integer redPackage2OrderNum;
    /**
     * 红包2支付金额
     */
    private BigDecimal redPackage2Amount;
    /**
     * 原价付费率
     */
    private BigDecimal originPriceRate;
    /**
     * 红包付费率
     */
    private BigDecimal redPackageRate;
    /**
     * 订单敢付费率
     */
    private BigDecimal orderRate;
}
