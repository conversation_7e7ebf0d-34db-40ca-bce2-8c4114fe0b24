package com.miaowen.bh1xlhw.model.vo.logManagement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/8 10:31
 */
@Data
public class ExamLogVO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 设备指纹信息
     */
    private String deviceFingerprint;

    /**
     * 试卷ID
     */
    private String examId;

    /**
     * 推广ID
     */
    private String tuid;

    /**
     * 投放平台
     */
    private String platformCode;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 商品ID
     */
    private Integer productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 总题数
     */
    private Integer questionsNum;

    /**
     * 已答题数
     */
    private Integer examNum;

    /**
     * 考试进度
     */
    private String examSchedule;

    /**
     * 支付状态 0初始化 1下单 2成功 3等待 4失败 5退款
     */
    private Integer payStatus;

    /**
     * 1:多语言 2：繁体
     */
    private Integer goodsType;

    /**
     * 支付金额
     */
    private String payAmount;

    /**
     * 总金额
     */
    private String totalAmount;

    /**
     * IP地址
     */
    private String ip;

    /**
     * IP地理位置
     */
    private String ipAddress;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;
}
