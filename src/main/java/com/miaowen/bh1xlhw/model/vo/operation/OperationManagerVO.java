package com.miaowen.bh1xlhw.model.vo.operation;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * OperationUserVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OperationManagerVO {
    private Integer id;

    /**
     * 工号
     */
    private String workNo;

    /**
     * 主管名称
     */
    private String name;


    /**
     * 领导id
     */
    private Integer pid;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;
}
