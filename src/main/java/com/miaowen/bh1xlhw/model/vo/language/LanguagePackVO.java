package com.miaowen.bh1xlhw.model.vo.language;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName CurrencyPageVo
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 11:35
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LanguagePackVO {

    @NotNull
    private List<LanguagePackItemVO> items;
}
