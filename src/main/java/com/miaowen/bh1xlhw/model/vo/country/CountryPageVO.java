package com.miaowen.bh1xlhw.model.vo.country;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName CurrencyPageVo
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 11:35
 */
@Data
public class CountryPageVO {


    private Integer id;
    /**
     * 国家名称
     */
    private String name;

    /**
     * 国家代码
     */
    private String code;

    /**
     * 所属大洲代码
     */
    private String continentCode;


    /**
     * 排序值
     */
    private Integer sort;


    /**
     * 状态0停用1正常
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

}
