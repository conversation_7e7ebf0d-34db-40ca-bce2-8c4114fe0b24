package com.miaowen.bh1xlhw.model.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.vo.operation.AgentsVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationManagerVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationUserVO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 商品订单详情视图对象
 * <p>包含订单详细信息、商品信息、做题记录及状态信息</p>
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class OrderGoodsVO {

    /** 
     * 系统订单ID 
     * 示例值：4943
     */
    private Long orderId;

    /** 
     * 商户订单号 
     * 示例值："E760773853264741755"
     */
    private String outTradeNo;

    /** 
     * 第三方支付平台订单号 
     * 示例值：""
     */
    private String thirdOutTradeNo;

    /** 
     * 支付系统单号 
     * 示例值：""
     */
    private String outTradePayNo;

    /** 
     * 下单时间 
     * 示例值："2025-05-07T16:11:49"
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    /**
     * 商品分类
     */
    private Integer goodsCategoryId;

    /** 
     * 商品ID 
     * 示例值：1
     */
    private Long goodsId;

    /** 
     * 商品名称 
     * 示例值："智商测试"
     */
    private String goodsName;

    /**
     * 商品类型多1,语言商品,2繁体商品
     */
    private Integer goodsType;

    /** 
     * 价格方案ID 
     * 示例值：0
     */
    private Integer priceId;

    /** 
     * 价格方案名称 
     * 示例值：""
     */
    private String priceName;

    /** 
     * 订单金额（单位：元） 
     * 示例值："0.00"
     */
    private String orderAmount;

    /** 
     * 订单来源渠道 
     * 示例值："test"
     */
    private String source;

    /**
     * 代理商
     */
    private AgentsVO agent;

    /**
     * 运营人员
     */
    private OperationUserVO operationUser;

    /**
     * 运营主管
     */
    private OperationManagerVO operationManager;

    /**
     * 推广商品id
     */
    private Integer promotionGoodsId;
    /**
     * 推广活动ID 
     * 示例值："T6695c758"
     */
    private String tgId;

    /** 
     * 广告投放平台 
     * 示例值："Google"
     */
    private String platformType;

    /** 
     * 是否推广订单 
     * 示例值：true
     */
    private Boolean isPromotionOrder;

//    /**
//     * 用户做题状态
//     * 示例值："已做"
//     */
//    private String exerciseStatus;

    /** 
     * 用户答题记录（原始数据） 
     * 示例值："444444433444444444444444444444"
     */
    private String resultAnswer;

    /** 
     * 完成测试总耗时 
     * 示例值："13分25秒"
     */
    private String completionTime;

    /** 
     * 后台查看状态 
     * 示例值："未查看"
     */
    private String isReadResult;

    /** 
     * 用户注册邮箱 
     * 示例值："111@cm"
     */
    private String email;

    /** 
     * 邮件发送状态 
     * 示例值："未发送"
     */
    private String emailSendStatus;


    /** 
     * 支付状态（需根据业务补充） 
     * 示例值："已支付"
     */
    private Integer paymentStatus;

    /** 
     * 支付方式（需根据业务补充） 
     * 示例值："支付宝"
     */
    private String paymentCode;

    /**
     * 结果链接
     */
    private String resultUrl;

    /**
     * 回归类型
     */
    private Integer returnType;

}
