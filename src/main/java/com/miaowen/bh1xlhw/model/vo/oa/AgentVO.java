package com.miaowen.bh1xlhw.model.vo.oa;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * PlatformAccountVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@Data
public class AgentVO {
    @JsonProperty("id")
    private Integer id;

    /**
     * 合作公司名称
     */
    @JsonProperty("name")
    private String name;

    /**
     * 结算方式1:月结,2:预付
     */
    @JsonProperty("settleType")
    private Integer settleType;


    /**
     * 平台表的主键
     */
    @JsonProperty("platformId")
    private Integer platformId;
    /**
     * 平台名称
     */
    @JsonProperty("platformName")
    private String platformName;

    /**
     * 平台类型1:谷歌,2facebook,3,tiktok
     */
    @JsonProperty("platformType")
    private String platformType;

    /**
     * 运营方式1:自运营，2:代运营
     */
    @JsonProperty("operationType")
    private Integer operationType;

    /**
     * 返点正负号,1:+,0:-
     */
    @JsonProperty("rebateSymbol")
    private Integer rebateSymbol;

    /**
     * 返点
     */
    @JsonProperty("rebate")
    private BigDecimal rebate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;

    private List<OperationUserDTO> operationUserList;


    @NoArgsConstructor
    @Data
    @AllArgsConstructor
    public static class OperationUserDTO {
        private Integer id;
        private String username;
        private String userCode;

        /**
         * 基于userCode的去重谓词（用于Stream API）
         * 使用ConcurrentHashMap保证线程安全
         */
        private static final Set<String> seenUserCodes = ConcurrentHashMap.newKeySet();


        public static Predicate<? super OperationUserDTO> distinctByUserCode() {
            return dto -> seenUserCodes.add(dto.getUserCode());
        }

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (!(o instanceof OperationUserDTO)) {
                return false;
            }
            return Objects.equals(this.userCode, ((OperationUserDTO) o).userCode);
        }

        @Override
        public int hashCode() {
            return Objects.hash(this.userCode);
        }

        /**
         * 静态工具方法：通过Stream API去重
         * @param list 待去重的DTO列表
         * @return 去重后的列表
         */
        public static List<OperationUserDTO> removeDuplicates(List<OperationUserDTO> list) {
            seenUserCodes.clear(); // 清除历史状态
            return list.stream()
                    .filter(distinctByUserCode())
                    .collect(Collectors.toList());
        }

        /**
         * 静态工具方法：通过Set特性去重（更高效）
         * @param list 待去重的DTO列表
         * @return 去重后的列表
         */
        public static List<OperationUserDTO> deduplicate(List<OperationUserDTO> list) {
            Set<OperationUserDTO> set = new LinkedHashSet<>(list);
            return new ArrayList<>(set);
        }
    }


    public static void main(String[] args) {
        // 原始列表
        List<OperationUserDTO> userList = Arrays.asList(
                new OperationUserDTO(1, "Alice", "A001"),
                new OperationUserDTO(2, "Bob", "A002"),
                new OperationUserDTO(3, "Alice", "A001"), // 重复userCode
                new OperationUserDTO(54, "Alice", "A005") // 重复userCode
        );
        // 方法1：使用Stream API去重（保留顺序）
        List<OperationUserDTO> uniqueList1 = OperationUserDTO.removeDuplicates(userList);
        // 方法2：使用Set特性去重（更高性能）
        List<OperationUserDTO> uniqueList2 = OperationUserDTO.deduplicate(userList);
        System.out.println(userList);
        System.out.println(uniqueList1);
        System.out.println(uniqueList2);
    }
}
