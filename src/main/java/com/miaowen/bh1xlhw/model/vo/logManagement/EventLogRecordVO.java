package com.miaowen.bh1xlhw.model.vo.logManagement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/8 16:06
 */
@Data
public class EventLogRecordVO {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 推广id
     */
    private String tgid;

    /**
     * 事件code
     */
    private String eventCode;

    /**
     * 事件名称
     */
    private String eventName;

    /**
     * 推广平台
     */
    private String platformCode;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;
}
