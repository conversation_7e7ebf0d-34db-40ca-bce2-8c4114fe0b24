package com.miaowen.bh1xlhw.model.vo.adManagement;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Description 广告回传记录出参VO
 * @Author：huanglong
 * @Date：2025/5/12 9:36
 */
@Data
public class AdBackfillRecordVo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 日志ID
     */
    private String logId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 推广ID
     */
    private String tuid;

    /**
     * 追踪ID
     */
    private String traceId;

    /**
     * 推广表对应ID
     */
    private Integer tgId;

    /**
     * 产品ID
     */
    private Integer productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 支付状态(0:未支付 1:已支付)
     */
    private Integer payStatus;

    /**
     * 支付金额
     */
    private String payAmount;

    /**
     * 红包使用类型 0原价 1红包1 2红包2
     */
    private Integer redPackUseType;

    /**
     * 订单时间/答题时间
     */
    private String orderTime;

    /**
     * 页面类型
     */
    private Integer pageType;

    /**
     * 页面类型
     */
    private String pageTypeName;

    /**
     * 平台代码
     */
    private String platformCode;

    /**
     * 前端URL
     */
    private String frontUrl;

    /**
     * 扩展参数（广告值）
     */
    private String params;

    /**
     * 发送状态(未发送  已发送)
     */
    private String sendStatus;

    /**
     * 发送状态 1:发送成功 -1:发送失败
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deleteTime;
}
