package com.miaowen.bh1xlhw.model.vo.article;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 *
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/23 15:01
 */
@Data
public class ArticleVo {

    /**
     * 标题
     */
    private String title;

    /**
     * 类型
     */
    private String articleType;

    /**
     * 商品类型  1:多语言,2:繁体
     */
    private Integer type;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态  状态 0禁用 1启用
     */
    private Integer status;

    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;
}
