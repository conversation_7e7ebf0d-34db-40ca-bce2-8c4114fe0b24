package com.miaowen.bh1xlhw.model.vo.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.constant.enums.PlatformTypeEnum;
import com.miaowen.bh1xlhw.model.bo.ad.FacebookConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.GoogleConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.TikTokConfigInfoBO;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.vo.operation.*;
import com.miaowen.bh1xlhw.service.good.GoodsPromotionService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.GsonUtil;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * GoodsPromotionMultilingualVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
@Data
public class GoodsPromotionMultilingualVO {
    private Integer id;

    /**
     * 推广id
     */
    private String tgId;
    /**
     * 用途
     */
    private String application;

    /**
     * 商品分类
     */
    private GoodsCategoryMultilingualVO goodsType;

    /**
     * 商品名称
     */
    private GoodsMultilingualVO goods;

    /**
     * 平台名称
     */
    private String platformType;


    /**
     * 代理商
     */
    private PlatformVO platform;

    /**
     * 广告账户id
     */
    private Integer advertiseAccountId;

    /**
     * 运营主管
     */
    private OperationManagerVO operationManager;

    /**
     * 运营人员
     */
    private OperationUserVO operationUser;

    /**
     * 域名
     */
    private DomainVO domain;

    /**
     * 推广状态0否，1是
     */
    private Integer promotionStatus;

    /**
     * 推广链接
     */
    private List<String> linkList;

    /**
     * 价格信息
     */
    private PriceVO price;
    /**
     * 关联文章标题
     */
    private List<Article> article;
    /**
     * 原价支付通道,0表示默认
     */
    private Integer pricePaymentChannel;

    /**
     * 红包1支付通道,0表示默认
     */
    private Integer red1PaymentChannel;

    /**
     * 红包2支付通道,0表示默认
     */
    private Integer red2PaymentChannel;

    /**
     * 当平台类型是Facebook时,传此参数,Facebook广告配置信息
     */
    private FacebookConfigInfoBO facebookConfigInfo;
    /**
     * 当平台类型是Google时,传此参数,Google广告配置信息
     */
    private GoogleConfigInfoBO googleConfigInfo;
    /**
     * 当平台类型是TikTik时,传此参数,TikTik广告配置信息
     */
    private TikTokConfigInfoBO tikTokConfigInfo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    /**
     * 转换为vo对象
     */
    public static GoodsPromotionMultilingualVO transformVo(GoodsPromotionMultilingual goodsPromotionMultilingual,
                                                           GoodsMultilingual goodsMultilingual, Price price,
                                                           PlatformVO platform,
                                                           Domain domain, OperationUser operationUser,
                                                           Map<Integer, OperationUser> operationManagerMap,
                                                           List<LanguageContextConfig> languageContextConfigs) {

        GoodsPromotionMultilingualVO goodsPromotionMultilingualVO = BeanUtils.copy(goodsPromotionMultilingual, GoodsPromotionMultilingualVO.class);
        String platformType = goodsPromotionMultilingual.getPlatformType();
        if (Objects.nonNull(goodsMultilingual)) {
            goodsPromotionMultilingualVO.setGoods(BeanUtils.copy(goodsMultilingual, GoodsMultilingualVO.class));
        }
        if (Objects.nonNull(price)) {
            goodsPromotionMultilingualVO.setPrice(PriceVO.transformVo(price));
        }
        if (Objects.nonNull(platform)) {
            goodsPromotionMultilingualVO.setPlatform(platform);
        }
        if (Objects.nonNull(domain)) {
            goodsPromotionMultilingualVO.setDomain(BeanUtils.copy(domain, DomainVO.class));
        }
        if (Objects.nonNull(operationUser)) {
            goodsPromotionMultilingualVO.setOperationUser(BeanUtils.copy(operationUser, OperationUserVO.class));
            Integer pid = operationUser.getPid();
            OperationUser operationManager = operationManagerMap.getOrDefault(pid, operationUser);
            goodsPromotionMultilingualVO.setOperationManager(BeanUtils.copy(operationManager, OperationManagerVO.class));
        }
        if (Objects.nonNull(platformType)) {
            if (PlatformTypeEnum.Facebook.getValue().equals(platformType)) {
                FacebookConfigInfoBO faceBookConfigInfoBO =
                    GsonUtil.gsonToBean(goodsPromotionMultilingual.getAdvertiseConfigInfo(),
                        FacebookConfigInfoBO.class);
                goodsPromotionMultilingualVO.setFacebookConfigInfo(faceBookConfigInfoBO);
            } else if (PlatformTypeEnum.Google.getValue().equals(platformType)) {
                GoogleConfigInfoBO googleConfigInfo =
                    GsonUtil.gsonToBean(goodsPromotionMultilingual.getAdvertiseConfigInfo(),
                        GoogleConfigInfoBO.class);
                goodsPromotionMultilingualVO.setGoogleConfigInfo(googleConfigInfo);
            }else if (PlatformTypeEnum.TikTok.getValue().equals(platformType)) {
                TikTokConfigInfoBO tikTokConfigInfoBO =
                    GsonUtil.gsonToBean(goodsPromotionMultilingual.getAdvertiseConfigInfo(), TikTokConfigInfoBO.class);
                goodsPromotionMultilingualVO.setTikTokConfigInfo(tikTokConfigInfoBO);
            }
        }
        List<String> linkList = GoodsPromotionService.getMultilingualLinkList(goodsPromotionMultilingual, goodsMultilingual,
            domain, platform, languageContextConfigs);
        goodsPromotionMultilingualVO.setLinkList(linkList);
        return goodsPromotionMultilingualVO;
    }



}
