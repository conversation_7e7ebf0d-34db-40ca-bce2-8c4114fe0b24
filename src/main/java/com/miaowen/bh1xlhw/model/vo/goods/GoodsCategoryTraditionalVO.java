package com.miaowen.bh1xlhw.model.vo.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * GoodsTypeEnVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GoodsCategoryTraditionalVO {
    private Integer id;
    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类标签
     */
    private String type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;
}
