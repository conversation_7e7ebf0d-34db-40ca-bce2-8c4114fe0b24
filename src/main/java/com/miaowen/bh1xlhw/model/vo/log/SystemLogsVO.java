package com.miaowen.bh1xlhw.model.vo.log;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * SystemLogsVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-22
 */
@Data
public class SystemLogsVO {


    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    /**
     * 管理员id
     */
    private Integer adminUserId;

    /**
     * 管理员用户名
     */
    private String adminUsername;

    /**
     * token
     */
    private String token;

    /**
     * 负责响应的服务名
     */
    private String server;

    /**
     * 负责响应的端口地址
     */
    private String port;

    /**
     * 请求方法
     */
    private String method;

    /**
     * 请求uri
     */
    private String uri;

    /**
     * 请求body
     */
    private String body;

    /**
     * 响应时间
     */
    private String ms;

    /**
     * 请求代理
     */
    private String agent;

    /**
     * 请求ip
     */
    private String ip;

    /**
     * 异常
     */
    private String abnormal;

    /**
     * 请求开始时间
     */
    private LocalDateTime startTime;

    /**
     * 请求结束时间
     */
    private LocalDateTime endTime;
}
