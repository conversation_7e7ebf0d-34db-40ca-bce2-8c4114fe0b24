package com.miaowen.bh1xlhw.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.miaowen.bh1xlhw.model.entity.Email;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * DomainVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DomainVO {

    private Integer id;
    /**
     * 域名
     */
    private String domain;
    /**
     * 备注
     */
    private String remark;

    /**
     * 平台id
     */
    private String platformType;
    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 平台使用情况
     */
    private Set<String> platformUsed;

    /**
     * 商品分类
     */
    private Integer goodsCategoryId;

    private String goodsCategory;
    /**
     * 邮箱id
     */
    private List<Email> emails;


    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;
}
