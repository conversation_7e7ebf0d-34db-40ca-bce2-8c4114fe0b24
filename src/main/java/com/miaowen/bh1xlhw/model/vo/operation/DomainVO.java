package com.miaowen.bh1xlhw.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.miaowen.bh1xlhw.model.entity.Email;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * DomainVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DomainVO {

    private Integer id;
    /**
     * 域名
     */
    private String domain;
    /**
     * 备注
     */
    private String remark;

    /**
     * 平台id
     */
    private Integer platformId;
    /**
     * 商品类型
     */
    private Integer goodsType;

    /**
     * 商品分类
     */
    private Integer goodsCategoryId;

    private String goodsCategory;
    /**
     * 邮箱id
     */
    private List<Email> emails;

    /**
     * 平台昵称
     */
    private String platformName;

    /**
     * 是否使用
     */
    private Integer use;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;
}
