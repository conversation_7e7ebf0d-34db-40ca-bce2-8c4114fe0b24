package com.miaowen.bh1xlhw.model.vo.goods;

import com.miaowen.bh1xlhw.model.query.goods.ScoreManagementForm;
import lombok.Data;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/6/4 19:16
 */
@Data
public class ScoreManagementVo {


    /**
     * 分类标签
     */
    private String tag;

    /**
     * 1：多语言 2：繁体
     */
    private Integer type;

    /**
     * 分值设置
     */
    private List<ScoreManagementForm.Score> scores;


    @Data
    public static class Score{
        /**
         * 题号
         */
        private Integer examNo;

        private List<ScoreManagementForm.Detail> details;
    }

    @Data
    public static class Detail{
        /**
         * 选项
         */
        private Integer examOption;

        /**
         * 答案/结果分值
         */
        private String examResult;
    }

}
