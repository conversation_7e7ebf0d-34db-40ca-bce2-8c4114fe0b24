package com.miaowen.bh1xlhw.model.vo.operation;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 平台管理
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_platform
 */
@Data
public class PlatformVO {

    private Integer id;

    /**
     * 平台名称
     */
    private String name;

    /**
     * 平台类型Google,Facebook,3,TikTok
     */
    private String type;

    /**
     * 来源
     */
    private String source;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;

}
