package com.miaowen.bh1xlhw.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * AgentVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlatformVO {
    private Integer id;
    /**
     * 代理商id
     */
    private Integer agentId;

    /**
     * 平台名
     */
    private String name;

    /**
     * 结算方式1:月结,2:预付
     */
    private Integer settleType;

    /**
     * 平台类型1:谷歌,2facebook,3,tiktok
     */
    private String platformType;

    /**
     * 运营方式1:自运营，2:代运营
     */
    private Integer operationType;

    /**
     * 返点正负号,1:+,0:-
     */
    private Integer rebateSymbol;

    /**
     * 返点
     */
    private BigDecimal rebate;

    /**
     * 来源
     */
    private String source;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;

}
