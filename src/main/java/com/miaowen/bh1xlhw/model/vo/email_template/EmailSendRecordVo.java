package com.miaowen.bh1xlhw.model.vo.email_template;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 推广邮件记录
  * @company 武汉秒闻网络科技有限公司

 */
@Data
public class EmailSendRecordVo {
    private Integer id;

    /**
     * 邮件标题
     */
    private String title;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 模板类型
     */
    private String templateType;

    /**
     * 邮件类型(通知场景)
     */
    private String emailType;
    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;
    /**
     * 商品分类id
     */
    private Integer goodsTagId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品分类
     */
    private String goodsTag;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 发送状态1：成功，0失败
     */
    private Integer sendStatus;

    /**
     * 错误信息
     */
    private String errorInfo;

    /**
     * 语言
     */
    private String languageCode;

    /**
     * 收件人邮箱
     */
    private String email;

    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 点击时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime clickTime;

    /**
     * 订单创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderCreateTime;

    /**
     * 订单号
     */
    private String outTradeNo;

}
