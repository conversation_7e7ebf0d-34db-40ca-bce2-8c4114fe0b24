package com.miaowen.bh1xlhw.model.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.entity.AccUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @Description 我的个人信息返回结果专用包装
 * <AUTHOR>
 * @Date 2025/3/18 15:05
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccMyInfoVo {

    private Integer id;
    /**
     * 密码强度
     */
    private String passwordRank;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;

    private Integer userId;
    private String username;
    private String realName;
    private String phone;
    private String email;
    private Integer status;
    private String userType;
    private String avatar;

    public AccMyInfoVo(AccUser accUser) {
        this.id = accUser.getId();
        this.createTime = accUser.getCreateTime();
        this.updateTime = accUser.getUpdateTime();
        this.userId = accUser.getId();
        this.username = accUser.getUsername();
        this.email = accUser.getEmail();
        this.status = accUser.getStatus();
        this.realName = accUser.getRealName();
        this.phone = accUser.getPhone();
        this.avatar = accUser.getAvatar();
        this.passwordRank = getPasswordRank(accUser.getPasswordRank());
    }

    public String getPasswordRank(Integer passwordRank) {
        if (passwordRank <= 0) {
            return "极弱";
        } else if (passwordRank >= 1 && passwordRank <= 3) {
            return "弱";
        } else if (passwordRank >= 4 && passwordRank <= 6) {
            return "中";
        } else {
            return "强";
        }
    }
}
