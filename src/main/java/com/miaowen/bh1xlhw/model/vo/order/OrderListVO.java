package com.miaowen.bh1xlhw.model.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.model.vo.operation.OperationManagerVO;
import com.miaowen.bh1xlhw.model.vo.operation.OperationUserVO;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 订单列表视图对象
 * <p>用于展示订单列表信息，包含订单基本信息和支付相关信息</p>
 * 
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13
 */
@Data
public class OrderListVO {
    private Long id;
    /**
     * 内部唯一订单号
     */
    private String outTradeNo;
    /**
     * 三方订单号
     */
    private String thirdOutTradeNo;

    /** 
     * 推广活动标识 
     * 示例值："PROMO_001"
     */
    private Integer promotionId;

    private String tgId;

    /** 
     * 广告投放平台 
     * 示例值："Google Ads"
     */
    private String platformType;

    /**
     * 红包使用状态
     */
    private Integer redPackUseType;

    /**
     * 红包领取状态
     */
    private Integer redPackGetType;

    /**
     * 商品类型多1,语言商品,2繁体商品
     */
    private Integer goodsType;


    private Integer goodsCategoryId;

    /**
     * 商品分类
     * 示例值："电子书"
     */
    private String goodsCategoryName;

    /**
     * 付款美元金额,美分单位
     */
    private BigDecimal usdAmount;

    /**
     * 原价
     */
    private BigDecimal originalAmount;

    /**
     * 红包金额
     */
    private BigDecimal redAmount;

    /** 
     * 商品全称 
     * 示例值："Java编程思想"
     */
    private String goodsName;


    /** 
     * 实际支付金额（含货币单位） 
     * 示例值："99.99"
     */
    private BigDecimal paymentAmount;
    /**
     * 剩余可退款金额
     */
    private BigDecimal leftAmount;
    /**
     *
     */
    private Integer paymentStatus;

    /** 
     * 用户来源渠道 
     * 示例值："gg"
     */
    private String source;

    /** 
     * 具体支付方式 
     * 示例值："stripe"
     */
    private String paymentCode;

    /** 
     * 国家或地区代码 
     * 示例值："CN"
     */
    private String countryCode;

    /**
     * 货币符号
     * 示例值："CN"
     */
    private String currency;

    /** 
     * 语言代码 
     * 示例值："zh"
     */
    private String languageCode;

    /** 
     * 支付完成时间 
     * 示例值："2023-05-15 14:30:45"
     */
    private String paymentTime;

    private String createTime;

    /**
     * 用户完成测试/做题时间
     */
    private String exerciseTime;

    /** 
     * 支付时的设备环境 
     * 示例值："iOS 15.4"
     */
    private String paymentEnv;
    /**
     * 退款金额,单位分
     */
    private BigDecimal refundAmount;

    /**
     * 是否部分退款,1是,0否
     */
    private Integer partialRefund;

    /** 
     * 负责该订单的运营人员 
     * 示例值："张三"
     */
    private OperationUserVO operatorUser;
    /**
     * 运营主管
     */
    private OperationManagerVO operationManagerVO;

    /**
     * 结果查看状态或链接
     */
    private Integer isReadResult;

    /** 
     * 用户绑定的邮箱地址 
     * 示例值："<EMAIL>"
     */
    private String email;

    /** 
     * 支付账户邮箱或银行卡号 
     * 示例值："5678"
     */
    private String cardNumber;

    /**
     * 第三方订单邮件
     */
    private String thirdEmail;

    /**
     * 货币符号
     */
    private String symbol;

}
