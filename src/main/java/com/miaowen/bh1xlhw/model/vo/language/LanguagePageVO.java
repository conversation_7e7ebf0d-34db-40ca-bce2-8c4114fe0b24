package com.miaowen.bh1xlhw.model.vo.language;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @ClassName LanguageVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/5/7 11:35
 */
@Data
public class LanguagePageVO {
    //语言名称
    private String name;

    //前台语言
    private String front;

    //语言代码
    private String code;

    //排序值
    private Integer sort;

    private Integer isDefault;

    private Integer status;

    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;
}
