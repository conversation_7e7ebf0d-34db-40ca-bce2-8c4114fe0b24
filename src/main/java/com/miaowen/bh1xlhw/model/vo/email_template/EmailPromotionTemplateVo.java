package com.miaowen.bh1xlhw.model.vo.email_template;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/6/7 13:53
 */
@Data
public class EmailPromotionTemplateVo {


    private Integer id;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;

    private Integer deleteTime;

    /**
     * 变量说明
     */
    private String variableDescription;

    /**
     * 名称
     */
    private String name;

    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;
    /**
     * 商品分类id
     */
    private Integer goodsTagId;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 商品分类id
     */
    private String goodsTag;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 域名
     */
    private Integer domain;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;

}
