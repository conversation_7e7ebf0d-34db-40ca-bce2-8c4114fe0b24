package com.miaowen.bh1xlhw.model.vo.logManagement;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description 事件配置返回结果包装
 * @Author：huanglong
 * @Date：2025/5/7 11:31
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SystemEventVo {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 事件名称
     */
    private String name;

    /**
     * 事件代码
     */
    private String code;

    /**
     * 事件类型code
     */
    private String typeCode;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Boolean status;
}
