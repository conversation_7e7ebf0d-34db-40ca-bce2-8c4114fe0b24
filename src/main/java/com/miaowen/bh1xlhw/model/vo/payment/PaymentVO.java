package com.miaowen.bh1xlhw.model.vo.payment;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class PaymentVO{

    private Integer id;

    /**
     * Payment name
     */
    private String name;

    /**
     * Merchant ID
     */
    private Integer merchantId;

    /**
     * Payment method code
     */
    private String paymentCode;

    /**
     * Merchant type code
     */
    private String merchantTypeCode;

    /**
     * Logo URL
     */
    private String logo;

    /**
     * Payment description
     */
    private String description;

    /**
     * Sort value
     */
    private Integer sort;

    /**
     * Is default (0-not default, 1-default)
     */
    private Integer isDefault;

    /**
     * Status (0-disabled, 1-enabled)
     */
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    private String merchantName;

    private List<String> countryCodeList;
    private List<String> currencyCodeList;

}
