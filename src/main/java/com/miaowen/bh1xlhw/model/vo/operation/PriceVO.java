package com.miaowen.bh1xlhw.model.vo.operation;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.miaowen.bh1xlhw.model.entity.Price;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.DecimalUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * PriceVo :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PriceVO {
    private Integer id;
    /**
     * 价格方案名称
     */
    private String name;

    /**
     * 价格类型,1:单价,2:双价,3:三价
     */
    private Integer type;
    /**
     * 低档价格,美分单位
     */
    private BigDecimal price1;

    /**
     * 低档红包1价格,美分单位
     */
    private BigDecimal red1Price1;

    /**
     * 低档红包2价格,美分单位
     */
    private BigDecimal red2Price1;
    /**
     * 低档划线价格,美分单位
     */
    private BigDecimal originPrice1;

    /**
     * 中档价格,美分单位
     */
    private BigDecimal price2;
    /**
     * 中档红包1价格,美分单位
     */
    private BigDecimal red1Price2;


    /**
     * 中档红包2价格,美分单位
     */
    private BigDecimal red2Price2;

    /**
     * 中档划线价格,美分单位
     */
    private BigDecimal originPrice2;

    /**
     * 高档价格,美分单位
     */
    private BigDecimal price3;

    /**
     * 高档红包1价格,美分单位
     */
    private BigDecimal red1Price3;

    /**
     * 高档红包2价格,美分单位
     */
    private BigDecimal red2Price3;

    /**
     * 高档划线价格,美分单位
     */
    private BigDecimal originPrice3;
    /**
     * 红包1
     */
    private BigDecimal redPackage1;

    /**
     * 红包2
     */
    private BigDecimal redPackage2;


    /**
     * roi基数
     */
    private BigDecimal baseRoi;

    /**
     * roi目标值
     */
    private BigDecimal targetRoi;

    /**
     * 红包1是否回传
     */
    private Integer redPackageBack1;

    /**
     * 红包2是否回传
     */
    private Integer redPackageBack2;
    /**
     * 邮箱是否回传
     */
    private Integer emailBack;


    public static PriceVO transformVo(Price price){
        if (Objects.isNull(price)){
            return new PriceVO();
        }
        PriceVO priceVo = BeanUtils.copy(price, PriceVO.class);
        //低挡
        priceVo.setPrice1(DecimalUtil.fromStoredValue(price.getPrice1()));
        priceVo.setOriginPrice1(DecimalUtil.fromStoredValue(price.getOriginPrice1()));
        priceVo.setRed1Price1(DecimalUtil.fromStoredValue(price.getRed1Price1()));
        priceVo.setRed2Price1(DecimalUtil.fromStoredValue(price.getRed2Price1()));

        //中挡
        priceVo.setPrice2(DecimalUtil.fromStoredValue(price.getPrice2()));
        priceVo.setOriginPrice2(DecimalUtil.fromStoredValue(price.getOriginPrice2()));
        priceVo.setRed1Price2(DecimalUtil.fromStoredValue(price.getRed1Price2()));
        priceVo.setRed2Price2(DecimalUtil.fromStoredValue(price.getRed2Price2()));

        //高挡
        priceVo.setPrice3(DecimalUtil.fromStoredValue(price.getPrice3()));
        priceVo.setOriginPrice3(DecimalUtil.fromStoredValue(price.getOriginPrice3()));
        priceVo.setRed1Price3(DecimalUtil.fromStoredValue(price.getRed1Price3()));
        priceVo.setRed2Price3(DecimalUtil.fromStoredValue(price.getRed2Price3()));

        priceVo.setRedPackage1(DecimalUtil.fromStoredValue(price.getRedPackage1()));
        priceVo.setRedPackage2(DecimalUtil.fromStoredValue(price.getRedPackage2()));
        return priceVo;
    }
}
