package com.miaowen.bh1xlhw.model.vo.system;

import lombok.Data;

/**
 * AccPermission :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-06
 */
@Data
public class PermissionVO {
    private Integer id;
    /**
     * 菜单名称，按钮名称
     */
    private String title;

    /**
     * 名字
     */
    private String name;

    /**
     * 路径
     */
    private String path;

    /**
     * 组件
     */
    private String component;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 权限（前台使用，做接口鉴权）
     */
    private String sign;

    /**
     * 权限（后台使用，做接口鉴权）
     */
    private String apiUrl;
    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 父节点(对应当前表的所属父节点的ID)
     */
    private Integer pid;

    /**
     * 权限类型1一级菜单2二级菜单3按钮
     */
    private Integer type;

    /**
     * 页面状态0显示1隐藏
     */
    private Integer hidden;
}
