package com.miaowen.bh1xlhw.model.vo.oa;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * PlatformAccountVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27
 */
@Data
public class PlatformAccountVO {
    /**
     * 平台名称
     */
    @JsonProperty("platform")
    private String platform;
    /**
     * 账户名称
     */
    @JsonProperty("accountName")
    private String name;
    /**
     * 代理商名称
     */
    @JsonProperty("agentName")
    private String agentName;
    /**
     * 代理商名称
     */
    @JsonProperty("agentId")
    private Integer agentId;

    /**
     * 运营方式1:自运营，2:代运营
     */
    private Integer operationType;
    /**
     * ori目标值
     */
    @JsonProperty("targetRoi")
    private BigDecimal targetRoi;

    @JsonProperty("baseRoi")
    private BigDecimal baseRoi;
}
