package com.miaowen.bh1xlhw.model.vo.email_template;

import com.miaowen.bh1xlhw.model.entity.EmailPromotionTemplate;
import com.miaowen.bh1xlhw.model.entity.EmailPromotionTemplateContent;
import com.miaowen.bh1xlhw.model.entity.EmailTemplate;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmailPromotionTemplateDetailsVO {
    private EmailPromotionTemplateVo emailPromotionTemplate;
    private List<EmailPromotionTemplateContent> emailPromotionTemplateContents;

}
