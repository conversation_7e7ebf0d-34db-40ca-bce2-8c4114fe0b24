package com.miaowen.bh1xlhw.model.vo.order;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * OrderMbtiSubVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-13
 */
@Data
public class OrderMbtiSubVO {

    private Long id;
    /**
     * 主订单id
     */
    private Integer mainOrderId;

    /**
     * 主订单号
     */
    private String mainOutTradeNo;

    /**
     * 具体支付方式
     * 示例值："stripe"
     */
    private String paymentCode;

    /**
     * 子商品类型,1:mbti职场优势,2:mbti八维报告3:mbti恋爱报告
     */
    private Integer type;

    /**
     * careerAdvantages:职场优势,eightDimensionalReport:八维报告, loveReport:恋爱报告
     */
    private String mbtiReport;

    /**
     * 商品类型多1,语言商品,2繁体商品
     */
    private Integer goodsId;

    /**
     * 商品类型多1,语言商品,2繁体商品
     */
    private Integer goodsType;

    /**
     * 付款美元金额,美分单位
     */
    private BigDecimal usdAmount;

    /**
     * 商品分类
     * 示例值："电子书"
     */
    private String goodsName;

    /**
     * 剩余可退款金额
     */
    private BigDecimal leftAmount;

    /**
     * 结果页面链接
     */
    private String resultUrl;

    /**
     * 是否部分退款,1是,0否
     */
    private Integer partialRefund;

    /**
     * 内部唯一订单号
     */
    private String outTradeNo;

    /**
     * 货币符号
     */
    private String symbol;
    /**
     * 第三方订单号
     */
    private String thirdOutTradeNo;


    /**
     * 商品金额,单位分
     */
    private Integer amount;

    /**
     * 货币单位
     */
    private String currency;

    /**
     * 支付金额,单位分
     */
    private BigDecimal paymentAmount;

    /**
     * 退款金额,单位分
     */
    private BigDecimal refundAmount;

    /**
     * 支付货币类型
     */
    private String paymentCurrency;

    /**
     * 支付实际
     */
    private String paymentTime;


    /**
     * 支付状态 0初始化 1下单 2成功 3等待 4失败 5退款
     */
    private Integer paymentStatus;


    /**
     * 支付方式id
     */
    private Integer paymentTypeId;

    /**
     * 支付后四位卡号
     */
    private String cardNumber;

    /**
     * 第三方订单邮件
     */
    private String thirdEmail;


    /**
     *
     */
    private String createTime;

}
