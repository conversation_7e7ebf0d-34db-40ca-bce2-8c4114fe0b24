package com.miaowen.bh1xlhw.model.vo.goods;

import com.miaowen.bh1xlhw.model.bo.goods.GoodsBenefitInfoBO;
import com.miaowen.bh1xlhw.model.entity.Article;
import com.miaowen.bh1xlhw.model.entity.GoodsTraditional;
import com.miaowen.bh1xlhw.model.entity.GoodsCategoryTraditional;
import com.miaowen.bh1xlhw.model.entity.Price;
import com.miaowen.bh1xlhw.model.vo.operation.PriceVO;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.GsonUtil;
import lombok.Data;

import java.util.List;
import java.util.Objects;

/**
 * GoodTraditionalVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
@Data
public class GoodsTraditionalVO {
    private Integer id;
    /**
     * 繁体中文名称
     */
    private String name;

    /**
     * 商品分类id
     */
    private Integer goodsCategoryId;

    /**
     * 商品类型
     */
    private String goodsCategoryName;

    /**
     * 分类标签
     */
    private String goodsTag;


    /**
     * 试卷题目数量
     */
    private Integer paperNum;

    /**
     * 关联文章标题
     */
    private List<Article> article;

    /**
     * 风格名称
     */
    private String styleName;
    /**
     * 缩略图
     */
    private String thumbImage;

    /**
     * 前端包名
     */
    private String webPackageName;

    /**
     * 权益信息
     */
    private List<GoodsBenefitInfoBO> goodsBenefitInfoList;

    /**
     * 价格方案
     */
    private PriceVO price;

    public static GoodsTraditionalVO transformVo(GoodsTraditional record, Price price, GoodsCategoryTraditional goodsTypeMultilingual) {
        GoodsTraditionalVO goodsTraditionalVO = BeanUtils.copy(record, GoodsTraditionalVO.class);
        //商品权益信息
        String benefitInfo = record.getBenefitInfo();
        if (Objects.nonNull(benefitInfo)){
            List<GoodsBenefitInfoBO> goodsBenefitInfoList = GsonUtil.gsonToList(benefitInfo,
                GoodsBenefitInfoBO.class);
            goodsTraditionalVO.setGoodsBenefitInfoList(goodsBenefitInfoList);
        }

        //商品价格信息
        PriceVO priceVO = PriceVO.transformVo(price);
        goodsTraditionalVO.setPrice(priceVO);
        if (Objects.nonNull(goodsTypeMultilingual)){
            goodsTraditionalVO.setGoodsCategoryName(goodsTypeMultilingual.getName());
            goodsTraditionalVO.setGoodsTag(goodsTypeMultilingual.getType());
        }
        return goodsTraditionalVO;
    }
}
