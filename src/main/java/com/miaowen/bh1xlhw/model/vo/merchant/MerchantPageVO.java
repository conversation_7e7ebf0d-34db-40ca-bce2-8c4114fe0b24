package com.miaowen.bh1xlhw.model.vo.merchant;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * 商户管理表
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @TableName po_system_merchant
 */
@Slf4j
@Data
public class MerchantPageVO {
    private Integer id;
    /**
     * 商户名称
     */
    private String name;

    /**
     * 商户类型代码
     */
    private String typeCode;

    /**
     * 支付公司
     */
    private String company;

    /**
     * 支付显示名称
     */
    private String showName;

    /**
     * 配置数据
     */
    private String configData;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态 0禁用 1启用
     */
    private Integer status;

    /**
     * 商户是否可用
     */
    private Integer merchantEnableStatus;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    /**
     * 前端需要的JSON格式配置（动态转换）
     */
    public JsonNode getConfigData() {
        if (this.configData == null || this.configData.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readTree(this.configData);
        } catch (Exception e) {
            log.error("配置数据JSON解析失败 | configData: {}", this.configData, e);
            return null;
        }
    }
}
