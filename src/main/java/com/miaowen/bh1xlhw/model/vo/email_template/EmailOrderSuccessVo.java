package com.miaowen.bh1xlhw.model.vo.email_template;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 已支付成功邮箱出参
 * @Author：huanglong
 * @Date：2025/5/21 14:57
 */
@Data
public class EmailOrderSuccessVo {

    /**
     * 邮箱
     */
    private String email;
    /**
     * 订单号
     */
    private String outTradeNo;
    /**
     * 订单语言
     */
    private String languageCode;
    /**
     * 商品类型1:多语言,2:繁体
     */
    private Integer goodsType;
    /**
     * 商品分类
     */
    private String goodsTag;
    /**
     *
     */
    private Integer goodsCategoryId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 域名
     */
    private String domain;
    /**
     * 状态 1：正常 0：退订
     */
    private String status;
    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime paymentTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime updateTime;

}
