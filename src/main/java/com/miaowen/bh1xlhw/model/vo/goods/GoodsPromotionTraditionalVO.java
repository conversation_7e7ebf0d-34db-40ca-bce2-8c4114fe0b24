package com.miaowen.bh1xlhw.model.vo.goods;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.miaowen.bh1xlhw.constant.enums.PlatformTypeEnum;
import com.miaowen.bh1xlhw.model.bo.ad.FacebookConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.GoogleConfigInfoBO;
import com.miaowen.bh1xlhw.model.bo.ad.TikTokConfigInfoBO;
import com.miaowen.bh1xlhw.model.entity.*;
import com.miaowen.bh1xlhw.model.vo.operation.*;
import com.miaowen.bh1xlhw.service.good.GoodsPromotionService;
import com.miaowen.bh1xlhw.utils.BeanUtils;
import com.miaowen.bh1xlhw.utils.GsonUtil;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * GoodsPromotionTraditionalVO :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12
 */
@Data
public class GoodsPromotionTraditionalVO {
    private Integer id;
    /**
     * 用途
     */
    private String application;

    /**
     * 推广id
     */
    private String tgId;
    /**
     * 商品分类
     */
    private GoodsCategoryTraditionalVO goodsType;

    /**
     * 商品名称
     */
    private GoodsTraditionalVO goods;

    /**
     * 平台名称
     */
    private String platformType;


    /**
     * 代理商
     */
    private PlatformVO platform;

    /**
     * 广告账户id
     */
    private Integer advertiseAccountId;
    /**
     * 运营主管
     */
    private OperationManagerVO operationManager;

    /**
     * 运营人员
     */
    private OperationUserVO operationUser;

    /**
     * 域名
     */
    private DomainVO domain;

    /**
     * 推广状态0否，1是
     */
    private Integer promotionStatus;

    /**
     * 价格信息
     */
    private PriceVO price;
    /**
     * 关联文章标题
     */
    private List<Article> article;

    /**
     * 当平台类型是Facebook时,传此参数,Facebook广告配置信息
     */
    private FacebookConfigInfoBO facebookConfigInfo;
    /**
     * 当平台类型是Google时,传此参数,Google广告配置信息
     */
    private GoogleConfigInfoBO googleConfigInfo;
    /**
     * 当平台类型是TikTik时,传此参数,TikTik广告配置信息
     */
    private TikTokConfigInfoBO tikTokConfigInfo;

    /**
     * 原价支付通道,0表示默认
     */
    private Integer pricePaymentChannel;

    /**
     * 红包1支付通道,0表示默认
     */
    private Integer red1PaymentChannel;

    /**
     * 红包2支付通道,0表示默认
     */
    private Integer red2PaymentChannel;
    /**
     * 推广链接
     */
    private List<String> linkList;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "UTC+8")
    private LocalDateTime createTime;

    /**
     * 转换为vo对象
     */
    public static GoodsPromotionTraditionalVO transformVo(GoodsPromotionTraditional goodsPromotionTraditional,
                                                          GoodsTraditional goodsTraditional, Price price, PlatformVO platform,
                                                          Domain domain, OperationUser operationUser,
                                                          Map<Integer, OperationUser> operationManagerMap) {

        GoodsPromotionTraditionalVO goodsPromotionTraditionalVO = BeanUtils.copy(goodsPromotionTraditional, GoodsPromotionTraditionalVO.class);
        String platformType = goodsPromotionTraditional.getPlatformType();
        if (Objects.nonNull(goodsTraditional)) {
            goodsPromotionTraditionalVO.setGoods(BeanUtils.copy(goodsTraditional, GoodsTraditionalVO.class));
        }
        if (Objects.nonNull(price)) {
            goodsPromotionTraditionalVO.setPrice(PriceVO.transformVo(price));
        }
        if (Objects.nonNull(platform)) {
            goodsPromotionTraditionalVO.setPlatform(BeanUtils.copy(platform, PlatformVO.class));
        }
        if (Objects.nonNull(domain)) {
            goodsPromotionTraditionalVO.setDomain(BeanUtils.copy(domain, DomainVO.class));
        }
        if (Objects.nonNull(operationUser)) {
            goodsPromotionTraditionalVO.setOperationUser(BeanUtils.copy(operationUser, OperationUserVO.class));
            Integer pid = operationUser.getPid();
            OperationUser operationManager = operationManagerMap.getOrDefault(pid, operationUser);
            goodsPromotionTraditionalVO.setOperationManager(BeanUtils.copy(operationManager, OperationManagerVO.class));
        }
        if (Objects.nonNull(platform)) {
            if (PlatformTypeEnum.Facebook.getValue().equals(platformType)) {
                FacebookConfigInfoBO faceBookConfigInfoBO =
                    GsonUtil.gsonToBean(goodsPromotionTraditional.getAdvertiseConfigInfo(),
                        FacebookConfigInfoBO.class);
                goodsPromotionTraditionalVO.setFacebookConfigInfo(faceBookConfigInfoBO);
            } else if (PlatformTypeEnum.Google.getValue().equals(platformType)) {
                GoogleConfigInfoBO googleConfigInfo =
                    GsonUtil.gsonToBean(goodsPromotionTraditional.getAdvertiseConfigInfo(),
                        GoogleConfigInfoBO.class);
                goodsPromotionTraditionalVO.setGoogleConfigInfo(googleConfigInfo);
            }else if (PlatformTypeEnum.TikTok.getValue().equals(platformType)) {
                TikTokConfigInfoBO tikTokConfigInfoBO =
                    GsonUtil.gsonToBean(goodsPromotionTraditional.getAdvertiseConfigInfo(), TikTokConfigInfoBO.class);
                goodsPromotionTraditionalVO.setTikTokConfigInfo(tikTokConfigInfoBO);
            }
        }
        List<String> linkList = GoodsPromotionService.getTraditionalLinkList(goodsPromotionTraditional, goodsTraditional, domain, platform);

        goodsPromotionTraditionalVO.setLinkList(linkList);
        return goodsPromotionTraditionalVO;

    }
}
