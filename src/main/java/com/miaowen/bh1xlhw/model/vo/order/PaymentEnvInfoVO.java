package com.miaowen.bh1xlhw.model.vo.order;

import lombok.Data;

/**
 * 支付环境视图对象
 * <p>记录用户支付时的客户端环境、设备、网络及地理位置信息</p>
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Data
public class PaymentEnvInfoVO {

    /** 
     * 浏览器名称及版本 
     * 示例值："Chrome 132.0.0.0"
     */
    private String browserVersion;

    /**
     * 环境
     */
    private String paymentEnv;

    /** 
     * 浏览器完整User-Agent 
     * 示例值："Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
     */
    private String userAgent;

    /** 
     * 设备操作系统及版本 
     * 示例值："Windows 10"
     */
    private String deviceModel;

    /** 
     * 网络类型（4G/5G/Wi-Fi） 
     * 示例值："4G"
     */
    private String networkType;

    /** 
     * 屏幕分辨率（宽x高） 
     * 示例值："2560x1440"
     */
    private String screenResolution;

    /** 
     * 用户公网IP地址 
     * 示例值："*************"
     */
    private String ipAddress;

    /** 
     * IP所在地区（精确到城市） 
     * 示例值："中国武汉"
     */
    private String ipRegion;

    /** 
     * 用户首选语言 
     * 示例值："简体中文"
     */
    private String languageCode;

    /** 
     * 用户时区 
     * 示例值："Asia/Shanghai"
     */
    private String timezone;


    /**
     * 客户端时区偏移量（单位：小时）
     * 示例值：8
     */
    private Integer timezoneOffset;


    /** 
     * 用户发起购买的站点URL 
     * 示例值："https://h5.h1-xl-hw.517ce.top"
     */
    private String webSite;

    /** 
     * 支付API站点URL 
     * 示例值："https://a.h1-x1-nw.517ce.top"
     */
    private String apiSite;


}
