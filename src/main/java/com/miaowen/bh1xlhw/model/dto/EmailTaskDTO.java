package com.miaowen.bh1xlhw.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * EmailTaskDTO
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-14
 */
@Data
public class EmailTaskDTO implements Serializable {
    // 邮件类型（对应策略类型）
    private Integer emailType;
    
    // 收件人邮箱
    private String toEmail;
    
    // 业务数据（模板参数）
    private Map<String, Object> businessData;
    
    // 动态URL
    private String dynamicUrl;
    
    // PDF附件路径（建议使用OSS路径或本地绝对路径）
    private String pdfAttachmentPath;
    
    // 模板ID（可选）
    private Long templateId;
}
