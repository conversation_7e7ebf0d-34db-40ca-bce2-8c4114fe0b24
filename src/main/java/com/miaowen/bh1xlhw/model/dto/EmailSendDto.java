package com.miaowen.bh1xlhw.model.dto;

import com.miaowen.bh1xlhw.model.entity.Email;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮箱发送dto
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/22 17:08
 */
@Data
public class EmailSendDto implements Serializable {
    private Integer id;
    private String toEmail;
    private String title;
    private String content;
    private String unsubscribeUrl;
    private Email emailEntity;
}
