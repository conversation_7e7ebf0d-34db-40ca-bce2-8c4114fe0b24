package com.miaowen.bh1xlhw.model.dto;

import com.miaowen.bh1xlhw.model.entity.Email;
import lombok.Data;

import java.io.Serializable;

/**
 * 邮箱发送dto
 * @Author：huang<PERSON>
 * @Date：2025/5/22 17:08
 */
@Data
public class EmailSendDto implements Serializable {
    private Integer id;
    private String toEmail;
    private String title;
    private String content;
    private String unsubscribeUrl;
    private Email emailEntity;
}
