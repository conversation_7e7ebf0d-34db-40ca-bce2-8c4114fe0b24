package com.miaowen.bh1xlhw.mapper;

import com.miaowen.bh1xlhw.config.mybatisPlus.CustomBaseMapper;
import com.miaowen.bh1xlhw.model.entity.PaymentChannel;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 *
 * 针对表【po_payment_pattern(支付方式模式配置)】的数据库操作Mapper
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 09:50:56
 * {@link PaymentChannel}
 */
public interface PaymentChannelMapper extends CustomBaseMapper<PaymentChannel> {

    @Select("update po_payment_channel set default_status = 0 where id != #{id};")
    void updateNoDefault(@Param("id") Integer id);
}




