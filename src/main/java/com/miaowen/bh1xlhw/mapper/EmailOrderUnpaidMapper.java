package com.miaowen.bh1xlhw.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.miaowen.bh1xlhw.mapper.base.CustomBaseMapper;
import com.miaowen.bh1xlhw.model.entity.EmailOrderUnpaid;
import org.apache.ibatis.annotations.Mapper;

/**
 * 针对表【po_email_order_un_paid(未支付邮件发送记录表)】的数据库操作Mapper
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-19 16:19:45
 * {@link EmailOrderUnpaid}
 */
@Mapper
@DS("log")
public interface EmailOrderUnpaidMapper extends CustomBaseMapper<EmailOrderUnpaid> {

}




