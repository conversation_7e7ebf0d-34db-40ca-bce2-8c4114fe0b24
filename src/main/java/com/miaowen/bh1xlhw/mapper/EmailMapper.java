package com.miaowen.bh1xlhw.mapper;

import com.miaowen.bh1xlhw.config.mybatisPlus.CustomBaseMapper;
import com.miaowen.bh1xlhw.model.entity.Email;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_email】的数据库操作Mapper
 * @createDate 2025-05-06 15:41:28
 * @Entity com.miaowen.bh1xlhw.common.entity.Email
 */
@Mapper
public interface EmailMapper extends CustomBaseMapper<Email> {

    @Update("update po_email set send_numbers = send_numbers + 1 where id = #{id}")
    void updateNumbers(@Param("id") Integer id);
}




