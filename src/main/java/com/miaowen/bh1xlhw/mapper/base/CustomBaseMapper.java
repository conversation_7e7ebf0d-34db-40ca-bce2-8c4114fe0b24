package com.miaowen.bh1xlhw.mapper.base;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Collection;

/**
 * 组定义mapper基类
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since  2025/5/6 15:05
 */
public interface CustomBaseMapper<T> extends BaseMapper<T> {
    /**
     * 根据主键 删除逻辑删除
     *
     * @param id 主键
     */
    int logicDeleteById(Serializable id);

    /**
     * 条件逻辑删除 删除逻辑删除
     *
     * @param updateWrapper 条件
     */
    int logicDelete(@Param(Constants.WRAPPER) Wrapper<T> updateWrapper);

    /**
     * 根据主键集合 删除逻辑删除
     *
     * @param idList 主键集合
     */
    int logicDeleteBatchIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);


    /**
     * 根据主键逻辑恢复
     * @param id 主键
     */
    int recoverById(Serializable id);

    /**
     * 根据主键集合逻辑恢复
     * @param idList 主键集合
     */
    int recoverBatchIds(@Param(Constants.COLL) Collection<? extends Serializable> idList);
}
