package com.miaowen.bh1xlhw.mapper;

import com.miaowen.bh1xlhw.mapper.base.CustomBaseMapper;
import com.miaowen.bh1xlhw.model.entity.RefundRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;

/**
 *
 * 针对表【po_refund_record(后台退款记录表)】的数据库操作Mapper
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-23 14:58:43
 * {@link com.miaowen.bh1xlhw.model.entity.RefundRecord}
 */
public interface RefundRecordMapper extends CustomBaseMapper<RefundRecord> {

    @Select("select sum(amount) from po_refund_record where order_id = #{orderId} and status = 2")
    Integer sumRefundRecord(@Param("orderId") Long orderId);

    @Select("select sum(amount) from po_refund_record where status = 2" +
        " and refund_time > #{startTime} and refund_time < #{endTime}")
    Integer sumByDate(@Param("startTime") LocalDateTime of,@Param("endTime") LocalDateTime of1);
}




