package com.miaowen.bh1xlhw.mapper;

import com.miaowen.bh1xlhw.mapper.base.CustomBaseMapper;
import com.miaowen.bh1xlhw.model.entity.PoExchangeRateLog;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_exchange_rate_logs(汇率录表)】的数据库操作Mapper
 * @createDate 2025-05-06 15:41:28
 * @Entity com.miaowen.bh1xlhw.common.entity.PoExchangeRateLog
 */
@Mapper
public interface PoExchangeRateLogMapper extends CustomBaseMapper<PoExchangeRateLog> {
    BigDecimal getLatestRateByCurrencyUnit(String currencyUnit);
}




