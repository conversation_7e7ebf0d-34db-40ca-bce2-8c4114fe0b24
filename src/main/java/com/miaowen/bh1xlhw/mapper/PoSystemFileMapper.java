package com.miaowen.bh1xlhw.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.config.mybatisPlus.CustomBaseMapper;
import com.miaowen.bh1xlhw.model.entity.PoSystemFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_role(角色表)】的数据库操作Mapper
 * @createDate 2025-05-06 15:41:28
 * @Entity com.miaowen.bh1xlhw.common.entity.File
 */
@Mapper
public interface PoSystemFileMapper extends CustomBaseMapper<PoSystemFile> {
    // 自定义分页查询（包含排序）
    @Select("SELECT * FROM po_system_file WHERE is_deleted = 0 ${orderBy}")
    IPage<PoSystemFile> selectFilePage(Page<PoSystemFile> page, @Param("orderBy") String orderBy);
}




