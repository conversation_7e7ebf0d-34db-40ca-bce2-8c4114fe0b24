package com.miaowen.bh1xlhw.mapper;

import com.miaowen.bh1xlhw.model.bo.DomainPlatformCountBO;
import com.miaowen.bh1xlhw.model.entity.DomainPlatform;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_domain_platform(记录域名平台使用情况)】的数据库操作Mapper
 * @time   2025-05-08 15:42:34
 * {@link com.miaowen.bh1xlhw.model.entity.DomainPlatform}
 */
public interface DomainPlatformMapper extends BaseMapper<DomainPlatform> {

    @Select("select domain_id,count(*) as count  from po_domain_platform GROUP BY domain_id;")
    List<DomainPlatformCountBO> selectDomainPlatformCount();

}




