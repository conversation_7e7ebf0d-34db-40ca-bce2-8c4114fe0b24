package com.miaowen.bh1xlhw.mapper;

import com.miaowen.bh1xlhw.model.entity.AccRole;
import com.miaowen.bh1xlhw.model.entity.AccUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_user(后台用户表)】的数据库操作Mapper
 * @createDate 2025-05-06 15:41:28
 * @Entity com.miaowen.bh1xlhw.common.entity.AccUser
 */
public interface AccUserMapper extends BaseMapper<AccUser> {

    AccUser getAccUserByUserName(@Param("username") String username);

    AccUser getAccUserByEmail(@Param("email") String email);

    List<AccRole> getRolesByUserId(Integer userId);

    List<String> getAllSign();

    List<String> getPermissionSignByRoleId(Integer roleId);

}




