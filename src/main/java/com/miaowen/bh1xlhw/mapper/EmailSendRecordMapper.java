package com.miaowen.bh1xlhw.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.miaowen.bh1xlhw.config.mybatisPlus.CustomBaseMapper;
import com.miaowen.bh1xlhw.model.entity.Email;
import com.miaowen.bh1xlhw.model.entity.EmailSendRecord;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_role(角色表)】的数据库操作Mapper
 * @createDate 2025-05-06 15:41:28
 * @Entity com.miaowen.bh1xlhw.common.entity.Email
 */
@Mapper
@DS("log")
public interface EmailSendRecordMapper extends CustomBaseMapper<EmailSendRecord> {

}




