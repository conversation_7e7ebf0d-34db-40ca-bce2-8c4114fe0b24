package com.miaowen.bh1xlhw.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.miaowen.bh1xlhw.model.bo.oa.StatisticOrderBO;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.mapper.base.CustomBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 针对表【po_order(交易订单表)】的数据库操作Mapper
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 19:20:54
 * {@link Order}
 */
public interface OrderMapper extends CustomBaseMapper<Order> {

    @Select("SELECT" +
        "  tg_id ," +
        "  goods_type ," +
        "  payment_date, " +
        "  COUNT(*) AS totalOrderNum, " +
        "  SUM(IF(payment_status = 2, 1, 0)) AS paidOrderNum,  " +
        "  SUM(IF(payment_status = 2, payment_amount, 0)) AS totalPayAmount," +
        "  SUM(IF(red_pack_use_type = 0, 1, 0)) AS originalPriceOrderNum," +
        "  SUM(IF(red_pack_use_type = 0 AND payment_status = 2, payment_amount, 0)) AS originalPriceAmount," +
        "  SUM(IF(red_pack_use_type = 1, 1, 0)) AS redPackage1OrderNum," +
        "  SUM(IF(red_pack_use_type = 1 AND payment_status = 2, payment_amount, 0)) AS redPackage1Amount," +
        "  SUM(IF(red_pack_use_type = 2, 1, 0)) AS redPackage2OrderNum," +
        "  SUM(IF(red_pack_use_type = 2 AND payment_status = 2, payment_amount, 0)) AS redPackage2Amount" +
        " FROM" +
        "  `po_order`" +
        "  ${ew.customSqlSegment}  " +
        " GROUP BY payment_date, tg_id, goods_type")
    List<StatisticOrderBO> statisticOrder(@Param(Constants.WRAPPER) LambdaQueryWrapper<Order> wrapper);

}




