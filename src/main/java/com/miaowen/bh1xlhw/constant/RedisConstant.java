package com.miaowen.bh1xlhw.constant;

/**
 * @Description redis key前缀 和 key 常量
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/25 14:28
 */
public class RedisConstant {

    /**
     * 登录图形验证码存入redis的key前缀
     */
    public static final String VERIFY_CODE = "verifyCode:%s";

    /**
     * 登录前台验证码存入redis的key前缀
     */
    public static final String VERIFY_CODE_FRONT = "verifyCodeFront:";

    /**
     * 登录图形验证码存入redis的key的时间
     */
    public static final Integer VERIFY_CODE_EXPIRE_SECONDS = 100;

    /**
     * 最新汇率
     */
    public static final String EXCHANGE_RATE = "exchangeRate";

    /**
     * paypal商户的token前缀
     */
    public static final String PAYPAL_TOKEN = "paypalToken:";

    /**
     * 试卷题目的前缀
     */
    public static final String PAPER_ITEM = "paperItem:";


    /**
     * 订单号
     */
    public static final String LOCK_MBTI = "lock:order:mbti:%s";

}
