package com.miaowen.bh1xlhw.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * BooleanEnum :事件类型
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07
 */

@Getter
@AllArgsConstructor
public enum EventTypeEnum {
    CLICK(1, "点击"),
    PAGE(2, "页面");

    private final Integer value;
    private final String description;

    public static EventTypeEnum fromValue(int value) {
        // 遍历枚举类型的所有值
        for (EventTypeEnum type : EventTypeEnum.values()) {
            // 如果当前值的value等于传入的value，则返回当前枚举类型
            if (type.value == value) {
                return type;
            }
        }
        // 如果没有找到匹配的枚举类型，则抛出异常
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}
