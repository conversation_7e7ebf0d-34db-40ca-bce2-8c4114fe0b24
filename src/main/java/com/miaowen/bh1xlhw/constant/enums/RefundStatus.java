package com.miaowen.bh1xlhw.constant.enums;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;


/**
 * RefundStatus to be used.
 */
public enum RefundStatus {
    /**
     * The refund was cancelled.
     */
    CANCELLED(),

    /**
     * The refund could not be processed.
     */
    FAILED,

    /**
     * The refund is pending. For more information, see status_details.reason.
     */
    PENDING,

    /**
     * The funds for this transaction were debited to the customer's account.
     */
    COMPLETED,

    /**
     * Unknown values will be mapped by this enum member
     */
    _UNKNOWN;


    private static final TreeMap<String, RefundStatus> valueMap = new TreeMap<>();
    private String value;

    static {
        CANCELLED.value = "CANCELLED";
        FAILED.value = "FAILED";
        PENDING.value = "PENDING";
        COMPLETED.value = "COMPLETED";
        _UNKNOWN.value = null;

        valueMap.put("CANCELLED", CANCELLED);
        valueMap.put("FAILED", FAILED);
        valueMap.put("PENDING", PENDING);
        valueMap.put("COMPLETED", COMPLETED);
    }

    /**
     * Returns the enum member associated with the given string value.
     * @param toConvert String value to get enum member.
     * @return The enum member against the given string value.
     * @throws IOException when provided value is not mapped to any enum member.
     */
    @JsonCreator
    public static RefundStatus constructFromString(String toConvert) throws IOException {
        RefundStatus enumValue = fromString(toConvert);
        if (enumValue == null) {
            throw new IOException("Unable to create enum instance with value: " + toConvert);
        }
        return enumValue;
    }

    /**
     * Returns the enum member associated with the given string value.
     * @param toConvert String value to get enum member.
     * @return The enum member against the given string value.
     */
    public static RefundStatus fromString(String toConvert) {
        if (!valueMap.containsKey(toConvert)) {
            return _UNKNOWN;
        }
        return valueMap.get(toConvert);
    }

    /**
     * Returns the string value associated with the enum member.
     * @return The string value against enum member.
     */
    @JsonValue
    public String value() {
        return value;
    }
        
    /**
     * Get string representation of this enum.
     */
    @Override
    public String toString() {
        if (value == null) {
            return null;
        }
        return value.toString();
    }

    /**
     * Convert list of RefundStatus values to list of string values.
     * @param toConvert The list of RefundStatus values to convert.
     * @return List of representative string values.
     */
    public static List<String> toValue(List<RefundStatus> toConvert) {
        if (toConvert == null) {
            return null;
        }
        List<String> convertedValues = new ArrayList<>();
        for (RefundStatus enumValue : toConvert) {
            convertedValues.add(enumValue.value);
        }
        return convertedValues;
    }
} 