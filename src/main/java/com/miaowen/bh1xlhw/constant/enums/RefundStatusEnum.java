package com.miaowen.bh1xlhw.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@Getter
@AllArgsConstructor
public enum RefundStatusEnum {
    UNDEFINED(0,"undefined"),
    PENDING(1,"pending"),
    SUCCEEDED(2,"succeeded"),
    FAILED(3,"failed"),
    CANCELED(4,"canceled"),
    REQUIRES_ACTION(5,"requires_action"),
    ;
    private final Integer status;
    private final String value;
    public static RefundStatusEnum getByValue(String value) {
        for (RefundStatusEnum refundStatusEnum : RefundStatusEnum.values()) {
            if (refundStatusEnum.getValue().equals(value)) {
                return refundStatusEnum;
            }
        }
        return UNDEFINED;
    }

}
