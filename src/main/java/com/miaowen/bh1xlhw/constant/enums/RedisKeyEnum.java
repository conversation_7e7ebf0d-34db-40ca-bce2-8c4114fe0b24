package com.miaowen.bh1xlhw.constant.enums;

import lombok.Getter;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/6/13 10:35
 */
@Getter
public enum RedisKeyEnum {

    PLATFORM_EVENT("platform:eventId:%s:%s", 60 * 60),
    PLATFORM_ADVERTISE("platform:advertise:ad:%s", 60 * 60),
    ARTICLE_DETAIL_LANGUAGE("articleDetail:language:%s:tgid:%s", 60 * 60),
    SYSTEM_EVENT("system:event:%s", 60 * 60);

    private final String pattern;
    private final int expireSeconds;

    RedisKeyEnum(String pattern, int expireSeconds) {
        this.pattern = pattern;
        this.expireSeconds = expireSeconds;
    }

    public String getKey(Object... args) {
        return String.format(pattern, args);
    }
}
