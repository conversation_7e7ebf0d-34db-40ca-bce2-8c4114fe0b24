package com.miaowen.bh1xlhw.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */

@Getter
@AllArgsConstructor
public enum GoodsTypeEnum {
    /**
     * 多语言商品
     */
    MULTILINGUAL(1,"en"),
    /**
     * 繁体商品
     */
    TRADITIONAL(2,"zh-TW"),
    ;
    private final Integer value;

    private final String defaultLanguage;

    public static GoodsTypeEnum getByValue(Integer value) {
        for (GoodsTypeEnum goodsTypeEnum : GoodsTypeEnum.values()) {
            if (goodsTypeEnum.getValue().equals(value)) {
                return goodsTypeEnum;
            }
        }
        return MULTILINGUAL;
    }

}
