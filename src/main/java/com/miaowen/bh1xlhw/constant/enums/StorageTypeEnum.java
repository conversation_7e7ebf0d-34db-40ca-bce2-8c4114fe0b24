package com.miaowen.bh1xlhw.constant.enums;

/**
 * BooleanEnum :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-25
 */


public enum StorageTypeEnum {
    OSS(1, "oss服务器"),
    KS3(2, "ks3服务器"),
    LOCAL(3, "本地服务器");
    private  Integer code;
    private  String name;


    // 构造函数
    StorageTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    // Getter方法
    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
