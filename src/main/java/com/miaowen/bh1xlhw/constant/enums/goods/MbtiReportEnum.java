package com.miaowen.bh1xlhw.constant.enums.goods;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * MBTI报告类型枚举
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-11
 */
@Getter
@AllArgsConstructor
public enum MbtiReportEnum {

    // ================================ 基础报告类型 ================================
    CAREER_ADVANTAGES(1, "careerAdvantages", "职场优势", "career"),
    EIGHT_DIMENSIONAL_REPORT(2, "eightDimensionalReport", "八维报告", "eight-dimensions"),
    LOVE_REPORT(3, "loveReport", "恋爱报告", "analysis-love"),
    ;

    /**
     * 报告类型
     */
    private final Integer contentType;

    /**
     * 报告类型值（英文标识）
     */
    private final String value;

    /**
     * 报告类型标签（中文名称）
     */
    private final String label;
    /**
     * 前端页面路由
     */
    private final String webPage;

    /**
     * 根据ID获取枚举
     *
     * @param contentType 报告类型ID
     * @return MbtiReportEnum
     */
    public static MbtiReportEnum getByContentType(Integer contentType) {
        if (contentType == null) {
            return null;
        }
        for (MbtiReportEnum reportEnum : values()) {
            if (reportEnum.getContentType().equals(contentType)) {
                return reportEnum;
            }
        }
        return null;
    }

    public static MbtiReportEnum getByValue(String value) {
        if (!StringUtils.hasText(value)) {
            return null;
        }
        for (MbtiReportEnum reportEnum : values()) {
            if (reportEnum.getValue().equals(value)) {
                return reportEnum;
            }
        }
        return null;
    }

}
