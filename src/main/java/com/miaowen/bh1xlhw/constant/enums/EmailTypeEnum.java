package com.miaowen.bh1xlhw.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 邮件类型
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-15
 */
@Getter
@AllArgsConstructor
public enum EmailTypeEnum {
    /**
     * 支付成功
     */
    REPORT("report"),

    /**
     * 2小时未支付状态
     */
    UNPAID_2("noPay2"),

    /**
     * 24小时未支付状态
     */
    UNPAID_24("noPay24"),

    /**
     * 48小时未支付状态
     */
    UNPAID_48("noPay48"),

    /**
     * 96小时未支付状态
     */
    UNPAID_96("noPay96");

    private final String code;


}
