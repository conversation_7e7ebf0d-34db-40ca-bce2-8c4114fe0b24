package com.miaowen.bh1xlhw.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */

@Getter
@AllArgsConstructor
public enum PaymentStatusEnum {
    /**
     * 初始化
     */
    Initialization(0),
    /**
     * 下单
     */
    Placed(1),
    /**
     * 成功
     */
    Success(2),
    /**
     * 等待
     */
    Waiting(3),
    /**
     * 失败
     */
    Failure(4),
    /**
     * 已退款
     */
    Refund(5),
    ;

    private final Integer type;
}
