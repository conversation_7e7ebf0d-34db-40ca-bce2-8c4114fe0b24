package com.miaowen.bh1xlhw.constant.enums;

/**
 * BooleanEnum :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-25
 */


public enum AccRoleEnum {
    NORMAL_USER(0, "普通用户"),
    SUPER_ADMIN(1, "超级管理员"),
;
    private  Integer code;
    private  String name;


    // 构造函数
    AccRoleEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    // Getter方法
    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}
