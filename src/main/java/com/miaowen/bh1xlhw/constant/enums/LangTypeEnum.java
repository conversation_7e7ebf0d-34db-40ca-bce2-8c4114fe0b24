package com.miaowen.bh1xlhw.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description 语言类型枚举类
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 10:46
 */
@Getter
@AllArgsConstructor
public enum LangTypeEnum {
    LANG_CN("zh-cn", "简体中文"),
    LANG_EN("en", "英语"),
    LANG_TW("zh-tw", "繁体中文"),
    LANG_JA("ja", "日语"),
    LANG_DE("de", "德语"),
    LANG_PT("pt", "葡萄牙语"),
    LANG_FR("fr", "法语"),
    LANG_ES("es", "西班牙语"),
    ;
    private final String code;
    private final String name;



}
