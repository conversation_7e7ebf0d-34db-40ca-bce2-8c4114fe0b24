package com.miaowen.bh1xlhw.constant;

import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Description 系统通用 常量
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2024/3/19 15:22
 */
public class SystemConstant {

    public static final String SUPER_ADMIN = "SuperAdmin";
    /**
     * 后台系统默认语言
     */
    public static final String DEFAULT_LANGUAGE = "zh";

    /**
     * 用户端系统默认语言
     */
    public static final String DEFAULT_LANGUAGE_MULTI = "en";


    /**
     * 获取推广链接
     * 通配符按顺序的含义如下
     * @param domain 域名
     * @param webPackageName 前端包名
     * @param tgId 推广id
     * @param languageCode 语言代码
     * @param source 来源
     * @return tgLinkUrl 推广链接url
     */
    public static String buildTgLink(String domain, String webPackageName, String tgId, String languageCode, String source){
        StringBuilder sb = new StringBuilder();
        sb.append("https://").append(domain).append("/").append(webPackageName).append("?tgid=").append(tgId);
        if (StringUtils.hasText(languageCode)){
            sb.append("&lang=").append(languageCode);
        }
        if (StringUtils.hasText(source)){
            sb.append("&source=").append(source);
        }
        return sb.toString();
    }

    /**
     * 后台新增用户默认密码
     */
    public static final String DEFAULT_PASSWORD = "123456";
    public static final String PERMIT_ATTRIBUTE = "permitAll";
    public static final List<String> METHOD_TYPE_LIST = Arrays.asList("get", "post", "delete", "put", "any");


}
