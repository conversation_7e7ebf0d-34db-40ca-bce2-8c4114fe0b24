package com.miaowen.bh1xlhw.constant;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * StringConsTant :
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-04-27
 */
public class CommonConstant {
    /**
     * 空串
     */
    public final static String EMPTY_ARRAY_JSON= "[]";
    public final static String EMPTY_STRING= "";
    public final static String DELETE_TIME = "delete_time";
    public final static String ID = "id";
    public final static String CODE = "code";
    public final static String STATUS = "status";
    public final static String IS_DEFAULT = "is_default";
    public final static String DESC = "desc";
    public final static String ASC = "asc";


    public final static String STORAGE_OSS = "oss";
    public final static String STORAGE_R2 = "r2";
    public final static String STORAGE_KS3 = "ks3";
    public final static String STORAGE_LOCAL = "local";
    public final static String STORAGE_TYPE = "storage.type";

    public static final String EMAIL_TEMPLATE_ID = "email_template_id";
    public static final String EMAIL_PROMOTION_TEMPLATE_ID = "email_promotion_template_id";


    public static String listToString(List<String> codes) {
        if (CollectionUtils.isEmpty(codes)){
            return CommonConstant.EMPTY_STRING;
        }
        StringJoiner sj = new StringJoiner(",");
        codes.forEach(sj::add);
        return sj.toString();
    }

    public static String idsToString(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return CommonConstant.EMPTY_STRING;
        }
        StringJoiner sj = new StringJoiner(",");
        ids.forEach(id -> sj.add(String.valueOf(id)));
        return sj.toString();
    }

   public static List<Integer> stringToIds(String string) {
       if (!StringUtils.hasText(string)){
           return Collections.emptyList();
       }
       return Arrays.stream(string.split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }

    public  static List<String> stringToList(String string) {
        if (!StringUtils.hasText(string)){
            return Collections.emptyList();
        }
        return Arrays.stream(string.split(",")).collect(Collectors.toList());
    }
}
