package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.ResultWeidu;
import com.miaowen.bh1xlhw.model.query.PageForm;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:51
 */
public interface ResultWeiduService {
    Page<ResultWeidu> getByTag(PageForm page, String tag, boolean isMulti);

    Integer saveOrUpdateResultWeidu(ResultWeidu resultWeidu);

    void deleteResultWeidu(List<Integer> ids);

    void updateResultWeiduById(ResultWeidu resultWeidu);
}
