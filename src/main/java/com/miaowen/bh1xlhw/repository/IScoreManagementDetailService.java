package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.ScoreManagementDetail;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:51
 */
public interface IScoreManagementDetailService extends IService<ScoreManagementDetail> {
  void removeByScoreId(List<Integer> scoreIds);

  Map<Integer,List<ScoreManagementDetail>> mapByScoreIds(List<Integer> scoreIds);

  List<ScoreManagementDetail> listByScoreId(List<Integer>  scoreIds);
}
