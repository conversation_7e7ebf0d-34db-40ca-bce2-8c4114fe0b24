package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.EventLog;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;

/**
 * @Description
 * @Author：huang<PERSON>
 * @Date：2025/5/8 16:03
 */
public interface EventLogService extends IService<EventLog> {

    int countByCondition(BrowseCountForm browseCountForm);

}
