package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.EventLog;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 16:03
 */
public interface EventLogService extends IService<EventLog> {

    int countByCondition(BrowseCountForm browseCountForm);

}
