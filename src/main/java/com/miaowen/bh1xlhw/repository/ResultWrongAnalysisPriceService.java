package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysis;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysisPrice;

import java.util.List;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 10:22
 */
public interface ResultWrongAnalysisPriceService extends IService<ResultWrongAnalysisPrice> {

    void deleteByTag(String tag, boolean isMulti);

    List<ResultWrongAnalysisPrice> getByTag(String tag, boolean isMulti);

}
