package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.AccRole;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_role(角色表)】的数据库操作Service
 * @createDate 2025-05-06 15:41:28
 */
public interface AccRoleService extends IService<AccRole> {

    void logicRemoveById(Integer roleId);

    List<AccRole> listExistByIds(Set<Integer> roleIds);

    List<AccRole> getRolesByIds(List<Integer> ids);
}
