package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.bo.DomainPlatformCountBO;
import com.miaowen.bh1xlhw.model.entity.DomainPlatform;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_domin_platform(记录域名平台使用情况)】的数据库操作Service
 * @createDate 2025-05-08 15:42:34
 */
public interface DomainPlatformService extends IService<DomainPlatform> {

    List<DomainPlatformCountBO> listDomainPlatformCount();

}
