package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.Agents;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.goods.AgentsPageForm;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_agents(代理商)】的数据库操作Service
 * @createDate 2025-05-07 11:28:54
 */
public interface AgentsService extends IService<Agents> {

    void logicRemoveById(Integer id);

    void recoverById(Integer id);

    Page<Agents> pageInfo(AgentsPageForm pageForm);

    Map<Integer,Agents> mapNameByIds(List<Integer> agentIds);

    List<Agents> listAll();

    Agents getExistById(Integer agentId);

    List<Agents> listExistByIds(List<Integer> agentIds);
}
