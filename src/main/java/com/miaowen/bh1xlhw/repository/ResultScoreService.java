package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.ResultScore;
import com.miaowen.bh1xlhw.model.query.PageForm;

import java.util.List;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:51
 */
public interface ResultScoreService {

    Page<ResultScore> getResultScoreByTag(PageForm page, String tag, boolean isMulti);

    ResultScore getResultScoreById(Integer id);

    Integer saveResultScore(ResultScore resultScore);

    void updateResultScore(ResultScore resultScore);

    void deleteResultScore(List<Integer> ids);

}
