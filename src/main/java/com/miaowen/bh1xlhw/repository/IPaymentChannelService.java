package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.PaymentChannel;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.vo.payment.PaymentChannelVO;

import java.util.List;

/**
 * 针对表【po_payment_pattern(支付方式模式配置)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 09:50:56
 */
public interface IPaymentChannelService extends IService<PaymentChannel> {


    void logicRemoveById(Integer id);

    Page<PaymentChannel> pageInfo(PageForm pageForm, String name);

    void updateNoDefault(Integer id);

    List<PaymentChannel> allList();

}
