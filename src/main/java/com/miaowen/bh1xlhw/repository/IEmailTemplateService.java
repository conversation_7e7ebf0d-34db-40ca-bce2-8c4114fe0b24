package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.EmailTemplate;


/**
 * @Description TODO
 * <AUTHOR>
 */
public interface IEmailTemplateService extends IService<EmailTemplate> {
    EmailTemplate getTemplateByType(String templateType, String emailType,Integer goodsType);

    EmailTemplate getTemplateByType(String templateType, String emailType, Integer goodsCategoryId, Integer goodsType);

}
