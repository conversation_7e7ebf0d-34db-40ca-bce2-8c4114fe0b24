package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.ResultScoreLang;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:52
 */
public interface ResultScoreLangService {

    List<ResultScoreLang> findByScoreId(Integer scoreId);

    void add(ResultScoreLang resultScoreLang);

    void deleteByScoreId(Integer scoreId);

    void deleteByScoreIds(List<Integer> ids);

    void update(ResultScoreLang resultScoreLang);

}
