package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.Domain;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Platform;
import com.miaowen.bh1xlhw.model.query.operation.DomainPageForm;

import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_domin(域名管理)】的数据库操作Service
 * @createDate 2025-05-07 16:15:09
 */
public interface DomainService extends IService<Domain> {

    void logicRemoveById(Integer id);

    void recoverById(Integer id);

    Page<Domain> pageInfo(DomainPageForm pageForm);

    Map<Integer, Domain> mapNameByIds(List<Integer> domainIds);

    List<Domain> listAll();

    Map<Integer, Domain> mapAll();

    Domain getByDomain(String domain);


    Domain getByDomain(String domain, Integer goodsTypeId, Integer goodsType);



    /**
     * 正则获取二级域名
     */
    static String getDomain(String origin) {
        // 正则匹配协议 + 域名部分，提取最后两个有效部分
        Pattern pattern = Pattern.compile("https?://([^/:]+)");
        Matcher matcher = pattern.matcher(origin);

        if (matcher.find()) {
            // 提取二级域名
            return matcher.group(1);
        } else {
            return "";
        }
    }

}
