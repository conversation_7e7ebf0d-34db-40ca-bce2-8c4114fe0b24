package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.RefundStatistic;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.LocalDate;
import java.util.List;

/**
 * 针对表【po_refund_statistic(退款统计表,每天统计前一天的数据)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27 21:13:33
 */
public interface RefundStatisticService extends IService<RefundStatistic> {

    RefundStatistic getByDate(LocalDate lastMonthDay);

    List<RefundStatistic> listRefundStatisticByMonth(LocalDate localDate, LocalDate localDate1);
}
