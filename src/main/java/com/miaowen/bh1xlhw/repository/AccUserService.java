package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.AccRole;
import com.miaowen.bh1xlhw.model.entity.AccUser;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_user(后台用户表)】的数据库操作Service
 * @createDate 2025-05-06 15:41:28
 */
public interface AccUserService extends IService<AccUser> {

    /**
     * 获取登录用户信息
     */
    AccUser getByUsernameOrEmail(String username);

    List<AccRole> getRolesByUserId(Integer userId);

    AccUser getExistById(Integer userId);

}
