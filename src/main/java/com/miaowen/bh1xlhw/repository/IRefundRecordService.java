package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.RefundRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;

/**
 * 针对表【po_refund_record(后台退款记录表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-23 14:58:43
 */
public interface IRefundRecordService extends IService<RefundRecord> {

    Integer sumRefundRecord(Long orderId);

    Integer sumByDate(LocalDate lastMonthDay);

}
