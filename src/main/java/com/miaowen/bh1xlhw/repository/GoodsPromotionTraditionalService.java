package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.GoodsPromotionMultilingual;
import com.miaowen.bh1xlhw.model.entity.GoodsPromotionTraditional;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionPageForm;

import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 针对表【po_goods_promotion_traditional(推广商品多语言)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 15:13:26
 */
public interface GoodsPromotionTraditionalService extends IService<GoodsPromotionTraditional> {
    void logicRemoveById(Integer id);

    GoodsPromotionTraditional getExistById(Integer id);

    List<GoodsPromotionTraditional> listAll();

    Page<GoodsPromotionTraditional> pageInfo(GoodsPromotionPageForm pageForm);

    List<GoodsPromotionTraditional> listByPriceAndOperationId(Integer priceId, Integer operationId);

    List<GoodsPromotionTraditional> listByAdvertiseAccountId(Integer advertiseAccountId);

    Map<Integer, GoodsPromotionTraditional> mapById(List<Integer> goodsTraditionalPromotionIds);

    GoodsPromotionTraditional getByTgId(String tgId);

    List<GoodsPromotionTraditional> listTgId(List<String> traditionalTiIds);

    void recoverBatchByIds(List<Integer> ids);
    String getTgIdById(Integer id);
}
