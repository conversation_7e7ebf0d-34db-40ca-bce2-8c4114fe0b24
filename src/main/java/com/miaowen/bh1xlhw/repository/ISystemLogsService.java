package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.SystemLogs;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.log.LogPageForm;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 针对表【po_system_logs(操作日志表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-21 20:32:55
 */
public interface ISystemLogsService extends IService<SystemLogs> {

    void logicBatchDelete(@Size(max = 500) List<Integer> ids);

    Page<SystemLogs> pages(LogPageForm form);
}
