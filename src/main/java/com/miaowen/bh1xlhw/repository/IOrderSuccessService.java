package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.order.OrderSuccess;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;

/**
 * 针对表【po_order_success(交易订单成功表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 19:20:54
 */
public interface IOrderSuccessService extends IService<OrderSuccess> {

    Page<OrderSuccess> pageInfo(OrderForm orderForm);

}
