package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.ResultWeiduLang;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
public interface ResultWeiduLangService {
    List<ResultWeiduLang> findByWeiduId(Integer weiduId);

    void deleteByWeiduId(Integer weiduId);

    void deleteByWeiduIds(List<Integer> weiduIds);

    void saveWeiduLang(ResultWeiduLang resultWeiduLang);

}
