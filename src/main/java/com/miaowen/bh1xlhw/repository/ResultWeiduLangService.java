package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.ResultWeiduLang;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:52
 */
public interface ResultWeiduLangService {
    List<ResultWeiduLang> findByWeiduId(Integer weiduId);

    void deleteByWeiduId(Integer weiduId);

    void deleteByWeiduIds(List<Integer> weiduIds);

    void saveWeiduLang(ResultWeiduLang resultWeiduLang);

}
