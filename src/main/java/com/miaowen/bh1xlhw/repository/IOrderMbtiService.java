package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.order.OrderMbti;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;

import java.util.List;


/**
 * 针对表【po_order_mbti(mbti子订单表)】的数据库操作Service
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-10 15:01:08
 */
public interface IOrderMbtiService extends IService<OrderMbti> {


    Page<OrderMbti> pageInfo(OrderForm orderForm);

    void dealRefundRecord(Integer sumRefundAmount, Integer status, OrderMbti order);

    OrderMbti getByMainOutTradeNo(String mainOutTradeNo, Integer contentType);

    List<OrderMbti> listByMainOutTradeNoType(String mainOutTradeNo, List<Integer> contentTypeList);


    List<OrderMbti> listByMainOutTradeNo(String mainOutTradeNo);
}
