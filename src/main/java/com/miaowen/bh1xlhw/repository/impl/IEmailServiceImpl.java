package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.EmailMapper;
import com.miaowen.bh1xlhw.model.entity.Email;
import com.miaowen.bh1xlhw.repository.IEmailService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_merchant(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmailServiceImpl extends ServiceImpl<EmailMapper, Email>
        implements IEmailService {

    @Override
    public void incrementSendNumbersById(Integer id) {
        baseMapper.updateNumbers(id);
    }

    @Override
    public Email getGoogleEmail() {
        return baseMapper.selectOne(Wrappers.<Email>lambdaQuery()
                .eq(Email::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .eq(Email::getStatus, 1)
                .last(SQLConstant.LIMIT_ONE));

    }

    @Override
    public List<Email> getEmailByIds(String emailIds) {
        List<Integer> idList = Arrays.stream(emailIds.split(","))
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        return baseMapper.selectList(Wrappers.<Email>lambdaQuery()
                .in(Email::getId, idList)
                .eq(Email::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .eq(Email::getStatus, 1)
                .orderByAsc(Email::getSendNumbers)
                .last(SQLConstant.LIMIT_ONE));
    }


    @Override
    public Map<Integer, Email> listExistByIds(Collection<Integer> emailIds) {
        if (CollectionUtils.isEmpty(emailIds)) {
            return Collections.emptyMap();
        }
        List<Email> emails = baseMapper.selectList(Wrappers.<Email>lambdaQuery()
                .in(Email::getId, emailIds)
                .eq(Email::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(emails)) {
            return Collections.emptyMap();
        }

        return StreamUtil.map(emails, Email::getId);
    }
}



