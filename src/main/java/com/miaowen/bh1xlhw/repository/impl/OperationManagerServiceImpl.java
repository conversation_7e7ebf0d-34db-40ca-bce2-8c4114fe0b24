package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.OperationManagerMapper;
import com.miaowen.bh1xlhw.model.entity.OperationManager;
import com.miaowen.bh1xlhw.model.entity.OperationManager;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerQryForm;
import com.miaowen.bh1xlhw.repository.OperationManagerService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_operation_manager(运营主管)】的数据库操作Service实现
 * @createDate 2025-05-07 14:23:05
 */
@Service
public class OperationManagerServiceImpl extends ServiceImpl<OperationManagerMapper, OperationManager>
    implements OperationManagerService {
    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public void recoverById(Integer id) {
        baseMapper.recoverById(id);
    }

    @Override
    public Page<OperationManager> pageInfo(OperationManagerQryForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(OperationManager.class)
            .eq(OperationManager::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .like(Objects.nonNull(pageForm.getName()),OperationManager::getName, pageForm.getName())
            .like(Objects.nonNull(pageForm.getWorkNo()),OperationManager::getWorkNo, pageForm.getWorkNo())
            .orderByDesc(OperationManager::getCreateTime));
    }

    @Override
    public List<OperationManager> listExistByIds(Set<Integer> managerIds) {
        if (CollectionUtils.isEmpty(managerIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(OperationManager.class)
            .in(OperationManager::getId, managerIds)
            .eq(OperationManager::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .orderByDesc(OperationManager::getCreateTime));
    }

    @Override
    public Map<Integer, OperationManager> mapNameByIds(Set<Integer> managerIds) {
        if (CollectionUtils.isEmpty(managerIds)) {
            return Collections.emptyMap();
        }

        List<OperationManager> operationManagerList = baseMapper.selectList(Wrappers.<OperationManager>lambdaQuery()
            .select(OperationManager::getId, OperationManager::getName, OperationManager::getWorkNo)
            .in(OperationManager::getId, managerIds)
            .eq(OperationManager::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(operationManagerList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(operationManagerList, OperationManager::getId);
    }

    @Override
    public List<OperationManager> allExist() {
        return baseMapper.selectList(Wrappers.lambdaQuery(OperationManager.class)
            .eq(OperationManager::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<Integer> getIdsByManagerName(String operationManagerName, String operationManagerUserCode) {
        List<OperationManager> goodsMultilingualList =
            baseMapper.selectList(Wrappers.lambdaQuery(OperationManager.class)
                .select(OperationManager::getId)
                .in(StringUtils.hasText(operationManagerName), OperationManager::getName, operationManagerName)
                .eq(StringUtils.hasText(operationManagerUserCode), OperationManager::getWorkNo, operationManagerUserCode)
                .eq(OperationManager::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(goodsMultilingualList)) {
            return Collections.emptyList();
        }
        return StreamUtil.fetchList(goodsMultilingualList, OperationManager::getId);
    }

    @Override
    public OperationManager getByWorkNo(String operationManagerWorkNo) {
        return null;
    }
}




