package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.model.entity.order.OrderEnvironmental;
import com.miaowen.bh1xlhw.repository.IOrderEnvironmentalService;
import com.miaowen.bh1xlhw.mapper.OrderEnvironmentalMapper;
import org.springframework.stereotype.Service;

/**
 * 针对表【po_order_environmental(交易订单环境表,交易订单的水平拆分表)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 19:20:54
 */
@Service
public class IOrderEnvironmentalServiceImpl extends ServiceImpl<OrderEnvironmentalMapper, OrderEnvironmental>
    implements IOrderEnvironmentalService {

}




