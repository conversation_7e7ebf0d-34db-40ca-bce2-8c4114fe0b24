package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.AccRolePermissionMapper;
import com.miaowen.bh1xlhw.model.entity.AccRolePermission;
import com.miaowen.bh1xlhw.repository.AccRolePermissionService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_role_permission(角色权限关联表)】的数据库操作Service实现
 * @createDate 2025-05-06 15:41:28
 */
@Service
public class AccRolePermissionServiceImpl extends ServiceImpl<AccRolePermissionMapper, AccRolePermission>
    implements AccRolePermissionService{

    @Override
    public void logicRemoveByPermissionIds(Set<Integer> permissionIds) {
        baseMapper.logicDelete(Wrappers.lambdaQuery(AccRolePermission.class)
            .in(AccRolePermission::getPermissionId, permissionIds));
    }

    @Override
    public void logicRemoveByRoleId(Integer roleId) {
        baseMapper.logicDelete(Wrappers.lambdaQuery(AccRolePermission.class)
            .in(AccRolePermission::getRoleId, roleId));
    }

    @Override
    public List<AccRolePermission> listByRoleIds(Collection<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(AccRolePermission.class)
            .in(AccRolePermission::getRoleId, roleIds)
            .eq(AccRolePermission::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }
}




