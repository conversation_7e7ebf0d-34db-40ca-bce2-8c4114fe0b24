package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.PaymentMapper;
import com.miaowen.bh1xlhw.model.entity.SystemPayment;
import com.miaowen.bh1xlhw.repository.IPaymentService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_payment(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IPaymentServiceImpl extends ServiceImpl<PaymentMapper, SystemPayment>
        implements IPaymentService {

    @Override
    public void logicDelete(List<Integer> ids) {
        baseMapper.logicDeleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoverBatchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        baseMapper.recoverBatchIds(ids);

    }
}



