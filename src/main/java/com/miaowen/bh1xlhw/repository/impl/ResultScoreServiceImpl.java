package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.mapper.ResultScoreMapper;
import com.miaowen.bh1xlhw.model.entity.ResultScore;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.repository.ResultScoreService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
@Service
public class ResultScoreServiceImpl extends ServiceImpl<ResultScoreMapper, ResultScore>
        implements ResultScoreService {

    @Override
    public Page<ResultScore> getResultScoreByTag(PageForm page, String tag, boolean isMulti) {
        LambdaQueryWrapper<ResultScore> queryWrapper = Wrappers.lambdaQuery(ResultScore.class)
                .eq(ResultScore::getTag, tag)
                .eq(ResultScore::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue())
                .orderByDesc(ResultScore::getId);

        return baseMapper.selectPage(page.qry(), queryWrapper);
    }

    @Override
    public ResultScore getResultScoreById(Integer id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Integer saveResultScore(ResultScore resultScore) {
        baseMapper.insert(resultScore);
        return resultScore.getId();
    }

    @Override
    public void updateResultScore(ResultScore resultScore) {
        baseMapper.updateById(resultScore);
    }

    @Override
    public void deleteResultScore(List<Integer> ids) {
        baseMapper.deleteBatchIds(ids);
    }
}
