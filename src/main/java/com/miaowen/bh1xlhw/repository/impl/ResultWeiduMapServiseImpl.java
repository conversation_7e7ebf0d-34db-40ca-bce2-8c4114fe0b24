package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.ResultWeiduMapMapper;
import com.miaowen.bh1xlhw.model.entity.ResultWeiduMap;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.repository.ResultWeiduMapService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 16:15
 */
@Service
public class ResultWeiduMapServiseImpl extends ServiceImpl<ResultWeiduMapMapper, ResultWeiduMap>
        implements ResultWeiduMapService {

    @Override
    public Page<ResultWeiduMap> getByWeiduId(PageForm page, Integer weiduId) {
        LambdaQueryWrapper<ResultWeiduMap> wrapper = Wrappers.lambdaQuery(ResultWeiduMap.class)
                .eq(ResultWeiduMap::getWeiduId, weiduId);
        return baseMapper.selectPage(page.qry(), wrapper);
    }

    @Override
    public ResultWeiduMap getById(Integer id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Integer editOrSave(ResultWeiduMap resultWeiduMap) {
        if (resultWeiduMap.getId() == null) {
            //新增
            this.save(resultWeiduMap);
        } else {
            //修改
            this.updateById(resultWeiduMap);
        }
        return resultWeiduMap.getId();
    }

    @Override
    public void delete(List<Integer> ids) {
        baseMapper.deleteBatchIds(ids);
    }

    @Override
    public void deleteByWeiduId(Integer weiduId) {
        LambdaQueryWrapper<ResultWeiduMap> wrapper = Wrappers.lambdaQuery(ResultWeiduMap.class)
                .eq(ResultWeiduMap::getWeiduId, weiduId);
        this.remove(wrapper);
    }
}
