package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.EmailTemplateContentMapper;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;
import com.miaowen.bh1xlhw.repository.IEmailTemplateContentService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_merchant(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmailTemplateContentServiceImpl extends ServiceImpl<EmailTemplateContentMapper, EmailTemplateContent>
        implements IEmailTemplateContentService {

    @Override
    public List<EmailTemplateContent> getByTemplateId(Integer id) {
        return baseMapper.selectList(Wrappers.<EmailTemplateContent>lambdaQuery()
                .eq(EmailTemplateContent::getEmailTemplateId, id)
                .eq(EmailTemplateContent::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public Map<String, EmailTemplateContent> mapByTemplateId(Integer id) {
        List<EmailTemplateContent> templateContentList = getByTemplateId(id);
        if (CollectionUtils.isEmpty(templateContentList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(templateContentList, EmailTemplateContent::getLanguageCode);
    }

}



