package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.PriceMapper;
import com.miaowen.bh1xlhw.model.entity.Price;
import com.miaowen.bh1xlhw.model.query.operation.PricePageForm;
import com.miaowen.bh1xlhw.repository.IPriceService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_price(价格方案表)】的数据库操作Service实现
 * @createDate 2025-05-07 16:56:57
 */
@Service
public class IPriceServiceImpl extends ServiceImpl<PriceMapper, Price>
        implements IPriceService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public void recoverById(Integer id) {
        baseMapper.recoverById(id);

    }

    @Override
    public Page<Price> pageInfo(PricePageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(Price.class)
                .eq(Price::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(StringUtils.isNotBlank(pageForm.getName()), Price::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getType()), Price::getType, pageForm.getType())
                .orderByDesc(Price::getCreateTime));
    }

    @Override
    public Map<Integer, Price> mapByIds(List<Integer> priceIds) {
        if (CollectionUtils.isEmpty(priceIds)) {
            return Collections.emptyMap();
        }
        List<Price> prices = baseMapper.selectList(Wrappers.lambdaQuery(Price.class)
                .eq(Price::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .in(Price::getId, priceIds));
        if (CollectionUtils.isEmpty(prices)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(prices, Price::getId);
    }

    @Override
    public List<Price> listAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(Price.class)
                .eq(Price::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(Price::getCreateTime));
    }
}




