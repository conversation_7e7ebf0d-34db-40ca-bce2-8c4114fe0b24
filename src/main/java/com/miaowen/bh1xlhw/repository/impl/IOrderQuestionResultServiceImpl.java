package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.model.entity.order.OrderQuestionResult;
import com.miaowen.bh1xlhw.repository.IOrderQuestionResultService;
import com.miaowen.bh1xlhw.mapper.OrderQuestionResultMapper;
import org.springframework.stereotype.Service;

/**
 * 针对表【po_order_question_result(交易订单答题结果,交易订单的水平拆分表)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 19:20:54
 */
@Service
public class IOrderQuestionResultServiceImpl extends ServiceImpl<OrderQuestionResultMapper, OrderQuestionResult>
    implements IOrderQuestionResultService {

}




