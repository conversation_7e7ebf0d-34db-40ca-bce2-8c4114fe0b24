package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.AccPermissionMapper;
import com.miaowen.bh1xlhw.model.entity.AccPermission;
import com.miaowen.bh1xlhw.repository.AccPermissionService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_permission(权限表)】的数据库操作Service实现
 * @createDate 2025-05-06 15:41:28
 */
@Service
public class AccPermissionServiceImpl extends ServiceImpl<AccPermissionMapper, AccPermission>
    implements AccPermissionService {

    @Override
    public void logicRemoveByIds(Set<Integer> permissionIds) {
        baseMapper.logicDeleteBatchIds(permissionIds);
    }

    @Override
    public List<AccPermission> listExistByIds(Set<Integer> permissionIds) {
        if (CollectionUtils.isEmpty(permissionIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(AccPermission.class)
            .in(AccPermission::getId, permissionIds)
            .eq(AccPermission::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .orderByAsc(Arrays.asList(AccPermission::getSort, AccPermission::getId)));
    }

    @Override
    public List<AccPermission> listExistAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(AccPermission.class)
            .eq(AccPermission::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .orderByAsc(Arrays.asList(AccPermission::getSort, AccPermission::getId)));

    }

    @Override
    public AccPermission getByPath(String path) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(AccPermission.class)
            .eq(AccPermission::getPath, path)
            .eq(AccPermission::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .last(SQLConstant.LIMIT_ONE));
    }
}




