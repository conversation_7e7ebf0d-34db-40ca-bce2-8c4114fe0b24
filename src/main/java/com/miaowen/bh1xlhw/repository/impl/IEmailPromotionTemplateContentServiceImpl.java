package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.EmailPromotionTemplateContentMapper;
import com.miaowen.bh1xlhw.mapper.EmailTemplateContentMapper;
import com.miaowen.bh1xlhw.model.entity.EmailPromotionTemplateContent;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;
import com.miaowen.bh1xlhw.repository.IEmailPromotionTemplateContentService;
import com.miaowen.bh1xlhw.repository.IEmailTemplateContentService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_merchant(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmailPromotionTemplateContentServiceImpl extends ServiceImpl<EmailPromotionTemplateContentMapper, EmailPromotionTemplateContent>
        implements IEmailPromotionTemplateContentService {

}



