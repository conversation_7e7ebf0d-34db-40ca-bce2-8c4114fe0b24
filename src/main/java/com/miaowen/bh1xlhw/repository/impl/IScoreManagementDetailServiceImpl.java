package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.ScoreManagementDetailMapper;
import com.miaowen.bh1xlhw.model.entity.ScoreManagementDetail;
import com.miaowen.bh1xlhw.repository.IScoreManagementDetailService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:52
 */
@Service
public class IScoreManagementDetailServiceImpl extends ServiceImpl<ScoreManagementDetailMapper, ScoreManagementDetail>
        implements IScoreManagementDetailService {

    @Override
    public void removeByScoreId(List<Integer> scoreIds) {
        UpdateWrapper<ScoreManagementDetail> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("score_id", scoreIds);
        this.remove(updateWrapper);
    }

    @Override
    public Map<Integer, List<ScoreManagementDetail>> mapByScoreIds(List<Integer> scoreIds) {
        if (scoreIds == null || scoreIds.isEmpty()) {
            return Collections.emptyMap();
        }

        // 直接查询ScoreManagementDetail实体列表
        List<ScoreManagementDetail> details = this.list(new QueryWrapper<ScoreManagementDetail>()
                .in("score_id", scoreIds));
        if (CollectionUtils.isEmpty(details)) {
            return Collections.emptyMap();
        }
        // 按照scoreId分组
        return details.stream()
                .collect(Collectors.groupingBy(ScoreManagementDetail::getScoreId));
    }

    @Override
    public List<ScoreManagementDetail> listByScoreId(List<Integer> scoreIds) {
        if (scoreIds == null)
            return Collections.emptyList();
        return this.list(new QueryWrapper<ScoreManagementDetail>().in("score_id", scoreIds));
    }


}
