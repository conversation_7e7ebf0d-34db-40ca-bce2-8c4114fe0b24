package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.AdvertiseAccountMapper;
import com.miaowen.bh1xlhw.model.entity.AdvertiseAccount;
import com.miaowen.bh1xlhw.model.query.goods.AdvertisePageForm;
import com.miaowen.bh1xlhw.repository.AdvertiseAccountService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 针对表【po_advertise_account(广告账户表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 16:22:34
 */
@Service
public class AdvertiseAccountServiceImpl extends ServiceImpl<AdvertiseAccountMapper, AdvertiseAccount>
        implements AdvertiseAccountService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public Page<AdvertiseAccount> pageInfo(AdvertisePageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.<AdvertiseAccount>lambdaQuery()
                .eq(AdvertiseAccount::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(Objects.nonNull(pageForm.getName()), AdvertiseAccount::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getPlatformType()), AdvertiseAccount::getPlatformType, pageForm.getPlatformType())
                .eq(Objects.nonNull(pageForm.getPlatformId()), AdvertiseAccount::getPlatformId, pageForm.getPlatformId())
                .eq(Objects.nonNull(pageForm.getOperationManagerId()), AdvertiseAccount::getOperationManagerId, pageForm.getOperationManagerId())
                .eq(Objects.nonNull(pageForm.getOperationId()), AdvertiseAccount::getOperationId, pageForm.getOperationId())
                .orderByDesc(AdvertiseAccount::getCreateTime));
    }

    @Override
    public AdvertiseAccount getExistById(Integer id) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(AdvertiseAccount.class)
                .eq(AdvertiseAccount::getId, id)
                .eq(AdvertiseAccount::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<AdvertiseAccount> allList() {
        return baseMapper.selectList(Wrappers.lambdaQuery(AdvertiseAccount.class)
                .eq(AdvertiseAccount::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<AdvertiseAccount> listByOperationId(Integer operationId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(AdvertiseAccount.class)
            .eq(AdvertiseAccount::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .eq(AdvertiseAccount::getOperationId, operationId));
    }


    @Override
    public List<AdvertiseAccount> listByOperationManagerId(Integer operationManagerId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(AdvertiseAccount.class)
                .eq(AdvertiseAccount::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .eq(AdvertiseAccount::getOperationManagerId, operationManagerId));
    }
}




