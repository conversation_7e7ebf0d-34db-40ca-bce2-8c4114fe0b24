package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.SystemConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.PoCurrencyMapper;
import com.miaowen.bh1xlhw.model.entity.BasicsEntity;
import com.miaowen.bh1xlhw.model.entity.Currency;
import com.miaowen.bh1xlhw.repository.ICurrencyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.nio.channels.NonWritableChannelException;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import java.math.BigDecimal;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_currency(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class ICurrencyServiceImpl extends ServiceImpl<PoCurrencyMapper, Currency>
        implements ICurrencyService {

    @Override
    public void setRateById(Integer id, BigDecimal rate) {
        LambdaUpdateWrapper<Currency> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Currency::getId, id).set(Currency::getExchangeRate, rate);
        this.update(updateWrapper);
    }

    @Override
    public Currency getByCurrency(String currency) {
        return baseMapper.selectOne(Wrappers.<Currency>lambdaQuery()
            .in(Currency::getCurrencyUnit, currency)
            .eq(Currency::getStatus, BooleanEnum.TRUE.getValue())
            .last(SQLConstant.LIMIT_ONE));
    }

    @Override
    public Map<String, String> mapAll() {
        List<Currency> currencies = baseMapper.selectList(Wrappers.<Currency>lambdaQuery()
                .eq(Currency::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
        );
        if (CollectionUtils.isEmpty(currencies)){
            return Collections.emptyMap();
        }
        Map<String, String> result = new HashMap<>();
        currencies.forEach(t->{
            result.put(t.getCurrencyUnit(), t.getCurrencySymbol());
        });
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logicDeleteByIds(List<Integer> ids) {
        baseMapper.logicDeleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoverBatchByIds(List<Integer> ids) {
        baseMapper.recoverBatchIds(ids);
    }

    @Override
    public List<Currency> useListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<Currency>lambdaQuery()
            .in(Currency::getId, ids)
            .eq(Currency::getStatus, BooleanEnum.TRUE.getValue()));
    }
}




