package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.OrderMapper;
import com.miaowen.bh1xlhw.model.bo.oa.StatisticOrderBO;
import com.miaowen.bh1xlhw.model.entity.RefundRecord;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.entity.order.OrderSuccess;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;
import com.miaowen.bh1xlhw.repository.IOrderService;
import com.miaowen.bh1xlhw.repository.IOrderSuccessService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 针对表【po_order(交易订单表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 14:20:44
 */
@Service
@AllArgsConstructor
public class IOrderServiceImpl extends ServiceImpl<OrderMapper, Order>
    implements IOrderService {
    private final IOrderSuccessService orderSuccessService;

    @Override
    public Page<Order> pageInfo(OrderForm orderForm) {
        LambdaQueryWrapper<Order> wrapper = Wrappers.lambdaQuery(Order.class)
            .in(Order::getPaymentStatus, Arrays.asList(BaseOrder.PayStatusEnum.INIT.getValue(), BaseOrder.PayStatusEnum.CREATE.getValue(), BaseOrder.PayStatusEnum.PENDING.getValue(), BaseOrder.PayStatusEnum.FAILED.getValue()))
            .eq(StringUtils.hasText(orderForm.getOutTradeNo()), Order::getOutTradeNo, orderForm.getOutTradeNo())
            .eq(StringUtils.hasText(orderForm.getThirdOutTradeNo()), Order::getThirdOutTradeNo, orderForm.getThirdOutTradeNo())
            .eq(StringUtils.hasText(orderForm.getTgId()), Order::getTgId, orderForm.getTgId())
            .eq(StringUtils.hasText(orderForm.getPlatformType()), Order::getPlatformType, orderForm.getPlatformType())
            .eq(StringUtils.hasText(orderForm.getCountryCode()), Order::getCountryCode, orderForm.getCountryCode())
            .eq(StringUtils.hasText(orderForm.getSource()), Order::getSource, orderForm.getSource())
            .eq(Objects.nonNull(orderForm.getGoodsType()), Order::getGoodsType, orderForm.getGoodsType())
            .eq(Objects.nonNull(orderForm.getOrderStatus()), Order::getPaymentStatus, orderForm.getOrderStatus())
            .eq(StringUtils.hasText(orderForm.getPaymentCode()), Order::getPaymentTypeId, orderForm.getPaymentCode())
            .eq(Objects.nonNull(orderForm.getPaymentId()), Order::getPaymentTypeId, orderForm.getPaymentId())
            .eq(Objects.nonNull(orderForm.getRedPackGetStatus()), Order::getRedPackGetType, orderForm.getRedPackGetStatus())
            .eq(Objects.nonNull(orderForm.getRedPackUseStatus()), Order::getRedPackUseType, orderForm.getRedPackUseStatus())
            .eq(Objects.nonNull(orderForm.getEmail()), Order::getEmail, orderForm.getEmail())
            .eq(Objects.nonNull(orderForm.getThirdEmail()), Order::getThirdEmail, orderForm.getThirdEmail())
            .eq(Objects.nonNull(orderForm.getCardNumber()), Order::getCardNumber, orderForm.getCardNumber())
            .eq(Objects.nonNull(orderForm.getReturnType()), Order::getIsEmail, orderForm.getReturnType())
            .orderByDesc(Order::getCreateTime);

        if (StringUtils.hasText(orderForm.getGoodsIds())){
            List<Integer> goodsIds = Arrays.stream(orderForm.getGoodsIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(Objects.nonNull(orderForm.getGoodsType()), Order::getGoodsId, goodsIds);
        }
        if (Objects.nonNull(orderForm.getStartTime())) {
            wrapper.ge(Order::getCreateTime, DateUtils.getEast8LocalDateTime(orderForm.getTimezone(), orderForm.getStartTime()));
        }
        if (Objects.nonNull(orderForm.getEndTime())) {
            wrapper.le(Order::getCreateTime, DateUtils.getEast8LocalDateTime(orderForm.getTimezone(), orderForm.getEndTime()));
        }
        return baseMapper.selectPage(orderForm.qry(), wrapper);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dealRefundRecord(Integer sumRefundAmount, Integer status, BaseOrder order) {
        if (RefundRecord.StatusEnum.SUCCEEDED.getStatus().equals(status)) {
            Order updateOrder = new Order();
            updateOrder.setRefundAmount(sumRefundAmount);
            OrderSuccess updateOrderSuccess = new OrderSuccess();
            updateOrderSuccess.setRefundAmount(sumRefundAmount);
            if (order.getPaymentAmount().equals(sumRefundAmount)) {
                updateOrder.setPaymentStatus(BaseOrder.PayStatusEnum.REFUND.getValue());
                updateOrderSuccess.setPaymentStatus(BaseOrder.PayStatusEnum.REFUND.getValue());
            }
            baseMapper.update(updateOrder, Wrappers.<Order>lambdaUpdate()
                .eq(Order::getId, order.getId())
                .eq(Order::getPaymentStatus, BaseOrder.PayStatusEnum.SUCCESS.getValue()));

            orderSuccessService.update(updateOrderSuccess, Wrappers.<OrderSuccess>lambdaUpdate()
                .eq(OrderSuccess::getId, order.getId())
                .eq(OrderSuccess::getPaymentStatus, BaseOrder.PayStatusEnum.SUCCESS.getValue()));
        }
    }

    @Override
    public List<StatisticOrderBO> statisticOrder(LambdaQueryWrapper<Order> wrapper) {
        return baseMapper.statisticOrder(wrapper);
    }

    @Override
    public List<Order> selectByCondition(BrowseCountForm form) {
        QueryWrapper<Order> wrapper = new QueryWrapper<>();
        wrapper.eq("tg_id", form.getTgid());
        wrapper.ge("create_time", form.getStartTime());
        wrapper.le("create_time", form.getEndTime());
        if (!StringUtils.isEmpty(form.getSource())) {
            wrapper.eq("source", form.getSource());
        }
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Order getByOutTradeNo(String outTradeNo) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(Order.class)
            .eq(Order::getOutTradeNo, outTradeNo));
    }

    @Override
    public List<Order> getByOutTradeNos(List<String> outTradeNo) {
        return baseMapper.selectList(Wrappers.lambdaQuery(Order.class).in(Order::getOutTradeNo, outTradeNo));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEmailStatus(Long orderId, String email) {
        Order order = new Order();
        order.setEmailSendStatus(BooleanEnum.TRUE.getValue());
        order.setEmail(email);
        baseMapper.update(order, Wrappers.lambdaUpdate(Order.class).eq(Order::getId, orderId));
        OrderSuccess orderSuccess = new OrderSuccess();
        orderSuccess.setEmailSendStatus(BooleanEnum.TRUE.getValue());
        orderSuccess.setEmail(email);
        orderSuccessService.update(orderSuccess, Wrappers.lambdaUpdate(OrderSuccess.class).eq(OrderSuccess::getId, orderId));
    }
}




