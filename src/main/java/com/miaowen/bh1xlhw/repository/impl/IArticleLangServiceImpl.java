package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.ArticleLangMapper;
import com.miaowen.bh1xlhw.model.entity.ArticleLang;
import com.miaowen.bh1xlhw.repository.IArticleLangService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:52
 */
@Service
public class IArticleLangServiceImpl extends ServiceImpl<ArticleLangMapper, ArticleLang>
        implements IArticleLangService {

    @Override
    public List<ArticleLang> findByArticleId(Integer articleId) {
        LambdaQueryWrapper<ArticleLang> queryWrapper = Wrappers.lambdaQuery(ArticleLang.class)
                .eq(ArticleLang::getArticleId, articleId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void add(ArticleLang ArticleLang) {
        baseMapper.insert(ArticleLang);
    }


    @Override
    public void deleteByArticleId(Integer articleId) {
        LambdaUpdateWrapper<ArticleLang> updateWrapper = Wrappers.lambdaUpdate(ArticleLang.class)
                .eq(ArticleLang::getArticleId, articleId);
        baseMapper.delete(updateWrapper);
    }


}
