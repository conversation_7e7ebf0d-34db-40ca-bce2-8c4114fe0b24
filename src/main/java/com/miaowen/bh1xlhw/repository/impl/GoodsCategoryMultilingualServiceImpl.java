package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.GoodsTypeMultilingualMapper;
import com.miaowen.bh1xlhw.model.entity.GoodsCategoryMultilingual;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryPageForm;
import com.miaowen.bh1xlhw.repository.GoodsCategoryMultilingualService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 针对表【po_goods_type_multilingual(商品类型多语言表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08 17:16:10
 */
@Service
public class GoodsCategoryMultilingualServiceImpl extends ServiceImpl<GoodsTypeMultilingualMapper, GoodsCategoryMultilingual>
        implements GoodsCategoryMultilingualService {

    @Override
    public Page<GoodsCategoryMultilingual> pageInfo(GoodsCategoryPageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.<GoodsCategoryMultilingual>lambdaQuery()
                .eq(GoodsCategoryMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(Objects.nonNull(pageForm.getName()), GoodsCategoryMultilingual::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getGoodsType()), GoodsCategoryMultilingual::getType, pageForm.getGoodsType())
                .orderByDesc(GoodsCategoryMultilingual::getCreateTime));
    }

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public Map<Integer, GoodsCategoryMultilingual> mapByIds(List<Integer> goodsTypeIds) {
        if (CollectionUtils.isEmpty(goodsTypeIds)) {
            return Collections.emptyMap();
        }
        List<GoodsCategoryMultilingual> list = baseMapper.selectList(Wrappers.lambdaQuery(GoodsCategoryMultilingual.class)
                .eq(GoodsCategoryMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .in(GoodsCategoryMultilingual::getId, goodsTypeIds));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(list, GoodsCategoryMultilingual::getId);
    }

    @Override
    public List<GoodsCategoryMultilingual> listAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(GoodsCategoryMultilingual.class)
                .eq(GoodsCategoryMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(GoodsCategoryMultilingual::getCreateTime));
    }

    @Override
    public Map<Integer, GoodsCategoryMultilingual> mapNameByIds(List<Integer> goodsTypeIds) {
        if (CollectionUtils.isEmpty(goodsTypeIds)) {
            return Collections.emptyMap();
        }

        List<GoodsCategoryMultilingual> goodsCategoryMultilingualList = baseMapper.selectList(Wrappers.<GoodsCategoryMultilingual>lambdaQuery()
                .select(GoodsCategoryMultilingual::getId, GoodsCategoryMultilingual::getName)
                .in(GoodsCategoryMultilingual::getId, goodsTypeIds)
                .eq(GoodsCategoryMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(goodsCategoryMultilingualList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsCategoryMultilingualList, GoodsCategoryMultilingual::getId);
    }

    @Override
    public GoodsCategoryMultilingual getByType(String type) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(GoodsCategoryMultilingual.class)
                .eq(GoodsCategoryMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .in(GoodsCategoryMultilingual::getType, type));
    }
}




