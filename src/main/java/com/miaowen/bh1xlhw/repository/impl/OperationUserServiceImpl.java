package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.OperationUserMapper;
import com.miaowen.bh1xlhw.model.entity.OperationUser;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerQryForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserQryForm;
import com.miaowen.bh1xlhw.repository.OperationUserService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_operation_user(运营人员表)】的数据库操作Service实现
 * @createDate 2025-05-07 14:23:05
 */
@Service
public class OperationUserServiceImpl extends ServiceImpl<OperationUserMapper, OperationUser>
    implements OperationUserService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public void recoverById(Integer id) {
        baseMapper.recoverById(id);
    }

    @Override
    public Page<OperationUser> pageInfo(OperationUserQryForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(OperationUser.class)
            .gt(OperationUser::getPid, 0)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .like(Objects.nonNull(pageForm.getName()), OperationUser::getName, pageForm.getName())
            .like(Objects.nonNull(pageForm.getWorkNo()), OperationUser::getWorkNo, pageForm.getWorkNo())
            .eq(Objects.nonNull(pageForm.getPid()), OperationUser::getPid, pageForm.getPid())
            .orderByDesc(OperationUser::getCreateTime));
    }

    @Override
    public Map<Integer, OperationUser> mapNameByIds(Collection<Integer> operationIds) {
        if (CollectionUtils.isEmpty(operationIds)) {
            return Collections.emptyMap();
        }

        List<OperationUser> operationUserList = baseMapper.selectList(Wrappers.<OperationUser>lambdaQuery()
            .select(OperationUser::getId, OperationUser::getName, OperationUser::getWorkNo, OperationUser::getPid)
            .in(OperationUser::getId, operationIds)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(operationUserList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(operationUserList, OperationUser::getId);
    }

    @Override
    public List<OperationUser> allExist() {
        return baseMapper.selectList(Wrappers.lambdaQuery(OperationUser.class)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .orderByDesc(OperationUser::getCreateTime));
    }

    @Override
    public List<OperationUser> allUser() {
        return baseMapper.selectList(Wrappers.lambdaQuery(OperationUser.class)
            .gt(OperationUser::getPid, 0)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .orderByDesc(OperationUser::getCreateTime));
    }

    @Override
    public OperationUser getByUserCode(String workNo) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(OperationUser.class)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .eq(OperationUser::getWorkNo, workNo)
            .last(SQLConstant.LIMIT_ONE));
    }

    @Override
    public List<OperationUser> listByPid(Collection<Integer> managerUserIds) {
        if (CollectionUtils.isEmpty(managerUserIds)) {
            return Collections.emptyList();
        }

        return baseMapper.selectList(Wrappers.<OperationUser>lambdaQuery()
            .select(OperationUser::getId, OperationUser::getName, OperationUser::getPid)
            .in(OperationUser::getId, managerUserIds)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

    }

    @Override
    public List<OperationUser> getIdsByName(String operationManagerWorkNo, String operationUserCode) {
        return baseMapper.selectList(Wrappers.lambdaQuery(OperationUser.class)
            .select(OperationUser::getId)
            .gt(OperationUser::getPid, 0)
            .in(StringUtils.hasText(operationManagerWorkNo), OperationUser::getName, operationManagerWorkNo)
            .eq(StringUtils.hasText(operationUserCode), OperationUser::getWorkNo, operationUserCode)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

    }

    @Override
    public List<Integer> getManagerIdsByName(String operationManagerWorkNo, String operationUserCode) {
        List<OperationUser> operationUsers = baseMapper.selectList(Wrappers.lambdaQuery(OperationUser.class)
            .select(OperationUser::getId)
            .eq(OperationUser::getPid, 0)
            .in(StringUtils.hasText(operationManagerWorkNo), OperationUser::getName, operationManagerWorkNo)
            .eq(StringUtils.hasText(operationUserCode), OperationUser::getWorkNo, operationUserCode)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(operationUsers)) {
            return Collections.emptyList();
        }
        return StreamUtil.fetchList(operationUsers, OperationUser::getId);

    }

    @Override
    public Page<OperationUser> pageManagerInfo(OperationManagerQryForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(OperationUser.class)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .like(Objects.nonNull(pageForm.getName()), OperationUser::getName, pageForm.getName())
            .like(Objects.nonNull(pageForm.getWorkNo()), OperationUser::getWorkNo, pageForm.getWorkNo())
            .eq(OperationUser::getPid, 0)
            .orderByDesc(OperationUser::getCreateTime));
    }

    @Override
    public Map<Integer, OperationUser> allMap() {
        List<OperationUser> operationUsers = allExist();
        if (CollectionUtils.isEmpty(operationUsers)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(operationUsers, OperationUser::getId);
    }

    @Override
    public List<OperationUser> allManagerExist() {
        return baseMapper.selectList(Wrappers.lambdaQuery(OperationUser.class)
            .eq(OperationUser::getPid, 0)
            .eq(OperationUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .orderByDesc(OperationUser::getCreateTime));
    }
}




