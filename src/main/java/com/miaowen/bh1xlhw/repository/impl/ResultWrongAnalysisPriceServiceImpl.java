package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.LangTypeEnum;
import com.miaowen.bh1xlhw.mapper.ResultWrongAnalysisMapper;
import com.miaowen.bh1xlhw.mapper.ResultWrongAnalysisPriceMapper;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysis;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysisPrice;
import com.miaowen.bh1xlhw.repository.ResultWrongAnalysisPriceService;
import com.miaowen.bh1xlhw.repository.ResultWrongAnalysisService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 10:23
 */
@Service
public class ResultWrongAnalysisPriceServiceImpl extends ServiceImpl<ResultWrongAnalysisPriceMapper, ResultWrongAnalysisPrice>
        implements ResultWrongAnalysisPriceService {


    @Override
    public void deleteByTag(String tag, boolean isMulti) {
        LambdaQueryWrapper<ResultWrongAnalysisPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ResultWrongAnalysisPrice::getTag, tag);
        queryWrapper.eq(ResultWrongAnalysisPrice::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        this.remove(queryWrapper);
    }

    @Override
    public List<ResultWrongAnalysisPrice> getByTag(String tag, boolean isMulti) {
        LambdaQueryWrapper<ResultWrongAnalysisPrice> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ResultWrongAnalysisPrice::getTag, tag);
        queryWrapper.eq(ResultWrongAnalysisPrice::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        return this.list(queryWrapper);
    }

}
