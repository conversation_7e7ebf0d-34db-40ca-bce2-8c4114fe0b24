package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.OrderMbtiMapper;
import com.miaowen.bh1xlhw.model.entity.RefundRecord;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.model.entity.order.OrderMbti;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;
import com.miaowen.bh1xlhw.repository.IOrderMbtiService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 针对表【po_order_mbti(mbti子订单表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-10 15:01:08
 */
@Service
public class IOrderMbtiServiceImpl extends ServiceImpl<OrderMbtiMapper, OrderMbti>
    implements IOrderMbtiService {

    @Override
    public Page<OrderMbti> pageInfo(OrderForm orderForm) {
        LambdaQueryWrapper<OrderMbti> wrapper = Wrappers.lambdaQuery(OrderMbti.class)
            .eq(StringUtils.hasText(orderForm.getOutTradeNo()), OrderMbti::getOutTradeNo, orderForm.getOutTradeNo())
            .eq(StringUtils.hasText(orderForm.getThirdOutTradeNo()), OrderMbti::getThirdOutTradeNo, orderForm.getThirdOutTradeNo())
            .eq(StringUtils.hasText(orderForm.getMainOutTradeNo()), OrderMbti::getMainOutTradeNo, orderForm.getMainOutTradeNo())
            .eq(Objects.nonNull(orderForm.getGoodsType()), OrderMbti::getGoodsType, orderForm.getGoodsType())
            .eq(Objects.nonNull(orderForm.getPaymentId()), OrderMbti::getPaymentTypeId, orderForm.getPaymentId())
            .eq(Objects.nonNull(orderForm.getOrderStatus()), OrderMbti::getPaymentStatus, orderForm.getOrderStatus())
            .orderByDesc(OrderMbti::getCreateTime);
        if (StringUtils.hasText(orderForm.getGoodsIds())){
            List<Integer> goodsIds = Arrays.stream(orderForm.getGoodsIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(Objects.nonNull(orderForm.getGoodsId()), OrderMbti::getGoodsId, goodsIds);
        }
        if (Objects.nonNull(orderForm.getStartTime())) {
            wrapper.ge(OrderMbti::getCreateTime, DateUtils.getEast8LocalDateTime(orderForm.getTimezone(), orderForm.getStartTime()));
        }
        if (Objects.nonNull(orderForm.getEndTime())) {
            wrapper.le(OrderMbti::getCreateTime, DateUtils.getEast8LocalDateTime(orderForm.getTimezone(), orderForm.getEndTime()));
        }
        return baseMapper.selectPage(orderForm.qry(), wrapper);
    }

    @Transactional
    @Override
    public void dealRefundRecord(Integer sumRefundAmount, Integer status, OrderMbti order) {
        if (RefundRecord.StatusEnum.SUCCEEDED.getStatus().equals(status)) {
            OrderMbti updateOrder = new OrderMbti();
            updateOrder.setRefundAmount(sumRefundAmount);
            if (order.getPaymentAmount().equals(sumRefundAmount)) {
                updateOrder.setPaymentStatus(BaseOrder.PayStatusEnum.REFUND.getValue());
            }
            baseMapper.update(updateOrder, Wrappers.<OrderMbti>lambdaUpdate()
                .eq(OrderMbti::getId, order.getId())
                .eq(OrderMbti::getPaymentStatus, BaseOrder.PayStatusEnum.SUCCESS.getValue()));

        }
    }

    @Override
    public OrderMbti getByMainOutTradeNo(String mainOutTradeNo, Integer contentType) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(OrderMbti.class)
            .eq(OrderMbti::getMainOutTradeNo, mainOutTradeNo)
            .eq(OrderMbti::getType, contentType)
            .last(SQLConstant.LIMIT_ONE));
    }

    @Override
    public List<OrderMbti> listByMainOutTradeNoType(String mainOutTradeNo, List<Integer> contentTypeList) {
        return baseMapper.selectList(Wrappers.lambdaQuery(OrderMbti.class)
            .eq(OrderMbti::getMainOutTradeNo, mainOutTradeNo)
            .in(OrderMbti::getType, contentTypeList)
            .in(OrderMbti::getPaymentStatus, Arrays.asList(BaseOrder.PayStatusEnum.ADMIN_SUCCESS.getValue(),
                BaseOrder.PayStatusEnum.SUCCESS.getValue(), BaseOrder.PayStatusEnum.REFUND.getValue())));
    }

    @Override
    public List<OrderMbti> listByMainOutTradeNo(String mainOutTradeNo) {
        return baseMapper.selectList(Wrappers.lambdaQuery(OrderMbti.class)
            .eq(OrderMbti::getMainOutTradeNo, mainOutTradeNo));
    }
}




