package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.PaymentChannelMapper;
import com.miaowen.bh1xlhw.model.entity.PaymentChannel;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.repository.IPaymentChannelService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 针对表【po_payment_pattern(支付方式模式配置)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 09:50:56
 */
@Service
public class IPaymentChannelServiceImpl extends ServiceImpl<PaymentChannelMapper, PaymentChannel>
        implements IPaymentChannelService {


    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public Page<PaymentChannel> pageInfo(PageForm pageForm, String name) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.<PaymentChannel>lambdaQuery()
                .eq(PaymentChannel::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(StringUtils.isNotBlank(name), PaymentChannel::getName, name)
                .orderByAsc(Arrays.asList(PaymentChannel::getSort, PaymentChannel::getId)));
    }

    @Override
    public void updateNoDefault(Integer id) {
        baseMapper.updateNoDefault(id);
    }

    @Override
    public List<PaymentChannel> allList() {
        return baseMapper.selectList(Wrappers.<PaymentChannel>lambdaQuery()
                .eq(PaymentChannel::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByAsc(Arrays.asList(PaymentChannel::getSort, PaymentChannel::getId)));
    }
}




