package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.GoodsTypeTraditionalMapper;
import com.miaowen.bh1xlhw.model.entity.GoodsCategoryTraditional;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryPageForm;
import com.miaowen.bh1xlhw.repository.GoodsCategoryTraditionalService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 针对表【po_goods_type_traditional(商品类型多语言表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 15:13:26
 */
@Service
public class GoodsCategoryTraditionalServiceImpl extends ServiceImpl<GoodsTypeTraditionalMapper, GoodsCategoryTraditional>
        implements GoodsCategoryTraditionalService {


    @Override
    public Page<GoodsCategoryTraditional> pageInfo(GoodsCategoryPageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.<GoodsCategoryTraditional>lambdaQuery()
                .eq(GoodsCategoryTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(Objects.nonNull(pageForm.getName()), GoodsCategoryTraditional::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getGoodsType()), GoodsCategoryTraditional::getType, pageForm.getGoodsType())
                .orderByDesc(GoodsCategoryTraditional::getCreateTime));
    }

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public Map<Integer, GoodsCategoryTraditional> mapByIds(List<Integer> goodsTypeIds) {
        if (CollectionUtils.isEmpty(goodsTypeIds)) {
            return Collections.emptyMap();
        }
        List<GoodsCategoryTraditional> list = baseMapper.selectList(Wrappers.lambdaQuery(GoodsCategoryTraditional.class)
                .eq(GoodsCategoryTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .in(GoodsCategoryTraditional::getId, goodsTypeIds));
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(list, GoodsCategoryTraditional::getId);
    }

    @Override
    public List<GoodsCategoryTraditional> listAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(GoodsCategoryTraditional.class)
                .eq(GoodsCategoryTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(GoodsCategoryTraditional::getCreateTime));
    }

    @Override
    public Map<Integer, GoodsCategoryTraditional> mapNameByIds(List<Integer> goodsTypeIds) {
        if (CollectionUtils.isEmpty(goodsTypeIds)) {
            return Collections.emptyMap();
        }

        List<GoodsCategoryTraditional> goodsCategoryTraditionalList = baseMapper.selectList(Wrappers.<GoodsCategoryTraditional>lambdaQuery()
                .select(GoodsCategoryTraditional::getId, GoodsCategoryTraditional::getName)
                .in(GoodsCategoryTraditional::getId, goodsTypeIds)
                .eq(GoodsCategoryTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(goodsCategoryTraditionalList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsCategoryTraditionalList, GoodsCategoryTraditional::getId);
    }

    @Override
    public GoodsCategoryTraditional getByType(String type) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(GoodsCategoryTraditional.class)
                .eq(GoodsCategoryTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .in(GoodsCategoryTraditional::getType, type));
    }
}




