package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.EmailOrderSuccessMapper;
import com.miaowen.bh1xlhw.mapper.EmailUnsubscribeMapper;
import com.miaowen.bh1xlhw.model.entity.EmailOrderSuccess;
import com.miaowen.bh1xlhw.model.entity.EmailUnsubscribe;
import com.miaowen.bh1xlhw.model.query.email.UnsubscribeEmailForm;
import com.miaowen.bh1xlhw.repository.IEmailOrderSuccessService;
import com.miaowen.bh1xlhw.repository.IEmailUnsubscribeService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_email_order_success的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmaiUnsubscribeServiceImpl extends ServiceImpl<EmailUnsubscribeMapper, EmailUnsubscribe>
        implements IEmailUnsubscribeService {

    @Override
    public Page<EmailUnsubscribe> pageInfo(UnsubscribeEmailForm form) {
        return baseMapper.selectPage(form.qry(), Wrappers.lambdaQuery(EmailUnsubscribe.class)
            .likeRight(StringUtils.hasText(form.getEmail()), EmailUnsubscribe::getEmail, form.getEmail())
            .orderByDesc(EmailUnsubscribe::getCreateTime));
    }

    @Override
    public void recoverUnsubscribe(Integer id) {
        EmailUnsubscribe emailUnsubscribe = new EmailUnsubscribe();
        emailUnsubscribe.setId(id);
        emailUnsubscribe.setStatus(BooleanEnum.FALSE.getValue());
        emailUnsubscribe.setTime(LocalDateTime.now());
        baseMapper.updateById(emailUnsubscribe);
    }
}



