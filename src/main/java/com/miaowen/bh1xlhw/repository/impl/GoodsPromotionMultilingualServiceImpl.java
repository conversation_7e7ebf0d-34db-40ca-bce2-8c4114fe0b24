package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.GoodsPromotionMultilingualMapper;
import com.miaowen.bh1xlhw.model.entity.GoodsPromotionMultilingual;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionPageForm;
import com.miaowen.bh1xlhw.repository.GoodsPromotionMultilingualService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 针对表【po_goods_promotion_multilingual(推广商品多语言)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 09:46:38
 */
@Service
public class GoodsPromotionMultilingualServiceImpl extends ServiceImpl<GoodsPromotionMultilingualMapper, GoodsPromotionMultilingual>
        implements GoodsPromotionMultilingualService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public GoodsPromotionMultilingual getExistById(Integer id) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(GoodsPromotionMultilingual.class)
                .eq(GoodsPromotionMultilingual::getId, id)
                .eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<GoodsPromotionMultilingual> listAll() {
        LambdaQueryWrapper<GoodsPromotionMultilingual> wrapper = Wrappers.lambdaQuery(GoodsPromotionMultilingual.class)
                .eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(GoodsPromotionMultilingual::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Page<GoodsPromotionMultilingual> pageInfo(GoodsPromotionPageForm pageForm) {
        LambdaQueryWrapper<GoodsPromotionMultilingual> wrapper = Wrappers.<GoodsPromotionMultilingual>lambdaQuery()
            .in(!CollectionUtils.isEmpty(pageForm.getGoodsIds()), GoodsPromotionMultilingual::getGoodsId, pageForm.getGoodsIds())
            .like(StringUtils.hasText(pageForm.getTgId()), GoodsPromotionMultilingual::getTgId, pageForm.getTgId())
            .in(StringUtils.hasText(pageForm.getApplication()), GoodsPromotionMultilingual::getApplication, pageForm.getApplication())
            .eq(Objects.nonNull(pageForm.getPlatformType()), GoodsPromotionMultilingual::getPlatformType, pageForm.getPlatformType())
            .eq(Objects.nonNull(pageForm.getPlatformId()), GoodsPromotionMultilingual::getPlatformId, pageForm.getPlatformId())
            .eq(Objects.nonNull(pageForm.getOperationManagerId()), GoodsPromotionMultilingual::getOperationManagerId, pageForm.getOperationManagerId())
            .eq(Objects.nonNull(pageForm.getOperationId()), GoodsPromotionMultilingual::getOperationId, pageForm.getOperationId())
            .eq(Objects.nonNull(pageForm.getPromotionStatus()), GoodsPromotionMultilingual::getPromotionStatus, pageForm.getPromotionStatus())
            .orderByDesc(GoodsPromotionMultilingual::getCreateTime);

        if (BooleanEnum.TRUE.getValue().equals(pageForm.getIsRecycle())){
            wrapper.gt(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }else {
            wrapper.eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }
        return baseMapper.selectPage(pageForm.qry(), wrapper);
    }

    @Override
    public List<GoodsPromotionMultilingual> listByPriceAndOperationId(Integer priceId, Integer operationId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(GoodsPromotionMultilingual.class)
                .eq(GoodsPromotionMultilingual::getPriceId, priceId)
                .eq(GoodsPromotionMultilingual::getOperationId, operationId)
                .eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<GoodsPromotionMultilingual> listByAdvertiseAccountId(Integer advertiseAccountId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(GoodsPromotionMultilingual.class)
                .eq(GoodsPromotionMultilingual::getAdvertiseAccountId, advertiseAccountId)
                .eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public Map<Integer, GoodsPromotionMultilingual> mapById(List<Integer> goodsMultilingualPromotionIds) {
        if (CollectionUtils.isEmpty(goodsMultilingualPromotionIds)) {
            return Collections.emptyMap();
        }
        List<GoodsPromotionMultilingual> goodsPromotionMultilingualList =
                baseMapper.selectList(Wrappers.lambdaQuery(GoodsPromotionMultilingual.class)
                        .in(GoodsPromotionMultilingual::getId, goodsMultilingualPromotionIds)
//                        .eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                );
        if (CollectionUtils.isEmpty(goodsPromotionMultilingualList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsPromotionMultilingualList, GoodsPromotionMultilingual::getId);
    }

    @Override
    public GoodsPromotionMultilingual getByTgId(String tgId) {
        return baseMapper.selectOne(Wrappers.<GoodsPromotionMultilingual>lambdaQuery()
            .eq(GoodsPromotionMultilingual::getTgId, tgId)
            .eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<GoodsPromotionMultilingual> listTgId(List<String> traditionalTiIds) {
        if (CollectionUtils.isEmpty(traditionalTiIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<GoodsPromotionMultilingual>lambdaQuery()
            .in(GoodsPromotionMultilingual::getTgId, traditionalTiIds)
            .eq(GoodsPromotionMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoverBatchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        baseMapper.recoverBatchIds(ids);
    }

}




