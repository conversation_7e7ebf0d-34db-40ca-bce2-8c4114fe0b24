package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.GoodsMultilingualMapper;
import com.miaowen.bh1xlhw.model.entity.GoodsMultilingual;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPageForm;
import com.miaowen.bh1xlhw.repository.IGoodsMultilingualService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 针对表【po_goods_ordinary(普通商品表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-07 09:50:47
 */
@Service
public class GoodsMultilingualServiceImpl extends ServiceImpl<GoodsMultilingualMapper, GoodsMultilingual>
        implements IGoodsMultilingualService {

    @Override
    public Page<GoodsMultilingual> pageInfo(GoodsPageForm pageForm) {
        LambdaQueryWrapper<GoodsMultilingual> wrapper = Wrappers.lambdaQuery(GoodsMultilingual.class)
                .like(StringUtils.hasText(pageForm.getName()), GoodsMultilingual::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getGoodsCategoryId()),GoodsMultilingual::getGoodsCategoryId, pageForm.getGoodsCategoryId())
                .orderByDesc(GoodsMultilingual::getCreateTime);

        if (BooleanEnum.TRUE.getValue().equals(pageForm.getIsRecycle())){
            wrapper.gt(GoodsMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }else {
            wrapper.eq(GoodsMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }
        return baseMapper.selectPage(pageForm.qry(), wrapper);
    }

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public Map<Integer, GoodsMultilingual> mapNameByIds(List<Integer> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyMap();
        }

        List<GoodsMultilingual> goodsMultilingualList = baseMapper.selectList(Wrappers.<GoodsMultilingual>lambdaQuery()
                .select(GoodsMultilingual::getId, GoodsMultilingual::getName, GoodsMultilingual::getGoodsCategoryId,
                        GoodsMultilingual::getWebPackageName)
                .in(GoodsMultilingual::getId, goodsIds)
                .eq(GoodsMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(goodsMultilingualList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsMultilingualList, GoodsMultilingual::getId);
    }

    @Override
    public List<GoodsMultilingual> listAll() {
        LambdaQueryWrapper<GoodsMultilingual> wrapper = Wrappers.lambdaQuery(GoodsMultilingual.class)
                .eq(GoodsMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(GoodsMultilingual::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<Integer> listIdsByName(String goodsName) {
        LambdaQueryWrapper<GoodsMultilingual> wrapper = Wrappers.lambdaQuery(GoodsMultilingual.class)
                .eq(GoodsMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(GoodsMultilingual::getName, goodsName);
        return baseMapper.selectList(wrapper).stream().map(GoodsMultilingual::getId).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, GoodsMultilingual> mapById(List<Integer> goodsMultilingualIds) {
        if (CollectionUtils.isEmpty(goodsMultilingualIds)) {
            return Collections.emptyMap();
        }
        List<GoodsMultilingual> goodsMultilingualList =
            baseMapper.selectList(Wrappers.lambdaQuery(GoodsMultilingual.class)
                .in(GoodsMultilingual::getId, goodsMultilingualIds));
//                .eq(GoodsMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(goodsMultilingualList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsMultilingualList, GoodsMultilingual::getId);


    }

    @Override
    public List<Integer> listIdByName(String goodsName, Integer productId) {
        List<GoodsMultilingual> goodsMultilingualList =
            baseMapper.selectList(Wrappers.lambdaQuery(GoodsMultilingual.class)
                .select(GoodsMultilingual::getId)
                .like(StringUtils.hasText(goodsName), GoodsMultilingual::getName, goodsName)
                .eq(Objects.nonNull(productId), GoodsMultilingual::getId, productId)
                .eq(GoodsMultilingual::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(goodsMultilingualList)) {
            return Collections.emptyList();
        }
        return goodsMultilingualList.stream().map(GoodsMultilingual::getId).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoverBatchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        baseMapper.recoverBatchIds(ids);
    }

}




