package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.mapper.ResultFurtherReadingMapper;
import com.miaowen.bh1xlhw.model.entity.ResultFurtherReading;
import com.miaowen.bh1xlhw.repository.ResultFurtherReadingService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/19 14:34
 */
@Service
public class ResultFurtherReadingServiceImpl extends ServiceImpl<ResultFurtherReadingMapper, ResultFurtherReading>
        implements ResultFurtherReadingService {

    @Override
    public List<ResultFurtherReading> getByTag(String tag, boolean isMulti) {
        LambdaQueryWrapper<ResultFurtherReading> wrapper = Wrappers.lambdaQuery(ResultFurtherReading.class)
                .eq(ResultFurtherReading::getTag, tag)
                .eq(ResultFurtherReading::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        return baseMapper.selectList(wrapper);
    }

    @Override
    public void deleteByTag(String tag, boolean isMulti) {
        LambdaUpdateWrapper<ResultFurtherReading> wrapper = Wrappers.lambdaUpdate(ResultFurtherReading.class)
                .eq(ResultFurtherReading::getTag, tag)
                .eq(ResultFurtherReading::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        baseMapper.delete(wrapper);
    }

    @Override
    public void saveEntity(ResultFurtherReading resultFurtherReading) {
        baseMapper.insert(resultFurtherReading);
    }


}
