package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.AccUserMapper;
import com.miaowen.bh1xlhw.model.entity.AccRole;
import com.miaowen.bh1xlhw.model.entity.AccUser;
import com.miaowen.bh1xlhw.repository.AccUserService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_user(后台用户表)】的数据库操作Service实现
 * @createDate 2025-05-06 15:41:28
 */
@Service
public class AccUserServiceImpl extends ServiceImpl<AccUserMapper, AccUser>
    implements AccUserService {

    @Override
    public AccUser getByUsernameOrEmail(String username) {
        LambdaQueryWrapper<AccUser> last = Wrappers.lambdaQuery(AccUser.class)
            .eq(AccUser::getDeleteTime, 0)
            .and(w ->
                w.eq(AccUser::getUsername, username)
                    .or().eq(AccUser::getEmail, username))
            .orderByDesc(AccUser::getCreateTime)
            .last("limit 1");
        return baseMapper.selectOne(last);
    }

    @Override
    public List<AccRole> getRolesByUserId(Integer userId) {
        return baseMapper.getRolesByUserId(userId);

    }

    @Override
    public AccUser getExistById(Integer userId) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(AccUser.class)
            .eq(AccUser::getId, userId)
            .eq(AccUser::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }
}




