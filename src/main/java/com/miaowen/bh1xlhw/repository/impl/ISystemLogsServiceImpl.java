package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.model.entity.SystemLogs;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.log.LogPageForm;
import com.miaowen.bh1xlhw.repository.ISystemLogsService;
import com.miaowen.bh1xlhw.mapper.SystemLogsMapper;
import com.miaowen.bh1xlhw.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 针对表【po_system_logs(操作日志表)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-21 20:32:55
 */
@Service
public class ISystemLogsServiceImpl extends ServiceImpl<SystemLogsMapper, SystemLogs>
    implements ISystemLogsService {

    @Override
    public void logicBatchDelete(List<Integer> ids) {
        baseMapper.logicDeleteBatchIds(ids);
    }

    @Override
    public Page<SystemLogs> pages(LogPageForm form) {
        LambdaQueryWrapper<SystemLogs> wrapper = Wrappers.<SystemLogs>lambdaQuery()
                .eq(SystemLogs::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(SystemLogs::getCreateTime);
        if (Objects.nonNull(form.getStartTime())) {
            wrapper.ge(SystemLogs::getCreateTime, DateUtils.getData(form.getStartTime()));
        }
        if (Objects.nonNull(form.getEndTime())) {
            wrapper.le(SystemLogs::getCreateTime, DateUtils.getData(form.getEndTime()));
        }
        return baseMapper.selectPage(form.qry(), wrapper);
    }
}




