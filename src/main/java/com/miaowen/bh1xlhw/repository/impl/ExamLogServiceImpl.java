package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.mapper.ExamLogMapper;
import com.miaowen.bh1xlhw.model.entity.ExamLog;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.repository.ExamLogService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * @Description 做题日志
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 10:33
 */
@Service
public class ExamLogServiceImpl extends ServiceImpl<ExamLogMapper, ExamLog>
        implements ExamLogService {

    @Override
    public List<ExamLog> getByCondition(BrowseCountForm form) {
        try {
            QueryWrapper<ExamLog> wrapper = new QueryWrapper<>();
            wrapper.eq("tuid", form.getTgid());
            MonthShardingTableNameHandler.setParams(form.getStartTime());
            wrapper.ge("create_time", form.getStartTime());
            wrapper.le("create_time", form.getEndTime());
            if (!StringUtils.isEmpty(form.getSource())) {
                wrapper.eq("source", form.getSource());
            }
            return this.list(wrapper);
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }
}
