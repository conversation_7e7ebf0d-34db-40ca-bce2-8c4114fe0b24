package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.EmailTemplateMapper;
import com.miaowen.bh1xlhw.model.entity.EmailTemplate;
import com.miaowen.bh1xlhw.repository.IEmailTemplateService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_merchant(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmailTemplateServiceImpl extends ServiceImpl<EmailTemplateMapper, EmailTemplate>
        implements IEmailTemplateService {
    @Override
    public EmailTemplate getTemplateByType(String templateType, String emailType,Integer goodsType) {
        return baseMapper.selectOne(Wrappers.<EmailTemplate>lambdaQuery()
                .eq(EmailTemplate::getTemplateType, templateType)
                .eq(EmailTemplate::getEmailType, emailType)
                .eq(EmailTemplate::getGoodsType, goodsType)
                .eq(EmailTemplate::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .last(SQLConstant.LIMIT_ONE));
    }

    @Override
    public EmailTemplate getTemplateByType(String templateType, String emailType, Integer goodsCategoryId, Integer goodsType) {
        return baseMapper.selectOne(Wrappers.<EmailTemplate>lambdaQuery()
            .eq(EmailTemplate::getTemplateType, templateType)
            .eq(EmailTemplate::getEmailType, emailType)
            .eq(EmailTemplate::getGoodsTagId, goodsCategoryId)
            .eq(EmailTemplate::getGoodsType, goodsType)
            .eq(EmailTemplate::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .last(SQLConstant.LIMIT_ONE));
    }
}



