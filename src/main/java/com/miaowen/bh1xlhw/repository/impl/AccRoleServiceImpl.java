package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.AccRoleMapper;
import com.miaowen.bh1xlhw.model.entity.AccRole;
import com.miaowen.bh1xlhw.model.entity.AccUserRole;
import com.miaowen.bh1xlhw.repository.AccRoleService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_role(角色表)】的数据库操作Service实现
 * @createDate 2025-05-06 15:41:28
 */
@Service
public class AccRoleServiceImpl extends ServiceImpl<AccRoleMapper, AccRole>
    implements AccRoleService{


    @Override
    public void logicRemoveById(Integer roleId) {
        baseMapper.logicDeleteById(roleId);
    }

    @Override
    public List<AccRole> listExistByIds(Set<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(AccRole.class)
            .in(AccRole::getId, roleIds)
            .eq(AccRole::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<AccRole> getRolesByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AccRole> wrapper = Wrappers.lambdaQuery();
        wrapper.in(AccRole::getId, ids).eq(AccRole::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        return baseMapper.selectList(wrapper);
    }


}




