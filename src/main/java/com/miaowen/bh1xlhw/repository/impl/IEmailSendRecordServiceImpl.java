package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.config.annotation.Sharding;
import com.miaowen.bh1xlhw.config.annotation.ShardingParam;
import com.miaowen.bh1xlhw.mapper.EmailSendRecordMapper;
import com.miaowen.bh1xlhw.model.entity.EmailSendRecord;
import com.miaowen.bh1xlhw.model.query.email_template.EmailSendRecordForm;
import com.miaowen.bh1xlhw.repository.IEmailSendRecordService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_merchant(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmailSendRecordServiceImpl extends ServiceImpl<EmailSendRecordMapper, EmailSendRecord>
        implements IEmailSendRecordService {

    @Sharding
    @Override
    public boolean save(@ShardingParam String suffix, EmailSendRecord entity) {
        return super.save(entity);
    }

    @Sharding
    @Override
    public Page<EmailSendRecord> pageInfo(@ShardingParam String suffix, EmailSendRecordForm pageForm, List<Integer> templateIds) {
        LambdaQueryWrapper<EmailSendRecord> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isEmpty(pageForm.getTemplateType()) || !StringUtils.isEmpty(pageForm.getGoodsTypeCode())) {
            wrapper.in(EmailSendRecord::getEmailTemplateId, templateIds);
        }
        if (!StringUtils.isEmpty(pageForm.getTitle())) {
            wrapper.like(EmailSendRecord::getTitle, pageForm.getTitle());
        }
        if (!StringUtils.isEmpty(pageForm.getEmail())) {
            wrapper.like(EmailSendRecord::getEmail, pageForm.getEmail());
        }
        if (Objects.nonNull(pageForm.getSendStatus())){
            wrapper.eq(EmailSendRecord::getSendStatus, pageForm.getSendStatus());
        }

        if (Objects.nonNull(pageForm.getOutTradeNo())){
            wrapper.eq(EmailSendRecord::getOutTradeNo, pageForm.getOutTradeNo());
        }
        if (pageForm.getStartDate() != null && pageForm.getEndDate() != null) {
            if (!DateUtils.isSameMonth(DateUtils.getData(pageForm.getStartDate()), DateUtils.getData(pageForm.getEndDate()))) {
                //跨月 则取开始时间当月
                wrapper.between(EmailSendRecord::getCreateTime, pageForm.getStartDate(), DateUtils.getLastDayOfMonth(DateUtils.getData(pageForm.getStartDate())));
            } else {
                wrapper.between(EmailSendRecord::getCreateTime, pageForm.getStartDate(), pageForm.getEndDate());
            }
        } else {
            //没有的话则需要放入时间范围 默认查当月
            wrapper.between(EmailSendRecord::getCreateTime, DateUtils.getFirstDayOfMonth(), new Date());
        }
        wrapper.eq(EmailSendRecord::getDeleteTime, 0);
        wrapper.orderByDesc(EmailSendRecord::getCreateTime);
        return baseMapper.selectPage(pageForm.qry(), wrapper);
    }
}



