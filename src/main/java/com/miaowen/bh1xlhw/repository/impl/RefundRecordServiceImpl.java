package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.model.entity.RefundRecord;
import com.miaowen.bh1xlhw.repository.IRefundRecordService;
import com.miaowen.bh1xlhw.mapper.RefundRecordMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * 针对表【po_refund_record(后台退款记录表)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-23 14:58:43
 */
@Service
public class RefundRecordServiceImpl extends ServiceImpl<RefundRecordMapper, RefundRecord>
    implements IRefundRecordService {

    @Override
    public Integer sumRefundRecord(Long orderId) {
        return baseMapper.sumRefundRecord(orderId);
    }

    @Override
    public Integer sumByDate(LocalDate lastMonthDay) {
        return baseMapper.sumByDate(LocalDateTime.of(lastMonthDay, LocalTime.MIN), LocalDateTime.of(lastMonthDay, LocalTime.MAX));
    }
}




