package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.LanguageContextConfigMapper;
import com.miaowen.bh1xlhw.model.entity.LanguageContextConfig;
import com.miaowen.bh1xlhw.model.query.goods.GoodsLanguageForm;
import com.miaowen.bh1xlhw.repository.LanguageContextConfigService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 针对表【po_language_context_config(多语言配置表)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09 11:37:55
 */
@Service
public class LanguageContextConfigServiceImpl extends ServiceImpl<LanguageContextConfigMapper, LanguageContextConfig>
    implements LanguageContextConfigService{

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveBatchGoodsName(List<GoodsLanguageForm> goodsLanguageList, Integer relationId,
                                   LanguageContextConfig.RelationType relationType) {
        if (CollectionUtils.isEmpty(goodsLanguageList)){
            return;
        }
        List<LanguageContextConfig> languageContextConfigs = goodsLanguageList.stream().map(goodsLanguageForm -> {
            LanguageContextConfig languageContextConfig = new LanguageContextConfig();
            languageContextConfig.setContext(goodsLanguageForm.getName());
            languageContextConfig.setLanguageCode(goodsLanguageForm.getLanguageCode());
            languageContextConfig.setRelationId(relationId);
            languageContextConfig.setRelationType(relationType.getValue());
            return languageContextConfig;
        }).collect(Collectors.toList());

        saveBatch(languageContextConfigs);
    }

    @Override
    public void logicRemoveByRelationId(Integer relationId, LanguageContextConfig.RelationType relationType) {
        baseMapper.logicDelete(Wrappers.lambdaUpdate(LanguageContextConfig.class)
            .eq(LanguageContextConfig::getRelationId, relationId)
            .eq(LanguageContextConfig::getRelationType, relationType.getValue()));
    }

    @Override
    public List<LanguageContextConfig> listByRelationIds(LanguageContextConfig.RelationType relationType, List<Integer> relationIds) {
        if (CollectionUtils.isEmpty(relationIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaUpdate(LanguageContextConfig.class)
            .in(LanguageContextConfig::getRelationId, relationIds)
            .eq(LanguageContextConfig::getRelationType, relationType.getValue())
            .eq(LanguageContextConfig::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public Map<Integer, List<LanguageContextConfig>> mapByRelationIds(LanguageContextConfig.RelationType relationType, List<Integer> relationIds) {
        List<LanguageContextConfig> languageContextConfigs = listByRelationIds(relationType, relationIds);
        if (CollectionUtils.isEmpty(languageContextConfigs)){
            return Collections.emptyMap();
        }
        return StreamUtil.groupingToList(languageContextConfigs, LanguageContextConfig::getRelationId, Function.identity());
    }


    @Override
    public Map<String, LanguageContextConfig> mapByRelationIds(Integer relationType, Integer relationId) {

        List<LanguageContextConfig> languageContextConfigs = baseMapper.selectList(Wrappers.lambdaUpdate(LanguageContextConfig.class)
                .eq(LanguageContextConfig::getRelationId, relationId)
                .eq(LanguageContextConfig::getRelationType, relationType)
                .eq(LanguageContextConfig::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(languageContextConfigs)){
            return Collections.emptyMap();
        }
        return StreamUtil.map(languageContextConfigs, LanguageContextConfig::getLanguageCode, Function.identity());
    }

}




