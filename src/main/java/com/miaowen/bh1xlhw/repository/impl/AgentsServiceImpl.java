package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.AgentsMapper;
import com.miaowen.bh1xlhw.model.entity.Agents;
import com.miaowen.bh1xlhw.model.query.goods.AgentsPageForm;
import com.miaowen.bh1xlhw.repository.AgentsService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_agents(代理商)】的数据库操作Service实现
 * @createDate 2025-05-07 11:28:54
 */
@Service
public class AgentsServiceImpl extends ServiceImpl<AgentsMapper, Agents>
        implements AgentsService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public void recoverById(Integer id) {
        baseMapper.recoverById(id);
    }

    @Override
    public Page<Agents> pageInfo(AgentsPageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(Agents.class)
                .eq(Agents::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(Objects.nonNull(pageForm.getName()), Agents::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getPlatformId()), Agents::getPlatformId, pageForm.getPlatformId())
                .eq(Objects.nonNull(pageForm.getOperationType()), Agents::getOperationType, pageForm.getOperationType())
                .eq(Objects.nonNull(pageForm.getSettleType()), Agents::getSettleType, pageForm.getSettleType())
                .orderByDesc(Agents::getCreateTime));
    }

    @Override
    public Map<Integer, Agents> mapNameByIds(List<Integer> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyMap();
        }

        List<Agents> agentsList = baseMapper.selectList(Wrappers.<Agents>lambdaQuery()
            .select(Agents::getId, Agents::getName, Agents::getOperationType)
            .in(Agents::getId, agentIds)
            .eq(Agents::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(agentsList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(agentsList, Agents::getId);
    }

    @Override
    public List<Agents> listAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(Agents.class)
                .eq(Agents::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(Agents::getCreateTime));
    }

    @Override
    public Agents getExistById(Integer agentId) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(Agents.class)
            .eq(Agents::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .eq(Agents::getId, agentId));
    }

    @Override
    public List<Agents> listExistByIds(List<Integer> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(Agents.class)
            .eq(Agents::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .in(Agents::getId, agentIds));
    }
}




