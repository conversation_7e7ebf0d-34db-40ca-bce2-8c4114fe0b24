package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.CountryMapper;
import com.miaowen.bh1xlhw.model.entity.Country;
import com.miaowen.bh1xlhw.repository.ICountryService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_country(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class ICountryServiceImpl extends ServiceImpl<CountryMapper, Country>
    implements ICountryService {

    @Override
    public List<Country> useListByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<Country>lambdaQuery()
            .in(Country::getId, ids)
            .eq(Country::getStatus, BooleanEnum.TRUE.getValue())
            .eq(Country::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<Country> existAll() {
        return baseMapper.selectList(Wrappers.<Country>lambdaQuery()
            .eq(Country::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }
}



