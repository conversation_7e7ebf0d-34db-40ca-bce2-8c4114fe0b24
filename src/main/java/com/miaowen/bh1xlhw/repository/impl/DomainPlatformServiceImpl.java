package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.model.bo.DomainPlatformCountBO;
import com.miaowen.bh1xlhw.model.entity.DomainPlatform;
import com.miaowen.bh1xlhw.mapper.DomainPlatformMapper;
import com.miaowen.bh1xlhw.repository.DomainPlatformService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_domin_platform(记录域名平台使用情况)】的数据库操作Service实现
 * @createDate 2025-05-08 15:42:34
 */
@Service
public class DomainPlatformServiceImpl extends ServiceImpl<DomainPlatformMapper, DomainPlatform>
    implements DomainPlatformService {

    @Override
    public List<DomainPlatformCountBO> listDomainPlatformCount() {
       return baseMapper.selectDomainPlatformCount();
    }
}




