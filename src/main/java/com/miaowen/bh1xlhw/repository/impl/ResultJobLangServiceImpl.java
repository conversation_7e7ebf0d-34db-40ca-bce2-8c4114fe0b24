package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.ResultJobLangMapper;
import com.miaowen.bh1xlhw.model.entity.ResultJobLang;
import com.miaowen.bh1xlhw.repository.ResultJobLangService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
@Service
public class ResultJobLangServiceImpl extends ServiceImpl<ResultJobLangMapper, ResultJobLang>
        implements ResultJobLangService {
    @Override
    public List<ResultJobLang> findByJobId(Integer JobId) {
        LambdaQueryWrapper<ResultJobLang> queryWrapper = Wrappers.lambdaQuery(ResultJobLang.class)
                .eq(ResultJobLang::getResultJobId, JobId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void add(ResultJobLang resultJobLang) {
        baseMapper.insert(resultJobLang);
    }

    @Override
    public void deleteByJobId(Integer JobId) {
        LambdaUpdateWrapper<ResultJobLang> updateWrapper = Wrappers.lambdaUpdate(ResultJobLang.class)
                .eq(ResultJobLang::getResultJobId, JobId);
        baseMapper.delete(updateWrapper);
    }

    @Override
    public void deleteByJobIds(List<Integer> ids) {
        LambdaUpdateWrapper<ResultJobLang> updateWrapper = Wrappers.lambdaUpdate(ResultJobLang.class)
                .in(ResultJobLang::getResultJobId, ids);
        baseMapper.delete(updateWrapper);
    }

    @Override
    public void update(ResultJobLang resultJobLang) {
        deleteByJobId(resultJobLang.getResultJobId());
        add(resultJobLang);
    }
}
