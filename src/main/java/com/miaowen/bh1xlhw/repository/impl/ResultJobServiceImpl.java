package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.mapper.ResultJobMapper;
import com.miaowen.bh1xlhw.model.entity.ResultJob;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.repository.ResultJobService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
@Service
public class ResultJobServiceImpl extends ServiceImpl<ResultJobMapper, ResultJob>
        implements ResultJobService {

    @Override
    public Page<ResultJob> getResultJobByTag(PageForm page, String tag, boolean isMulti) {
        LambdaQueryWrapper<ResultJob> queryWrapper = Wrappers.lambdaQuery(ResultJob.class)
                .eq(ResultJob::getTag, tag)
                .eq(ResultJob::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        return baseMapper.selectPage(page.qry(),queryWrapper);
    }

    @Override
    public ResultJob getResultJobById(Integer id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Integer saveResultJob(ResultJob resultJob) {
        baseMapper.insert(resultJob);
        return resultJob.getId();
    }

    @Override
    public void updateResultJob(ResultJob resultJob) {
        baseMapper.updateById(resultJob);
    }

    @Override
    public void deleteResultJob(List<Integer> ids) {
        baseMapper.deleteBatchIds(ids);
    }
}
