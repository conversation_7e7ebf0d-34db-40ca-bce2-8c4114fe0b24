package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.AccUserRoleMapper;
import com.miaowen.bh1xlhw.model.entity.AccUserRole;
import com.miaowen.bh1xlhw.repository.AccUserRoleService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_user_role(用户角色关联表)】的数据库操作Service实现
 * @createDate 2025-05-06 15:41:28
 */
@Service
public class AccUserRoleServiceImpl extends ServiceImpl<AccUserRoleMapper, AccUserRole>
    implements AccUserRoleService{

    @Override
    public List<AccUserRole> listByUserId(Integer userId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(AccUserRole.class)
            .eq(AccUserRole::getUserId, userId)
            .eq(AccUserRole::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public void logicRemoveByRoleId(Integer roleId) {
        baseMapper.logicDelete(Wrappers.lambdaQuery(AccUserRole.class)
            .eq(AccUserRole::getRoleId, roleId));
    }

    @Override
    public List<AccUserRole> getRolesByUserId(Integer userId) {
        LambdaQueryWrapper<AccUserRole> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(AccUserRole::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        wrapper.eq(AccUserRole::getUserId, userId);
        return baseMapper.selectList(wrapper);
    }
}




