package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.PlatformMapper;
import com.miaowen.bh1xlhw.model.entity.Platform;
import com.miaowen.bh1xlhw.model.query.goods.PlatformPageForm;
import com.miaowen.bh1xlhw.repository.PlatformService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_platform(平台管理)】的数据库操作Service实现
 * @createDate 2025-05-07 10:07:26
 */
@Service
public class PlatformServiceImpl extends ServiceImpl<PlatformMapper, Platform>
        implements PlatformService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public void recoverById(Integer id) {
        baseMapper.recoverById(id);
    }

    @Override
    public Page<Platform> pageInfo(PlatformPageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(Platform.class)
                .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(Objects.nonNull(pageForm.getName()), Platform::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getType()), Platform::getType, pageForm.getType())
                .eq(Objects.nonNull(pageForm.getSource()), Platform::getSource, pageForm.getSource())
                .orderByDesc(Platform::getCreateTime));
    }

    @Override
    public Platform getExistById(Integer platformId) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(Platform.class)
                .eq(Platform::getId, platformId)
                .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<Platform> listExistByIds(Collection<Integer> platformIds) {
        if (CollectionUtils.isEmpty(platformIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(Platform.class)
                .in(Platform::getId, platformIds)
                .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(Platform::getCreateTime));
    }

    @Override
    public Map<Integer, Platform> mapByIds(Collection<Integer> platformIds) {
        List<Platform> platforms = listExistByIds(platformIds);
        return StreamUtil.map(platforms, Platform::getId, Function.identity());
    }

    @Override
    public Map<Integer, Platform> mapAll() {
        List<Platform> platforms = allExist();
        return StreamUtil.map(platforms, Platform::getId, Function.identity());
    }

    @Override
    public List<Platform> allExist() {
        return baseMapper.selectList(Wrappers.lambdaQuery(Platform.class)
                .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public Map<Integer, String> allExistMap() {
        List<Platform> platforms = allExist();
        return StreamUtil.map(platforms, Platform::getId, Platform::getName);
    }

}




