package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.PlatformMapper;
import com.miaowen.bh1xlhw.model.entity.Platform;
import com.miaowen.bh1xlhw.model.query.operation.PlatformPageForm;
import com.miaowen.bh1xlhw.repository.PlatformService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_platform(代理商)】的数据库操作Service实现
 * @createDate 2025-05-07 11:28:54
 */
@Service
public class PlatformServiceImpl extends ServiceImpl<PlatformMapper, Platform>
        implements PlatformService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public void recoverById(Integer id) {
        baseMapper.recoverById(id);
    }

    @Override
    public Page<Platform> pageInfo(PlatformPageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(Platform.class)
                .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .eq(Objects.nonNull(pageForm.getPlatformType()), Platform::getPlatformType, pageForm.getPlatformType())
                .eq(Objects.nonNull(pageForm.getOperationType()), Platform::getOperationType, pageForm.getOperationType())
                .orderByDesc(Platform::getCreateTime));
    }

    @Override
    public Map<Integer, Platform> mapByIds(List<Integer> platform) {
        if (CollectionUtils.isEmpty(platform)) {
            return Collections.emptyMap();
        }

        List<Platform> platformList = baseMapper.selectList(Wrappers.<Platform>lambdaQuery()
            .select(Platform::getId, Platform::getAgentId, Platform::getAgentId)
            .in(Platform::getId, platform)
            .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(platformList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(platformList, Platform::getId);
    }

    @Override
    public List<Platform> listAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(Platform.class)
                .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(Platform::getCreateTime));
    }

    @Override
    public Map<Integer, Platform> allMap() {
        List<Platform> platforms = listAll();
        if (CollectionUtils.isEmpty(platforms)){
            return Collections.emptyMap();
        }
        return StreamUtil.map(platforms, Platform::getId);
    }

    @Override
    public Platform getExistById(Integer agentId) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(Platform.class)
            .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .eq(Platform::getId, agentId));
    }

    @Override
    public List<Platform> listExistByIds(List<Integer> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.lambdaQuery(Platform.class)
            .eq(Platform::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .in(Platform::getId, agentIds));
    }
}




