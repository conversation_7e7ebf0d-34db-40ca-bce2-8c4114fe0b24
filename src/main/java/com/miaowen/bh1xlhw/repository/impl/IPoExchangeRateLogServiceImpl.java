package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.config.exception.BizException;
import com.miaowen.bh1xlhw.mapper.PoExchangeRateLogMapper;
import com.miaowen.bh1xlhw.model.entity.PoExchangeRateLog;
import com.miaowen.bh1xlhw.repository.IPoExchangeRateLogService;
import com.mysql.cj.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_currency(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Slf4j
@Service
public class IPoExchangeRateLogServiceImpl extends ServiceImpl<PoExchangeRateLogMapper, PoExchangeRateLog> implements IPoExchangeRateLogService {

    @Resource
    private PoExchangeRateLogMapper poExchangeRateLogMapper;


    @Override
    public BigDecimal getLatestRateByCurrencyUnit(String currencySymbol) {
        if (StringUtils.isNullOrEmpty(currencySymbol)) {
            log.error("IPoExchangeRateLogServiceImpl::getLatestRateBycurrencyUnit():currencyUnit is null!");
            throw new BizException("currencySymbol is null");
        }
        return poExchangeRateLogMapper.getLatestRateByCurrencyUnit(currencySymbol);
    }
}




