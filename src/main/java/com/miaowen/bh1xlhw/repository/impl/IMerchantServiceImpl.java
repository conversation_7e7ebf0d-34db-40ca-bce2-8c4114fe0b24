package com.miaowen.bh1xlhw.repository.impl;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.MerchantMapper;
import com.miaowen.bh1xlhw.model.bo.payment.AlipayConfigBO;
import com.miaowen.bh1xlhw.model.bo.payment.PaypalConfigBO;
import com.miaowen.bh1xlhw.model.bo.payment.StripeConfigBO;
import com.miaowen.bh1xlhw.model.bo.payment.WechatConfigBO;
import com.miaowen.bh1xlhw.model.entity.Merchant;
import com.miaowen.bh1xlhw.repository.IMerchantService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_merchant(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IMerchantServiceImpl extends ServiceImpl<MerchantMapper, Merchant>
        implements IMerchantService {

    @Override
    public Merchant getMerchantById(Integer merchantId) {
        return baseMapper.selectOne(Wrappers.<Merchant>lambdaQuery().eq(Merchant::getId, merchantId)
            .eq(Merchant::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public PaypalConfigBO getPaypalConfigById(Integer id) {
        Merchant merchant = baseMapper.selectById(id);
        if (Objects.isNull(merchant)){
            return new PaypalConfigBO();
        }
        return JSONObject.parseObject(merchant.getConfigData(), PaypalConfigBO.class);
    }

    @Override
    public StripeConfigBO getStripeConfigById(Integer id) {
        Merchant merchant = baseMapper.selectById(id);
        if (Objects.isNull(merchant)){
            return new StripeConfigBO();
        }
        return JSONObject.parseObject(merchant.getConfigData(), StripeConfigBO.class);
    }

    @Override
    public AlipayConfigBO getAliPayConfig(Integer id) {
        Merchant merchant = baseMapper.selectById(id);
        if (Objects.isNull(merchant)){
            return new AlipayConfigBO();
        }
        return JSONObject.parseObject(merchant.getConfigData(), AlipayConfigBO.class);
    }

    @Override
    public WechatConfigBO getWechatPayConfig(Integer id) {
        Merchant merchant = baseMapper.selectById(id);
        if (Objects.isNull(merchant)){
            return new WechatConfigBO();
        }
        return JSONObject.parseObject(merchant.getConfigData(), WechatConfigBO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void logicDeleteByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        baseMapper.logicDeleteBatchIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoverBatchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        baseMapper.recoverBatchIds(ids);
    }
}



