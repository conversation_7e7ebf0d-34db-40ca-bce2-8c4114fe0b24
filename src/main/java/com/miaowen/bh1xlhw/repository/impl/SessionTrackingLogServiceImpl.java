package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.config.annotation.Sharding;
import com.miaowen.bh1xlhw.config.annotation.ShardingParam;
import com.miaowen.bh1xlhw.mapper.SessionTrackingLogMapper;
import com.miaowen.bh1xlhw.model.entity.SessionTrackingLog;
import com.miaowen.bh1xlhw.model.query.logManagement.SessionTrackingForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SessionTrackingVO;
import com.miaowen.bh1xlhw.repository.SessionTrackingLogService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/8 11:37
 */
@Service
public class SessionTrackingLogServiceImpl extends ServiceImpl<SessionTrackingLogMapper, SessionTrackingLog>
        implements SessionTrackingLogService {

    @Sharding
    @Override
    public Page<SessionTrackingLog> pageInfo(@ShardingParam String dateString, SessionTrackingForm form) {
        LambdaQueryWrapper<SessionTrackingLog> wrapper = Wrappers.<SessionTrackingLog>lambdaQuery()
            .like(StringUtils.hasText(form.getLogId()), SessionTrackingLog::getLogId, form.getLogId())
            .eq(StringUtils.hasText(form.getEnvType()), SessionTrackingLog::getEnvType, form.getEnvType())
            .like(StringUtils.hasText(form.getTraceId()), SessionTrackingLog::getTraceId, form.getTraceId())
            .like(StringUtils.hasText(form.getTuid()), SessionTrackingLog::getTuid, form.getTuid())
            .like(StringUtils.hasText(form.getOrderNo()), SessionTrackingLog::getOrderNo, form.getOrderNo())
            .like(StringUtils.hasText(form.getSource()), SessionTrackingLog::getSource, form.getSource())
            .eq(StringUtils.hasText(form.getPlatformCode()), SessionTrackingLog::getPlatformCode, form.getPlatformCode());
        LocalDateTime startTime;
        LocalDateTime endTime;
        if (form.getStartTime() != null && form.getEndTime() != null) {
            startTime = DateUtils.getEast8LocalDateTime(form.getTimezone(), form.getStartTime());
            endTime = DateUtils.getEast8LocalDateTime(form.getTimezone(), form.getEndTime());
        } else {
            //没有的话则需要放入时间范围 默认查当月
            startTime = LocalDate.now().withDayOfMonth(1).atStartOfDay();
            endTime = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()).atTime(LocalTime.MAX);
        }
        wrapper.between(SessionTrackingLog::getCreateTime, startTime, endTime);
        wrapper.orderByDesc(SessionTrackingLog::getCreateTime);
        return baseMapper.selectPage(form.qry(), wrapper);
    }
}
