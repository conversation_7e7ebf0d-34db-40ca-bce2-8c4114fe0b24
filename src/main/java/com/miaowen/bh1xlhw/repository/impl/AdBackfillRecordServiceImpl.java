package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.mapper.AdBackfillRecordMapper;
import com.miaowen.bh1xlhw.model.entity.AdBackfillRecord;
import com.miaowen.bh1xlhw.repository.AdBackfillRecordService;
import com.miaowen.bh1xlhw.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * @Description 广告回传日志记录
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/12 9:11
 */
@Service
public class AdBackfillRecordServiceImpl extends ServiceImpl<AdBackfillRecordMapper, AdBackfillRecord>
        implements AdBackfillRecordService {

    @Override
    public AdBackfillRecord getByOrderNo(String orderNo, LocalDateTime createTime) {
        try {
            MonthShardingTableNameHandler.setParams(DateUtils.toDate(createTime));
            return this.lambdaQuery().eq(AdBackfillRecord::getOrderNo, orderNo).last("limit 1").one();
        }finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }
}
