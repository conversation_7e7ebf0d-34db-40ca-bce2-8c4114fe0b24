package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.config.annotation.Sharding;
import com.miaowen.bh1xlhw.config.annotation.ShardingParam;
import com.miaowen.bh1xlhw.mapper.EmailMapper;
import com.miaowen.bh1xlhw.mapper.EmailOrderSuccessMapper;
import com.miaowen.bh1xlhw.model.entity.Email;
import com.miaowen.bh1xlhw.model.entity.EmailOrderSuccess;
import com.miaowen.bh1xlhw.repository.IEmailOrderSuccessService;
import com.miaowen.bh1xlhw.repository.IEmailService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_email_order_success的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmailOrderSuccessServiceImpl extends ServiceImpl<EmailOrderSuccessMapper, EmailOrderSuccess>
        implements IEmailOrderSuccessService {

    @Sharding
    @Override
    public void save(@ShardingParam String suffix, EmailOrderSuccess emailOrderSuccess) {
        baseMapper.insert(emailOrderSuccess);
    }
}



