package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.mapper.ResultWeiduMapper;
import com.miaowen.bh1xlhw.model.entity.ResultWeidu;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.repository.ResultWeiduService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 16:15
 */
@Service
public class ResultWeiduServiseImpl extends ServiceImpl<ResultWeiduMapper, ResultWeidu>
        implements ResultWeiduService {

    @Override
    public Page<ResultWeidu> getByTag(PageForm page, String tag, boolean isMulti) {
        LambdaQueryWrapper<ResultWeidu> queryWrapper = Wrappers.lambdaQuery(ResultWeidu.class)
                .eq(ResultWeidu::getTag, tag)
                .eq(ResultWeidu::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue())
                .orderByDesc(ResultWeidu::getId);

        return baseMapper.selectPage(page.qry(), queryWrapper);

    }

    @Override
    public Integer saveOrUpdateResultWeidu(ResultWeidu resultWeidu) {
        if (resultWeidu.getId() == null) {
            baseMapper.insert(resultWeidu);
        } else {
            baseMapper.updateById(resultWeidu);
        }
        return resultWeidu.getId();
    }

    @Override
    public void deleteResultWeidu(List<Integer> ids) {
        this.removeBatchByIds(ids);
    }

    @Override
    public void updateResultWeiduById(ResultWeidu resultWeidu) {
        this.updateById(resultWeidu);
    }
}
