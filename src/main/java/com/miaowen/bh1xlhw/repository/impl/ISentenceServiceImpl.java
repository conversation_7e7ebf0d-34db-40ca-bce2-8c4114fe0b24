package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.SentenceMapper;
import com.miaowen.bh1xlhw.model.entity.Sentence;
import com.miaowen.bh1xlhw.repository.ISentenceService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.miaowen.bh1xlhw.constant.CommonConstant.DELETE_TIME;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_language(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class ISentenceServiceImpl extends ServiceImpl<SentenceMapper, Sentence>
        implements ISentenceService {



    @Override
    public List<Sentence> listAllSentence() {
        // 1. 查询未删除数据
        QueryWrapper<Sentence> wrapper = new QueryWrapper<Sentence>().eq(DELETE_TIME, 0);

        // 2. 转换结果
        return this.list(wrapper).stream().collect(Collectors.toList());
    }
}




