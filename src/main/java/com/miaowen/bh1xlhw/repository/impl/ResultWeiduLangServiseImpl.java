package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.ResultWeiduLangMapper;
import com.miaowen.bh1xlhw.mapper.ResultWeiduMapper;
import com.miaowen.bh1xlhw.model.entity.ResultJob;
import com.miaowen.bh1xlhw.model.entity.ResultWeidu;
import com.miaowen.bh1xlhw.model.entity.ResultWeiduLang;
import com.miaowen.bh1xlhw.repository.ResultWeiduLangService;
import com.miaowen.bh1xlhw.repository.ResultWeiduService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/19 16:15
 */
@Service
public class ResultWeiduLangServiseImpl extends ServiceImpl<ResultWeiduLangMapper, ResultWeiduLang>
        implements ResultWeiduLangService {

    @Override
    public List<ResultWeiduLang> findByWeiduId(Integer weiduId) {
        LambdaQueryWrapper<ResultWeiduLang> queryWrapper = Wrappers.lambdaQuery(ResultWeiduLang.class)
                .eq(ResultWeiduLang::getWeiduId,weiduId);
        return this.list(queryWrapper);
    }

    @Override
    public void deleteByWeiduId(Integer weiduId) {
        LambdaQueryWrapper<ResultWeiduLang> queryWrapper = Wrappers.lambdaQuery(ResultWeiduLang.class)
                .eq(ResultWeiduLang::getWeiduId,weiduId);
        this.remove(queryWrapper);
    }

    @Override
    public void deleteByWeiduIds(List<Integer> weiduIds) {
        LambdaQueryWrapper<ResultWeiduLang> queryWrapper = Wrappers.lambdaQuery(ResultWeiduLang.class)
                .in(ResultWeiduLang::getWeiduId,weiduIds);
        this.remove(queryWrapper);
    }

    @Override
    public void saveWeiduLang(ResultWeiduLang resultWeiduLang) {
        this.save(resultWeiduLang);
    }
}
