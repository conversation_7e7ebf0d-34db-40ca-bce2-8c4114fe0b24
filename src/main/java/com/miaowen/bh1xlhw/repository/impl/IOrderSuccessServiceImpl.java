package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.miaowen.bh1xlhw.model.entity.order.OrderMbti;
import com.miaowen.bh1xlhw.model.entity.order.OrderSuccess;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;
import com.miaowen.bh1xlhw.repository.IOrderSuccessService;
import com.miaowen.bh1xlhw.mapper.OrderSuccessMapper;
import com.miaowen.bh1xlhw.utils.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 针对表【po_order_success(交易订单成功表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 19:20:54
 */
@Service
public class IOrderSuccessServiceImpl extends ServiceImpl<OrderSuccessMapper, OrderSuccess>
    implements IOrderSuccessService {

    @Override
    public Page<OrderSuccess> pageInfo(OrderForm orderForm) {
        LambdaQueryWrapper<OrderSuccess> wrapper = Wrappers.lambdaQuery(OrderSuccess.class)
            .eq(StringUtils.hasText(orderForm.getOutTradeNo()), OrderSuccess::getOutTradeNo, orderForm.getOutTradeNo())
            .eq(StringUtils.hasText(orderForm.getThirdOutTradeNo()), OrderSuccess::getThirdOutTradeNo, orderForm.getThirdOutTradeNo())
            .eq(StringUtils.hasText(orderForm.getTgId()), OrderSuccess::getTgId, orderForm.getTgId())
            .eq(StringUtils.hasText(orderForm.getPlatformType()), OrderSuccess::getPlatformType, orderForm.getPlatformType())
            .eq(StringUtils.hasText(orderForm.getCountryCode()), OrderSuccess::getCountryCode, orderForm.getCountryCode())
            .eq(StringUtils.hasText(orderForm.getSource()), OrderSuccess::getSource, orderForm.getSource())
            .eq(Objects.nonNull(orderForm.getGoodsType()), OrderSuccess::getGoodsType, orderForm.getGoodsType())
            .eq(Objects.nonNull(orderForm.getOrderStatus()), OrderSuccess::getPaymentStatus, orderForm.getOrderStatus())
            .eq(StringUtils.hasText(orderForm.getPaymentCode()), OrderSuccess::getPaymentTypeId, orderForm.getPaymentCode())
            .eq(Objects.nonNull(orderForm.getPaymentId()), OrderSuccess::getPaymentTypeId, orderForm.getPaymentId())
            .eq(Objects.nonNull(orderForm.getRedPackGetStatus()), OrderSuccess::getRedPackGetType, orderForm.getRedPackGetStatus())
            .eq(Objects.nonNull(orderForm.getRedPackUseStatus()), OrderSuccess::getRedPackUseType, orderForm.getRedPackUseStatus())
            .eq(Objects.nonNull(orderForm.getEmail()), OrderSuccess::getEmail, orderForm.getEmail())
            .eq(Objects.nonNull(orderForm.getThirdEmail()), OrderSuccess::getThirdEmail, orderForm.getThirdEmail())
            .eq(Objects.nonNull(orderForm.getCardNumber()), OrderSuccess::getCardNumber, orderForm.getCardNumber())
            .eq(Objects.nonNull(orderForm.getReturnType()), OrderSuccess::getIsEmail, orderForm.getReturnType())
            .orderByDesc(OrderSuccess::getCreateTime);
        if (StringUtils.hasText(orderForm.getGoodsIds())){
            List<Integer> goodsIds = Arrays.stream(orderForm.getGoodsIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            wrapper.in(Objects.nonNull(orderForm.getGoodsType()), OrderSuccess::getGoodsId, goodsIds);
        }
        if (Objects.nonNull(orderForm.getStartTime())) {
            wrapper.ge(OrderSuccess::getPaymentTime, DateUtils.getEast8LocalDateTime(orderForm.getTimezone(),
                orderForm.getStartTime()));
        }
        if (Objects.nonNull(orderForm.getEndTime())) {
            wrapper.le(OrderSuccess::getPaymentTime, DateUtils.getEast8LocalDateTime(orderForm.getTimezone(), orderForm.getEndTime()));
        }
        return baseMapper.selectPage(orderForm.qry(), wrapper);
    }
}




