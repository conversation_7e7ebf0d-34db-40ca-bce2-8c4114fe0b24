package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.mapper.ScoreManagementMapper;
import com.miaowen.bh1xlhw.model.entity.ScoreManagement;
import com.miaowen.bh1xlhw.repository.IScoreManagementService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
@Service
public class IScoreManagementServiceImpl extends ServiceImpl<ScoreManagementMapper, ScoreManagement>
        implements IScoreManagementService {

    @Override
    public List<Integer> findByTag(String tag, boolean isMulti) {
        QueryWrapper<ScoreManagement> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tag", tag);
        queryWrapper.eq("type", isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        return this.list(queryWrapper).stream().map(ScoreManagement::getId).collect(Collectors.toList());

    }

    @Override
    public List<ScoreManagement> findByTagAndIsMulti(String tag, boolean isMulti) {
        QueryWrapper<ScoreManagement> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("tag", tag);
        queryWrapper.eq("type", isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        return this.list(queryWrapper);
    }
}
