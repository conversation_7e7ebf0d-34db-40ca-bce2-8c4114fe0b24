package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.mapper.ScoreManagementMapper;
import com.miaowen.bh1xlhw.model.entity.ScoreManagement;
import com.miaowen.bh1xlhw.repository.IScoreManagementService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:52
 */
@Service
public class IScoreManagementServiceImpl extends ServiceImpl<ScoreManagementMapper, ScoreManagement>
        implements IScoreManagementService {

    @Override
    public List<Integer> findByTag(String tag, Integer version, boolean isMulti) {
        return this.list(Wrappers.<ScoreManagement>lambdaQuery()
                .eq(ScoreManagement::getTag, tag)
                .eq(ScoreManagement::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue())
                .eq(ScoreManagement::getVersion, version))
                .stream()
                .map(ScoreManagement::getId)
                .collect(Collectors.toList());
    }

    @Override
    public List<ScoreManagement> findByTagAndIsMulti(String tag, Integer version, boolean isMulti) {
        return this.list(Wrappers.<ScoreManagement>lambdaQuery()
                .eq(ScoreManagement::getTag, tag)
                .eq(ScoreManagement::getVersion, version)
                .eq(ScoreManagement::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue()));
    }
}
