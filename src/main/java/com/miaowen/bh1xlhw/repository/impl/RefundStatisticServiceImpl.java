package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.model.entity.RefundStatistic;
import com.miaowen.bh1xlhw.repository.IRefundRecordService;
import com.miaowen.bh1xlhw.repository.RefundStatisticService;
import com.miaowen.bh1xlhw.mapper.RefundStatisticMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * 针对表【po_refund_statistic(退款统计表,每天统计前一天的数据)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-27 21:13:33
 */
@Service
@AllArgsConstructor
public class RefundStatisticServiceImpl extends ServiceImpl<RefundStatisticMapper, RefundStatistic>
    implements RefundStatisticService{

    @Override
    public RefundStatistic getByDate(LocalDate lastMonthDay) {
        return baseMapper.selectOne(Wrappers.<RefundStatistic>lambdaQuery()
            .eq(RefundStatistic::getStatisticDate, lastMonthDay));
    }

    @Override
    public List<RefundStatistic> listRefundStatisticByMonth(LocalDate startDate, LocalDate endDate) {
        return baseMapper.selectList(Wrappers.<RefundStatistic>lambdaQuery()
            .ge(RefundStatistic::getStatisticDate, startDate)
            .le(RefundStatistic::getStatisticDate, endDate));
    }
}




