package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.DictionaryTypeMapper;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.entity.DictionaryType;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;
import com.miaowen.bh1xlhw.repository.DictionaryTypeService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/3/18 17:49
 */
@Service
public class DictionaryTypeServiceImpl extends ServiceImpl<DictionaryTypeMapper, DictionaryType> implements DictionaryTypeService {

    @Override
    public DictionaryType getOneByCode(String code) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(DictionaryType.class).eq(DictionaryType::getCode, code).last(SQLConstant.LIMIT_ONE));
    }

    @Override
    public void logicBatchRemoveById(List<Integer> ids) {
        baseMapper.logicDeleteBatchIds(ids);
    }

    @Override
    public Page<DictionaryType> pageInfo(DictionaryPageForm form) {
        LambdaQueryWrapper<DictionaryType> wrapper = Wrappers.lambdaQuery(DictionaryType.class)
                .like(Objects.nonNull(form.getName()), DictionaryType::getName, form.getName())
                .eq(Objects.nonNull(form.getStatus()), DictionaryType::getStatus, form.getStatus())
                .eq(Objects.nonNull(form.getCode()), DictionaryType::getCode, form.getCode())
                .eq(DictionaryType::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByAsc(Arrays.asList(DictionaryType::getSort, DictionaryType::getId));
        return baseMapper.selectPage(form.qry(), wrapper);
    }
}
