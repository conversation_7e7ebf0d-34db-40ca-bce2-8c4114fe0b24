package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.GoodsPromotionTraditionalMapper;
import com.miaowen.bh1xlhw.model.entity.GoodsPromotionTraditional;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionPageForm;
import com.miaowen.bh1xlhw.repository.GoodsPromotionTraditionalService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 针对表【po_goods_promotion_traditional(推广商品多语言)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 15:13:26
 */
@Service
public class GoodsPromotionTraditionalServiceImpl extends ServiceImpl<GoodsPromotionTraditionalMapper, GoodsPromotionTraditional>
        implements GoodsPromotionTraditionalService {
    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public GoodsPromotionTraditional getExistById(Integer id) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(GoodsPromotionTraditional.class)
                .eq(GoodsPromotionTraditional::getId, id)
                .eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<GoodsPromotionTraditional> listAll() {
        LambdaQueryWrapper<GoodsPromotionTraditional> wrapper = Wrappers.lambdaQuery(GoodsPromotionTraditional.class)
                .eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(GoodsPromotionTraditional::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public Page<GoodsPromotionTraditional> pageInfo(GoodsPromotionPageForm pageForm) {
        LambdaQueryWrapper<GoodsPromotionTraditional> wrapper = Wrappers.<GoodsPromotionTraditional>lambdaQuery()
            .in(Objects.nonNull(pageForm.getGoodsIds()), GoodsPromotionTraditional::getGoodsId, pageForm.getGoodsIds())
            .in(StringUtils.hasText(pageForm.getApplication()), GoodsPromotionTraditional::getApplication, pageForm.getApplication())
            .like(Objects.nonNull(pageForm.getTgId()), GoodsPromotionTraditional::getTgId, pageForm.getTgId())
            .eq(Objects.nonNull(pageForm.getPlatformType()), GoodsPromotionTraditional::getPlatformType, pageForm.getPlatformType())
            .eq(Objects.nonNull(pageForm.getPlatformId()), GoodsPromotionTraditional::getPlatformId, pageForm.getPlatformId())
            .eq(Objects.nonNull(pageForm.getOperationManagerId()), GoodsPromotionTraditional::getOperationManagerId, pageForm.getOperationManagerId())
            .eq(Objects.nonNull(pageForm.getOperationId()), GoodsPromotionTraditional::getOperationId, pageForm.getOperationId())
            .eq(Objects.nonNull(pageForm.getPromotionStatus()), GoodsPromotionTraditional::getPromotionStatus, pageForm.getPromotionStatus())
            .orderByDesc(GoodsPromotionTraditional::getCreateTime);

        if (BooleanEnum.TRUE.getValue().equals(pageForm.getIsRecycle())){
            wrapper.gt(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }else {
            wrapper.eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }
        return baseMapper.selectPage(pageForm.qry(), wrapper);

    }

    @Override
    public List<GoodsPromotionTraditional> listByPriceAndOperationId(Integer priceId, Integer operationId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(GoodsPromotionTraditional.class)
                .eq(GoodsPromotionTraditional::getPriceId, priceId)
                .eq(GoodsPromotionTraditional::getOperationId, operationId)
                .eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<GoodsPromotionTraditional> listByAdvertiseAccountId(Integer advertiseAccountId) {
        return baseMapper.selectList(Wrappers.lambdaQuery(GoodsPromotionTraditional.class)
                .eq(GoodsPromotionTraditional::getAdvertiseAccountId, advertiseAccountId)
                .eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public Map<Integer, GoodsPromotionTraditional> mapById(List<Integer> goodsTraditionalPromotionIds) {
        if (CollectionUtils.isEmpty(goodsTraditionalPromotionIds)) {
            return Collections.emptyMap();
        }
        List<GoodsPromotionTraditional> goodsPromotionTraditionalList =
                baseMapper.selectList(Wrappers.lambdaQuery(GoodsPromotionTraditional.class)
                        .in(GoodsPromotionTraditional::getId, goodsTraditionalPromotionIds)
                        .eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(goodsPromotionTraditionalList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsPromotionTraditionalList, GoodsPromotionTraditional::getId);
    }

    @Override
    public GoodsPromotionTraditional getByTgId(String tgId) {
        return baseMapper.selectOne(Wrappers.<GoodsPromotionTraditional>lambdaQuery()
            .eq(GoodsPromotionTraditional::getTgId, tgId)
            .eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    public List<GoodsPromotionTraditional> listTgId(List<String> traditionalTiIds) {
        if (CollectionUtils.isEmpty(traditionalTiIds)){
            return Collections.emptyList();
        }
        return baseMapper.selectList(Wrappers.<GoodsPromotionTraditional>lambdaQuery()
            .in(GoodsPromotionTraditional::getTgId, traditionalTiIds)
            .eq(GoodsPromotionTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoverBatchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        baseMapper.recoverBatchIds(ids);
    }

    @Override
    public String getTgIdById(Integer id) {
        return baseMapper.selectById(id).getTgId();
    }
}




