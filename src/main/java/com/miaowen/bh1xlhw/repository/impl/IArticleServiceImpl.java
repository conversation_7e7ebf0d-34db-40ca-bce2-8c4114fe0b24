package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.mapper.ArticleMapper;
import com.miaowen.bh1xlhw.model.entity.Article;
import com.miaowen.bh1xlhw.model.query.article.ArticlePageForm;
import com.miaowen.bh1xlhw.repository.IArticleService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
@Service
public class IArticleServiceImpl extends ServiceImpl<ArticleMapper, Article>
        implements IArticleService {

    @Override
    public Page<Article> pageSort(ArticlePageForm page, boolean isMulti) {
        LambdaQueryWrapper<Article> queryWrapper = Wrappers.lambdaQuery(Article.class)
                .eq(Article::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue())
                .like(!StringUtils.isEmpty(page.getTitle()), Article::getTitle, page.getTitle())
                .eq(Objects.nonNull(page.getArticleType()), Article::getArticleType, page.getArticleType())
                .orderByDesc(Arrays.asList(Article::getSort, Article::getId));

        return baseMapper.selectPage(page.qry(), queryWrapper);
    }

    @Override
    public void updateStatus(Integer id, Integer status) {
        LambdaUpdateWrapper<Article> updateWrapper =
                Wrappers.lambdaUpdate(Article.class).eq(Article::getId, id).set(Article::getStatus, status);
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public Map<Integer, Article> mapByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        List<Article> list = baseMapper.selectList(Wrappers.lambdaQuery(Article.class)
                .eq(Article::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .in(Article::getId, ids));
        if (CollectionUtils.isEmpty(list)){
            return Collections.emptyMap();
        }
        return StreamUtil.map(list, Article::getId);
    }


}
