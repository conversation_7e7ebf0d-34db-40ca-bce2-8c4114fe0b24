package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.EmailMapper;
import com.miaowen.bh1xlhw.mapper.EmailPromotionSendRecordMapper;
import com.miaowen.bh1xlhw.model.entity.Email;
import com.miaowen.bh1xlhw.model.entity.EmailPromotionSendRecord;
import com.miaowen.bh1xlhw.repository.IEmailPromotionSendRecordService;
import com.miaowen.bh1xlhw.repository.IEmailService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_system_merchant(后台用户表)】的数据库操作Service实现
 * @Date 2025/5/7 11:28
 */
@Service
public class IEmailPromotionSendRecordServiceImpl extends ServiceImpl<EmailPromotionSendRecordMapper, EmailPromotionSendRecord>
        implements IEmailPromotionSendRecordService {


    @Override
    public void setSuccessNum(Integer id) {
        LambdaUpdateWrapper<EmailPromotionSendRecord> lambdaUpdate = Wrappers.lambdaUpdate();
        lambdaUpdate.eq(EmailPromotionSendRecord::getId, id)
                .setSql("success_num = success_num + 1");
        baseMapper.update(null, lambdaUpdate);
    }
}



