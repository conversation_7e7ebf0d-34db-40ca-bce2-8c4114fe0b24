package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.config.mybatisPlus.MonthShardingTableNameHandler;
import com.miaowen.bh1xlhw.mapper.EventLogMapper;
import com.miaowen.bh1xlhw.model.entity.EventLog;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.repository.EventLogService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/8 16:04
 */
@Service
public class EventLogServiceImpl extends ServiceImpl<EventLogMapper, EventLog>
        implements EventLogService {
    @Override
    public int countByCondition(BrowseCountForm browseCountForm) {
        try {
            QueryWrapper<EventLog> eventLogQueryWrapper = new QueryWrapper<>();
            //统计进入首页的人数
            eventLogQueryWrapper.eq("event_code", "10000");
            eventLogQueryWrapper.eq("tgid", browseCountForm.getTgid());
            MonthShardingTableNameHandler.setParams(browseCountForm.getStartTime());
            eventLogQueryWrapper.ge("create_time", browseCountForm.getStartTime());
            eventLogQueryWrapper.le("create_time", browseCountForm.getEndTime());
            if (!StringUtils.isEmpty(browseCountForm.getSource())) {
                eventLogQueryWrapper.eq("source", browseCountForm.getSource());
            }
            eventLogQueryWrapper.select("DISTINCT log_id");
            return Math.toIntExact(baseMapper.selectCount(eventLogQueryWrapper));
        } finally {
            MonthShardingTableNameHandler.clearParams();
        }
    }
}
