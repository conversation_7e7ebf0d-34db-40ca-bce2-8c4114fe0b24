package com.miaowen.bh1xlhw.repository.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.mapper.DictionaryMapper;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;
import com.miaowen.bh1xlhw.repository.DictionaryService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;


/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/3/18 17:49
 */
@Service
public class DictionaryServiceImpl extends ServiceImpl<DictionaryMapper, Dictionary> implements DictionaryService {

    @Override
    public void logicBatchRemoveByTypeIds(List<Integer> typeIds) {
        baseMapper.logicDelete(Wrappers.<Dictionary>lambdaQuery().in(Dictionary::getDictionaryTypeId, typeIds));
    }

    @Override
    public void logicBatchRemoveByIds(List<Integer> ids) {
        baseMapper.logicDeleteBatchIds(ids);

    }

    @Override
    public Page<Dictionary> pageInfo(DictionaryPageForm form) {
        LambdaQueryWrapper<Dictionary> wrapper = Wrappers.lambdaQuery(Dictionary.class)
                .eq(Objects.nonNull(form.getDictionaryTypeId()), Dictionary::getDictionaryTypeId,
                        form.getDictionaryTypeId())
                .like(Objects.nonNull(form.getName()), Dictionary::getName, form.getName())
                .eq(Objects.nonNull(form.getStatus()), Dictionary::getStatus, form.getStatus())
                .eq(Objects.nonNull(form.getCode()), Dictionary::getCode, form.getCode())
                .eq(Dictionary::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByAsc(Arrays.asList(Dictionary::getSort, Dictionary::getId));
        return baseMapper.selectPage(form.qry(), wrapper);
    }

    @Override
    public List<Dictionary> listByTypeId(Integer id) {
        LambdaQueryWrapper<Dictionary> wrapper = Wrappers.lambdaQuery(Dictionary.class)
                .eq(Dictionary::getDictionaryTypeId, id)
                .eq(Dictionary::getStatus, BooleanEnum.TRUE.getValue())
                .orderByAsc(Arrays.asList(Dictionary::getSort, Dictionary::getId))
                .eq(Dictionary::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        return baseMapper.selectList(wrapper);
    }
}
