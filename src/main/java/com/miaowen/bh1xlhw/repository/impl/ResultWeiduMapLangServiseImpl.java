package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.ResultWeiduMapLangMapper;
import com.miaowen.bh1xlhw.model.entity.ResultWeiduMapLang;
import com.miaowen.bh1xlhw.repository.ResultWeiduMapLangService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 16:15
 */
@Service
public class ResultWeiduMapLangServiseImpl extends ServiceImpl<ResultWeiduMapLangMapper, ResultWeiduMapLang>
        implements ResultWeiduMapLangService {

    @Override
    public List<ResultWeiduMapLang> findByWeiduMapId(Integer weiduMapId) {
        LambdaQueryWrapper<ResultWeiduMapLang> wrapper = Wrappers.lambdaQuery(ResultWeiduMapLang.class)
                .eq(ResultWeiduMapLang::getWeiduMapId, weiduMapId);
        return list(wrapper);
    }

    @Override
    public void deleteByWeiduMapId(Integer weiduMapId) {
        LambdaQueryWrapper<ResultWeiduMapLang> wrapper = Wrappers.lambdaQuery(ResultWeiduMapLang.class)
                .eq(ResultWeiduMapLang::getWeiduMapId, weiduMapId);
        remove(wrapper);
    }

    @Override
    public void deleteByWeiduMapIds(List<Integer> weiduMapIds) {
        LambdaQueryWrapper<ResultWeiduMapLang> wrapper = Wrappers.lambdaQuery(ResultWeiduMapLang.class)
                .in(ResultWeiduMapLang::getWeiduMapId, weiduMapIds);
        baseMapper.delete(wrapper);
    }

    @Override
    public void saveWeiduMapLang(ResultWeiduMapLang resultWeiduMapLang) {
        save(resultWeiduMapLang);
    }
}
