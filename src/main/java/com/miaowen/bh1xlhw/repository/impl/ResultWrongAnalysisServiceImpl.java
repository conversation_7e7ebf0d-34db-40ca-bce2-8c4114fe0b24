package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.enums.GoodsTypeEnum;
import com.miaowen.bh1xlhw.constant.enums.LangTypeEnum;
import com.miaowen.bh1xlhw.mapper.ResultWrongAnalysisMapper;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysis;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysisPrice;
import com.miaowen.bh1xlhw.repository.ResultWrongAnalysisService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 10:23
 */
@Service
public class ResultWrongAnalysisServiceImpl extends ServiceImpl<ResultWrongAnalysisMapper, ResultWrongAnalysis>
        implements ResultWrongAnalysisService {

    @Override
    public List<ResultWrongAnalysis> getListByTag(String tag, boolean isMulti) {
        LambdaQueryWrapper<ResultWrongAnalysis> queryWrapper = Wrappers.lambdaQuery(ResultWrongAnalysis.class)
                .eq(ResultWrongAnalysis::getCategoryTag, tag);
        queryWrapper.eq(ResultWrongAnalysis::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public List<ResultWrongAnalysis> findByExamNum(String tag, String examNum, boolean isMulti) {
        LambdaQueryWrapper<ResultWrongAnalysis> queryWrapper = Wrappers.lambdaQuery(ResultWrongAnalysis.class)
                .eq(ResultWrongAnalysis::getCategoryTag, tag)
                .eq(ResultWrongAnalysis::getExamNum, examNum);
        queryWrapper.eq(ResultWrongAnalysis::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        return baseMapper.selectList(queryWrapper);
    }


    @Override
    public void editExamAnswer(String tag, String examNum, int score, String answer, boolean isMulti) {

        LambdaUpdateWrapper<ResultWrongAnalysis> updateWrapper = Wrappers.lambdaUpdate(ResultWrongAnalysis.class)
                .eq(ResultWrongAnalysis::getCategoryTag, tag)
                .eq(ResultWrongAnalysis::getExamNum, examNum)
                .set(ResultWrongAnalysis::getScore, score)
                .set(ResultWrongAnalysis::getExamAnswer, answer);
        updateWrapper.eq(ResultWrongAnalysis::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        baseMapper.update(null, updateWrapper);
    }

    @Override
    public void deleteByExamNum(String tag, String examNum, boolean isMulti) {
        // 构建删除条件
        LambdaQueryWrapper<ResultWrongAnalysis> wrapper = Wrappers.lambdaQuery(ResultWrongAnalysis.class)
                .eq(ResultWrongAnalysis::getCategoryTag, tag)
                .eq(ResultWrongAnalysis::getExamNum, examNum);
        wrapper.eq(ResultWrongAnalysis::getType, isMulti ? GoodsTypeEnum.MULTILINGUAL.getValue() : GoodsTypeEnum.TRADITIONAL.getValue());
        // 执行删除
        baseMapper.delete(wrapper);
    }

    @Override
    public void editExamAnswerDetail(String tag, String examNum, String examAnswer, int score, String explain, String langType) {
        LambdaUpdateWrapper<ResultWrongAnalysis> updateWrapper = Wrappers.lambdaUpdate(ResultWrongAnalysis.class)
                .eq(ResultWrongAnalysis::getCategoryTag, tag)
                .eq(ResultWrongAnalysis::getExamNum, examNum)
                .set(ResultWrongAnalysis::getScore, score)
                .set(ResultWrongAnalysis::getExamAnswer, examAnswer);
        updateWrapper.set(ResultWrongAnalysis::getExamExplain, explain);
        updateWrapper.eq(ResultWrongAnalysis::getLangType, langType);
        baseMapper.update(null, updateWrapper);

    }

}
