package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.constant.enums.BooleanEnum;
import com.miaowen.bh1xlhw.model.entity.GoodsTraditional;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPageForm;
import com.miaowen.bh1xlhw.repository.IGoodsTraditionalService;
import com.miaowen.bh1xlhw.mapper.GoodsTraditionalMapper;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 针对表【po_goods_traditional(普通商品表)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 15:13:26
 */
@Service
public class GoodsTraditionalServiceImpl extends ServiceImpl<GoodsTraditionalMapper, GoodsTraditional>
        implements IGoodsTraditionalService {

    @Override
    public Page<GoodsTraditional> pageInfo(GoodsPageForm pageForm) {
        LambdaQueryWrapper<GoodsTraditional> wrapper = Wrappers.lambdaQuery(GoodsTraditional.class)
                .like(Objects.nonNull(pageForm.getName()),GoodsTraditional::getName, pageForm.getName())
                .eq(Objects.nonNull(pageForm.getGoodsCategoryId()),GoodsTraditional::getGoodsCategoryId, pageForm.getGoodsCategoryId())
                .orderByDesc(GoodsTraditional::getCreateTime);

        if (BooleanEnum.TRUE.getValue().equals(pageForm.getIsRecycle())){
            wrapper.gt(GoodsTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }else {
            wrapper.eq(GoodsTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME);
        }
        return baseMapper.selectPage(pageForm.qry(), wrapper);
    }

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public Map<Integer, GoodsTraditional> mapNameByIds(List<Integer> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyMap();
        }

        List<GoodsTraditional> goodsTraditionalList = baseMapper.selectList(Wrappers.<GoodsTraditional>lambdaQuery()
                .select(GoodsTraditional::getId, GoodsTraditional::getName, GoodsTraditional::getGoodsCategoryId,GoodsTraditional::getWebPackageName)
                .in(GoodsTraditional::getId, goodsIds)
                .eq(GoodsTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(goodsTraditionalList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsTraditionalList, GoodsTraditional::getId);
    }

    @Override
    public List<GoodsTraditional> listAll() {
        LambdaQueryWrapper<GoodsTraditional> wrapper = Wrappers.lambdaQuery(GoodsTraditional.class)
                .eq(GoodsTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(GoodsTraditional::getCreateTime);
        return baseMapper.selectList(wrapper);
    }

    @Override
    public List<Integer> listIdsByName(String goodsName) {
        LambdaQueryWrapper<GoodsTraditional> wrapper = Wrappers.lambdaQuery(GoodsTraditional.class)
                .eq(GoodsTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(GoodsTraditional::getName, goodsName);
        return baseMapper.selectList(wrapper).stream().map(GoodsTraditional::getId).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, GoodsTraditional> mapById(List<Integer> goodsTraditionalPromotionIds) {
        if (CollectionUtils.isEmpty(goodsTraditionalPromotionIds)) {
            return Collections.emptyMap();
        }
        List<GoodsTraditional> goodsTraditionalList =
                baseMapper.selectList(Wrappers.lambdaQuery(GoodsTraditional.class)
                        .in(GoodsTraditional::getId, goodsTraditionalPromotionIds)
                        .eq(GoodsTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(goodsTraditionalList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(goodsTraditionalList, GoodsTraditional::getId);
    }

    @Override
    public List<Integer> listIdByName(String goodsName, Integer productId) {
        List<GoodsTraditional> goodsTraditionalList =
            baseMapper.selectList(Wrappers.lambdaQuery(GoodsTraditional.class)
                .select(GoodsTraditional::getId)
                .in(GoodsTraditional::getName, goodsName)
                .eq(GoodsTraditional::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(goodsTraditionalList)){
            return Collections.emptyList();
        }
        return goodsTraditionalList.stream().map(GoodsTraditional::getId).collect(Collectors.toList());

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recoverBatchByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)){
            return;
        }
        baseMapper.recoverBatchIds(ids);
    }
}




