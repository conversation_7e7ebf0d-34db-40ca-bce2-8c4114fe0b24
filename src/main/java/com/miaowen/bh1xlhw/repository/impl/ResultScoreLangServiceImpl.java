package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.mapper.ResultScoreLangMapper;
import com.miaowen.bh1xlhw.model.entity.ResultScoreLang;
import com.miaowen.bh1xlhw.repository.ResultScoreLangService;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
@Service
public class ResultScoreLangServiceImpl extends ServiceImpl<ResultScoreLangMapper, ResultScoreLang>
        implements ResultScoreLangService {
    @Override
    public List<ResultScoreLang> findByScoreId(Integer scoreId) {
        LambdaQueryWrapper<ResultScoreLang> queryWrapper = Wrappers.lambdaQuery(ResultScoreLang.class)
                .eq(ResultScoreLang::getResultScoreId, scoreId);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public void add(ResultScoreLang resultScoreLang) {
        baseMapper.insert(resultScoreLang);
    }



    @Override
    public void deleteByScoreIds(List<Integer> ids) {
        LambdaUpdateWrapper<ResultScoreLang> updateWrapper = Wrappers.lambdaUpdate(ResultScoreLang.class)
                .in(ResultScoreLang::getResultScoreId, ids);
        baseMapper.delete(updateWrapper);
    }

    @Override
    public void deleteByScoreId(Integer scoreId) {
        LambdaUpdateWrapper<ResultScoreLang> updateWrapper = Wrappers.lambdaUpdate(ResultScoreLang.class)
                .eq(ResultScoreLang::getResultScoreId, scoreId);
        baseMapper.delete(updateWrapper);
    }

    @Override
    public void update(ResultScoreLang resultScoreLang) {
        deleteByScoreId(resultScoreLang.getResultScoreId());
        add(resultScoreLang);
    }
}
