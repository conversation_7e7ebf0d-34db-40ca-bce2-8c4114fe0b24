package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.DomainMapper;
import com.miaowen.bh1xlhw.model.entity.Domain;
import com.miaowen.bh1xlhw.model.query.operation.DomainPageForm;
import com.miaowen.bh1xlhw.repository.DomainService;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_domin(域名管理)】的数据库操作Service实现
 * @createDate 2025-05-07 16:15:09
 */
@Service
public class DomainServiceImpl extends ServiceImpl<DomainMapper, Domain>
        implements DomainService {

    @Override
    public void logicRemoveById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public void recoverById(Integer id) {
        baseMapper.recoverById(id);
    }

    @Override
    public Page<Domain> pageInfo(DomainPageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(Domain.class)
                .eq(Domain::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .like(Objects.nonNull(pageForm.getDomain()), Domain::getDomain, pageForm.getDomain())
                .orderByDesc(Domain::getCreateTime));
    }

    @Override
    public Map<Integer, Domain> mapNameByIds(List<Integer> domainIds) {
        if (CollectionUtils.isEmpty(domainIds)) {
            return Collections.emptyMap();
        }

        List<Domain> domainList = baseMapper.selectList(Wrappers.<Domain>lambdaQuery()
                .select(Domain::getId, Domain::getDomain)
                .in(Domain::getId, domainIds)
                .eq(Domain::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));

        if (CollectionUtils.isEmpty(domainList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(domainList, Domain::getId);
    }

    @Override
    public List<Domain> listAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(Domain.class)
                .eq(Domain::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(Domain::getCreateTime));
    }

    @Override
    public Map<Integer, Domain> mapAll() {
        List<Domain> domainList = listAll();
        return StreamUtil.map(domainList, Domain::getId);
    }

    @Override
    public Domain getByDomain(String domain) {
        return baseMapper.selectOne(Wrappers.lambdaQuery(Domain.class)
                .eq(Domain::getDomain, domain)
                .eq(Domain::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
                .orderByDesc(Domain::getCreateTime));
    }

}




