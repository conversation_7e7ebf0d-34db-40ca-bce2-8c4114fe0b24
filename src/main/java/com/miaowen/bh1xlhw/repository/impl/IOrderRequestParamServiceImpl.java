package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.mapper.OrderRequestParamMapper;
import com.miaowen.bh1xlhw.model.entity.order.OrderRequestParam;
import com.miaowen.bh1xlhw.repository.IOrderRequestParamService;
import org.springframework.stereotype.Service;

/**
 * 针对表【po_order_request_param(订单广告请求参数)】的数据库操作Service实现
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-21 15:51:17
 */
@Service
public class IOrderRequestParamServiceImpl extends ServiceImpl<OrderRequestParamMapper, OrderRequestParam>
    implements IOrderRequestParamService {

    @Override
    public OrderRequestParam getByOrderId(Long orderId) {
        return baseMapper.selectOne(Wrappers.<OrderRequestParam>lambdaQuery().eq(OrderRequestParam::getOrderId, orderId)
            .last(SQLConstant.LIMIT_ONE));
    }
}




