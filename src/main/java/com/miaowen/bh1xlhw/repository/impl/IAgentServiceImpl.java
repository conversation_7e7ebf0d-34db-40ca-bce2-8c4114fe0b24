package com.miaowen.bh1xlhw.repository.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.miaowen.bh1xlhw.constant.SQLConstant;
import com.miaowen.bh1xlhw.model.entity.Agent;
import com.miaowen.bh1xlhw.model.query.operation.AgentPageForm;
import com.miaowen.bh1xlhw.repository.IAgentService;
import com.miaowen.bh1xlhw.mapper.AgentMapper;
import com.miaowen.bh1xlhw.utils.StreamUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 针对表【po_agent(代理商id)】的数据库操作Service实现
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-06-23 21:55:59
 */
@Service
public class IAgentServiceImpl extends ServiceImpl<AgentMapper, Agent>
    implements IAgentService {

    @Override
    public Map<Integer, Agent> mapByIds(Collection<Integer> agentIds) {
        if (CollectionUtils.isEmpty(agentIds)) {
            return Collections.emptyMap();
        }
        List<Agent> agentList = baseMapper.selectList(Wrappers.lambdaQuery(Agent.class)
            .in(Agent::getId, agentIds)
            .eq(Agent::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
        if (CollectionUtils.isEmpty(agentList)) {
            return Collections.emptyMap();
        }
        return StreamUtil.map(agentList, Agent::getId);
    }

    @Override
    public void logicDeleteById(Integer id) {
        baseMapper.logicDeleteById(id);
    }

    @Override
    public Page<Agent> pageInfo(AgentPageForm pageForm) {
        return baseMapper.selectPage(pageForm.qry(), Wrappers.lambdaQuery(Agent.class)
            .eq(Agent::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME)
            .orderByDesc(Agent::getCreateTime));
    }

    @Override
    public List<Agent> listAll() {
        return baseMapper.selectList(Wrappers.lambdaQuery(Agent.class)
            .eq(Agent::getDeleteTime, SQLConstant.DEFAULT_DELETE_TIME));
    }
}




