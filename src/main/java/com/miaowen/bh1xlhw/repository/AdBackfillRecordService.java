package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.AdBackfillRecord;

import java.time.LocalDateTime;

/**
 * @Description
 * @author：huang<PERSON>
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/12 9:09
 */
public interface AdBackfillRecordService extends IService<AdBackfillRecord> {

    AdBackfillRecord getByOrderNo(String orderNo, LocalDateTime createTime);


}
