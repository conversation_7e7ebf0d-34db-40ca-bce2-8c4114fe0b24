package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.EmailUnsubscribe;
import com.miaowen.bh1xlhw.model.query.email.UnsubscribeEmailForm;


/**
 * 通知邮件记录
 *
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface IEmailUnsubscribeService extends IService<EmailUnsubscribe> {

    Page<EmailUnsubscribe> pageInfo(UnsubscribeEmailForm form);

    void recoverUnsubscribe(Integer id);

}
