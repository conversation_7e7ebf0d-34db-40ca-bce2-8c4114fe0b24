package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

import com.miaowen.bh1xlhw.model.entity.DictionaryType;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;

import java.util.List;


/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface DictionaryTypeService extends IService<DictionaryType> {


    DictionaryType getOneByCode(String code);

    void logicBatchRemoveById(List<Integer> ids);

    Page<DictionaryType> pageInfo(DictionaryPageForm form);

}
