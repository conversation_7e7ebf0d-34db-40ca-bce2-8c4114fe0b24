package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.SessionTrackingLog;
import com.miaowen.bh1xlhw.model.query.logManagement.SessionTrackingForm;
import com.miaowen.bh1xlhw.model.vo.logManagement.SessionTrackingVO;

/**
 * @Description 会话追踪日志持久层service
 * @Author：huanglong
 * @Date：2025/5/8 11:36
 */
public interface SessionTrackingLogService extends IService<SessionTrackingLog> {
    Page<SessionTrackingLog> pageInfo(String dateString, SessionTrackingForm form);

}
