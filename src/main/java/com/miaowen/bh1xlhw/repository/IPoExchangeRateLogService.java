package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.PoExchangeRateLog;

import java.math.BigDecimal;

/**
 * @ClassName IPoExchangeRateLogService
 * @Description 针对表【po_exchange_rate_logs(后台系统货币表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 16:15
 */
public interface IPoExchangeRateLogService extends IService<PoExchangeRateLog> {

    BigDecimal getLatestRateByCurrencyUnit(String currencySymbol);
}
