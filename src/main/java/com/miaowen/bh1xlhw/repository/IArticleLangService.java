package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.ArticleLang;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:52
 */
public interface IArticleLangService extends IService<ArticleLang> {

    List<ArticleLang> findByArticleId(Integer id);

    void add(ArticleLang ArticleLang);

    void deleteByArticleId(Integer id);

}
