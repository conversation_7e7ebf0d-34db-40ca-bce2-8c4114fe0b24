package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.GoodsMultilingual;
import com.miaowen.bh1xlhw.model.entity.GoodsPromotionMultilingual;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPromotionPageForm;

import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 针对表【po_goods_promotion_multilingual(推广商品多语言)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 09:46:38
 */
public interface GoodsPromotionMultilingualService extends IService<GoodsPromotionMultilingual> {

    void logicRemoveById(Integer id);

    GoodsPromotionMultilingual getExistById(Integer id);

    List<GoodsPromotionMultilingual> listAll();

    Page<GoodsPromotionMultilingual> pageInfo(GoodsPromotionPageForm pageForm);

    List<GoodsPromotionMultilingual> listByPriceAndOperationId(Integer priceId, Integer operationId);

    List<GoodsPromotionMultilingual> listByAdvertiseAccountId(Integer advertiseAccountId);

    Map<Integer, GoodsPromotionMultilingual> mapById(List<Integer> goodsMultilingualPromotionIds);

    GoodsPromotionMultilingual getByTgId(String tgId);

    List<GoodsPromotionMultilingual> listTgId(List<String> traditionalTiIds);

    void recoverBatchByIds(List<Integer> ids);
}
