package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.GoodsMultilingual;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPageForm;

import java.util.List;
import java.util.Map;

/**
 * 针对表【po_goods_ordinary(普通商品表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since  2025-05-07 09:50:47
 */
public interface IGoodsMultilingualService extends IService<GoodsMultilingual> {

    Page<GoodsMultilingual> pageInfo(GoodsPageForm pageForm);

    void logicRemoveById(Integer id);

    Map<Integer, GoodsMultilingual> mapNameByIds(List<Integer> goodsIds);

    List<GoodsMultilingual> listAll();

    List<Integer> listIdsByName(String goodsName);

    Map<Integer, GoodsMultilingual> mapById(List<Integer> goodsMultilingualIds);

    List<Integer> listIdByName(String goodsName, Integer productId);

    void recoverBatchByIds(List<Integer> ids);

}
