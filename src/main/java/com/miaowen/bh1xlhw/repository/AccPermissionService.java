package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.AccPermission;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_permission(权限表)】的数据库操作Service
 * @createDate 2025-05-06 15:41:28
 */
public interface AccPermissionService extends IService<AccPermission> {

    void logicRemoveByIds(Set<Integer> permissionIds);

    List<AccPermission> listExistByIds(Set<Integer> permissionIds);

    List<AccPermission> listExistAll();

    AccPermission getByPath(String path);
}
