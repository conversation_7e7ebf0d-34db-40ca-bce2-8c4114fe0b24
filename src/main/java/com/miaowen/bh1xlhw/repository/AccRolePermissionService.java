package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.AccRolePermission;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_role_permission(角色权限关联表)】的数据库操作Service
 * @createDate 2025-05-06 15:41:28
 */
public interface AccRolePermissionService extends IService<AccRolePermission> {

    void logicRemoveByPermissionIds(Set<Integer> permissionIds);

    void logicRemoveByRoleId(Integer roleId);

    List<AccRolePermission> listByRoleIds(Collection<Integer> roleIds);

}
