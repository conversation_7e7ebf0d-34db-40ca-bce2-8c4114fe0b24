package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.OperationManager;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerQryForm;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_operation_manager(运营主管)】的数据库操作Service
 * @createDate 2025-05-07 14:23:05
 */
public interface OperationManagerService extends IService<OperationManager> {

    void logicRemoveById(Integer id);

    void recoverById(Integer id);

    Page<OperationManager> pageInfo(OperationManagerQryForm pageForm);

    List<OperationManager> listExistByIds(Set<Integer> managerIds);

    Map<Integer,OperationManager> mapNameByIds(Set<Integer> managerIds);

    List<OperationManager> allExist();

    List<Integer> getIdsByManagerName(String operationManagerName, String operationManagerUserCode);

    OperationManager getByWorkNo(String operationManagerWorkNo);

}
