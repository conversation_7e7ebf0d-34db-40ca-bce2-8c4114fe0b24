package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Platform;
import com.miaowen.bh1xlhw.model.query.goods.PlatformPageForm;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_platform(平台管理)】的数据库操作Service
 * @createDate 2025-05-07 10:07:26
 */
public interface PlatformService extends IService<Platform> {
    void logicRemoveById(Integer id);

    void recoverById(Integer id);

    Page<Platform> pageInfo(PlatformPageForm pageForm);

    Platform getExistById(Integer platformId);

    List<Platform> listExistByIds(Collection<Integer> platformIds);

    Map<Integer, Platform> mapByIds(Collection<Integer> platformIds);

    Map<Integer, Platform> mapAll();

    List<Platform> allExist();

    Map<Integer, String> allExistMap();


}
