package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.Platform;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.operation.PlatformPageForm;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_platform(代理商)】的数据库操作Service
 * @createDate 2025-05-07 11:28:54
 */
public interface PlatformService extends IService<Platform> {

    void logicRemoveById(Integer id);

    void recoverById(Integer id);

    Page<Platform> pageInfo(PlatformPageForm pageForm);

    Map<Integer, Platform> mapByIds(List<Integer> platform);

    List<Platform> listAll();

    Map<Integer, Platform> allMap();

    Platform getExistById(Integer agentId);

    List<Platform> listExistByIds(List<Integer> agentIds);
}
