package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.bo.payment.AlipayConfigBO;
import com.miaowen.bh1xlhw.model.bo.payment.PaypalConfigBO;
import com.miaowen.bh1xlhw.model.bo.payment.StripeConfigBO;
import com.miaowen.bh1xlhw.model.bo.payment.WechatConfigBO;
import com.miaowen.bh1xlhw.model.entity.Merchant;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * @ClassName PoCurrencyService
 * @Description 针对表【po_system_currency(后台系统货币表)】的数据库操作Service
 * <AUTHOR>
 * @Date 2025/5/7 11:28
 */
public interface IMerchantService extends IService<Merchant> {
    Merchant getMerchantById(Integer merchantId);


    PaypalConfigBO getPaypalConfigById(Integer id);

    StripeConfigBO getStripeConfigById(Integer id);


    AlipayConfigBO getAliPayConfig(Integer id);

    WechatConfigBO getWechatPayConfig(Integer id);

    void logicDeleteByIds(List<Integer> ids);

    void recoverBatchByIds(List<Integer> ids);

}
