package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.AccUserRole;

import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【acc_user_role(用户角色关联表)】的数据库操作Service
 * @createDate 2025-05-06 15:41:28
 */
public interface AccUserRoleService extends IService<AccUserRole> {

    /**
     * 获取用户角色
     */
    List<AccUserRole> listByUserId(Integer userId);

    void logicRemoveByRoleId(Integer roleId);

    List<AccUserRole> getRolesByUserId(Integer userId);

}
