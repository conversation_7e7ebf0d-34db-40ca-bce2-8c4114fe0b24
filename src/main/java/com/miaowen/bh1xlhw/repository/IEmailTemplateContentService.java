package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.EmailTemplateContent;

import java.util.List;
import java.util.Map;


/**
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface IEmailTemplateContentService extends IService<EmailTemplateContent> {
    List<EmailTemplateContent> getByTemplateId(Integer id);

    Map<String, EmailTemplateContent> mapByTemplateId(Integer id);
}
