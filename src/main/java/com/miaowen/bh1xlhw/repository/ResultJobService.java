package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.ResultJob;
import com.miaowen.bh1xlhw.model.query.PageForm;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:51
 */
public interface ResultJobService {

    Page<ResultJob> getResultJobByTag(PageForm page, String tag, boolean isMulti);

    ResultJob getResultJobById(Integer id);

    Integer saveResultJob(ResultJob resultJob);

    void updateResultJob(ResultJob resultJob);

    void deleteResultJob(List<Integer> ids);

}
