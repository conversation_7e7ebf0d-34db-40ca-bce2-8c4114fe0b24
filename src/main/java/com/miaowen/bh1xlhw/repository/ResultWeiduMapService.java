package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.ResultWeiduMap;
import com.miaowen.bh1xlhw.model.query.PageForm;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:51
 */
public interface ResultWeiduMapService {

    Page<ResultWeiduMap> getByWeiduId(PageForm page, Integer weiduId);

    ResultWeiduMap getById(Integer id);

    Integer editOrSave(ResultWeiduMap resultWeiduMap);

    void delete(List<Integer> ids);

    void deleteByWeiduId(Integer weiduId);

}
