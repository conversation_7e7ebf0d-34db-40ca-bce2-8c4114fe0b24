package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.bo.oa.StatisticOrderBO;
import com.miaowen.bh1xlhw.model.entity.order.BaseOrder;
import com.miaowen.bh1xlhw.model.entity.order.Order;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;
import com.miaowen.bh1xlhw.model.query.order.OrderForm;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;


/**
 * 针对表【po_order(交易订单表)】的数据库操作Service
 *
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-13 14:20:44
 */
public interface IOrderService extends IService<Order> {

    void updateEmailStatus(Long orderId, String email);

    Page<Order> pageInfo(OrderForm orderForm);


    void dealRefundRecord(Integer sumRefundAmount, Integer status, BaseOrder order);

    List<StatisticOrderBO> statisticOrder(LambdaQueryWrapper<Order> wrapper);

    List<Order> selectByCondition(BrowseCountForm form);

    Order getByOutTradeNo(String outTradeNo);
    /**
     * 根据主订单号查询订单
     * @param outTradeNo
     * @return
     */
    List<Order> getByOutTradeNos(List<String> outTradeNo);

}
