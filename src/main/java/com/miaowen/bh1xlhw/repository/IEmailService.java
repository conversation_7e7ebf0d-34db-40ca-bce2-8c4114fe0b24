package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Email;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface IEmailService extends IService<Email> {
    void incrementSendNumbersById(Integer id);

    Email getGoogleEmail();

    List<Email> getEmailByIds(String emailIds);

    Map<Integer,Email> listExistByIds(Collection<Integer> emailIds);
}
