package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.ResultFurtherReading;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/19 14:34
 */
public interface ResultFurtherReadingService {

    List<ResultFurtherReading> getByTag(String tag,boolean isMulti);

    void deleteByTag(String tag,boolean isMulti);

    void saveEntity(ResultFurtherReading resultFurtherReading);

}
