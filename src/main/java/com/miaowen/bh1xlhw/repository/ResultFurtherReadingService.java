package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.ResultFurtherReading;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/19 14:34
 */
public interface ResultFurtherReadingService {

    List<ResultFurtherReading> getByTag(String tag,boolean isMulti);

    void deleteByTag(String tag,boolean isMulti);

    void saveEntity(ResultFurtherReading resultFurtherReading);

}
