package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Currency;

import java.math.BigDecimal;
import java.util.List;

import java.util.Map;

/**
 * @ClassName PoCurrencyService
 * @Description 针对表【po_system_currency(后台系统货币表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 11:28
 */
public interface ICurrencyService extends IService<Currency> {
    List<Currency> useListByIds(List<Integer> ids);

    void setRateById(Integer id, BigDecimal rate);



    Currency getByCurrency(String currency);

    Map<String,String> mapAll();

    void logicDeleteByIds(List<Integer> ids);

    void recoverBatchByIds( List<Integer> ids);
}
