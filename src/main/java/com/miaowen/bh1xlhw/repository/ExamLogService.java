package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.ExamLog;
import com.miaowen.bh1xlhw.model.query.logManagement.BrowseCountForm;

import java.util.List;

/**
 * @Description 做题日志持久层service
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/8 10:33
 */
public interface ExamLogService extends IService<ExamLog> {
    List<ExamLog> getByCondition(BrowseCountForm form);
}
