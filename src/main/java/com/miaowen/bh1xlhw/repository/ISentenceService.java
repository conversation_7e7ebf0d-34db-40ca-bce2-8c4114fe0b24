package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Sentence;

import java.util.List;

/**
 * @ClassName ILanguageService
 * @Description 针对表【po_system_language(后台系统货币表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @Date 2025/5/7 11:28
 */
public interface ISentenceService extends IService<Sentence> {

    List<Sentence> listAllSentence();
}
