package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Article;
import com.miaowen.bh1xlhw.model.query.PageForm;
import com.miaowen.bh1xlhw.model.query.article.ArticlePageForm;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 17:51
 */
public interface IArticleService extends IService<Article> {

    Page<Article> pageSort(ArticlePageForm page, boolean isMulti);


    void updateStatus(Integer id, Integer status);

    Map<Integer,Article> mapByIds(List<Integer> ids);
}
