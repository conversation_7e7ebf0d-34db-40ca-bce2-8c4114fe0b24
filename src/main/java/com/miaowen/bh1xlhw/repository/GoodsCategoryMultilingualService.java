package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.GoodsCategoryMultilingual;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryPageForm;

import java.util.List;
import java.util.Map;

/**
 * 针对表【po_goods_type_multilingual(商品类型表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-08 17:16:10
 */
public interface GoodsCategoryMultilingualService extends IService<GoodsCategoryMultilingual> {

    Page<GoodsCategoryMultilingual> pageInfo(GoodsCategoryPageForm pageForm);

    void logicRemoveById(Integer id);

    Map<Integer, GoodsCategoryMultilingual> mapByIds(List<Integer> goodsTypeIds);

    List<GoodsCategoryMultilingual> listAll();

    Map<Integer, GoodsCategoryMultilingual> mapNameByIds(List<Integer> goodsTypeIds);

    GoodsCategoryMultilingual getByType(String type);

}
