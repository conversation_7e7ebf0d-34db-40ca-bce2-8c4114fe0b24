package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.GoodsTraditional;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.goods.GoodsPageForm;

import java.util.List;
import java.util.Map;

/**
 * 针对表【po_goods_traditional(普通商品表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 15:13:26
 */
public interface IGoodsTraditionalService extends IService<GoodsTraditional> {

    Page<GoodsTraditional> pageInfo(GoodsPageForm pageForm);

    void logicRemoveById(Integer id);

    Map<Integer, GoodsTraditional> mapNameByIds(List<Integer> goodsIds);

    List<GoodsTraditional> listAll();

    List<Integer> listIdsByName(String goodsName);

    Map<Integer, GoodsTraditional> mapById(List<Integer> goodsTraditionalPromotionIds);

    List<Integer> listIdByName(String goodsName, Integer productId);

    void recoverBatchByIds(List<Integer> ids);
}
