package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.Price;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.operation.PricePageForm;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_price(价格方案表)】的数据库操作Service
 * @createDate 2025-05-07 16:56:57
 */
public interface IPriceService extends IService<Price> {

    void logicRemoveById(Integer id);

    void recoverById(Integer id);

    Page<Price> pageInfo(PricePageForm pageForm);

    Map<Integer, Price> mapByIds(List<Integer> priceIds);

    List<Price> listAll();


}
