package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.OperationUser;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.operation.OperationManagerQryForm;
import com.miaowen.bh1xlhw.model.query.operation.OperationUserQryForm;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @description 针对表【po_operation_user(运营人员表)】的数据库操作Service
 * @createDate 2025-05-07 14:23:05
 */
public interface OperationUserService extends IService<OperationUser> {

    void logicRemoveById(Integer id);

    void recoverById(Integer id);

    Page<OperationUser> pageInfo(OperationUserQryForm pageForm);

    Map<Integer, OperationUser> mapNameByIds(Collection<Integer> operationIds);

    List<OperationUser> allExist();

    List<OperationUser> allUser();


    OperationUser getByUserCode(String workNo);

    List<OperationUser> listByPid(Collection<Integer> managerUserIds);

    List<OperationUser> getIdsByName(String operationManagerWorkNo, String operationUserCode);

    List<Integer> getManagerIdsByName(String operationManagerWorkNo, String operationUserCode);

    Page<OperationUser> pageManagerInfo(OperationManagerQryForm pageForm);

    Map<Integer, OperationUser> allMap();

    List<OperationUser> allManagerExist();

}
