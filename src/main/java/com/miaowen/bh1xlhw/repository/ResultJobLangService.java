package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.ResultJobLang;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:52
 */
public interface ResultJobLangService {

    List<ResultJobLang> findByJobId(Integer JobId);

    void add(ResultJobLang resultJobLang);

    void deleteByJobId(Integer JobId);

    void deleteByJobIds(List<Integer> ids);

    void update(ResultJobLang resultJobLang);

}
