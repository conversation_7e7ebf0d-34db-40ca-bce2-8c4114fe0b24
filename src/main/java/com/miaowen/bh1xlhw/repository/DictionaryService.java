package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Dictionary;
import com.miaowen.bh1xlhw.model.query.system.DictionaryPageForm;

import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 */
public interface DictionaryService extends IService<Dictionary> {

    void logicBatchRemoveByTypeIds(List<Integer> typeIds);

    void logicBatchRemoveByIds(List<Integer> ids);

    Page<Dictionary> pageInfo(DictionaryPageForm form);

    List<Dictionary> listByTypeId(Integer id);

}
