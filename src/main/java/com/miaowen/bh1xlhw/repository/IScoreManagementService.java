package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.ScoreManagement;

import java.util.List;

/**
 * @Description
 * @Author：huanglong
 * @Date：2025/5/16 17:51
 */
public interface IScoreManagementService extends IService<ScoreManagement> {
    List<Integer> findByTag(String tag, Integer version, boolean isMulti);

    List<ScoreManagement> findByTagAndIsMulti(String tag, Integer version, boolean isMulti);
}
