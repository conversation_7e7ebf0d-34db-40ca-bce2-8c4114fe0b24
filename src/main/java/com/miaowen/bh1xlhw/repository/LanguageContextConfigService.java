package com.miaowen.bh1xlhw.repository;

import com.miaowen.bh1xlhw.model.entity.LanguageContextConfig;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.goods.GoodsLanguageForm;

import java.util.List;
import java.util.Map;

/**
 * 针对表【po_language_context_config(多语言配置表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-09 11:37:55
 */
public interface LanguageContextConfigService extends IService<LanguageContextConfig> {

    /**
     * 保存商品多语言
     */
    void saveBatchGoodsName(List<GoodsLanguageForm> goodsLanguageList, Integer relationId,  LanguageContextConfig.RelationType relationType);

    void logicRemoveByRelationId(Integer relationId, LanguageContextConfig.RelationType relationType);

    List<LanguageContextConfig> listByRelationIds(LanguageContextConfig.RelationType relationType, List<Integer> relationIds);

    Map<Integer, List<LanguageContextConfig>> mapByRelationIds(LanguageContextConfig.RelationType relationType,
                                                             List<Integer> relationIds);

    Map<String, LanguageContextConfig> mapByRelationIds(Integer relationType, Integer relationIds);
}
