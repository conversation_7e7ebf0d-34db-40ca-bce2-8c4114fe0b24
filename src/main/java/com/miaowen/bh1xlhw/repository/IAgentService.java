package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.Agent;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.operation.AgentPageForm;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
* 针对表【po_agent(代理商id)】的数据库操作Service
* <AUTHOR>
* @company 武汉秒闻网络科技有限公司
* @since 2025-06-23 21:55:59
*/
public interface IAgentService extends IService<Agent> {

    Map<Integer, Agent> mapByIds(Collection<Integer> agentIds);

    void logicDeleteById(Integer id);

    Page<Agent> pageInfo(AgentPageForm pageForm);

    List<Agent> listAll();
}
