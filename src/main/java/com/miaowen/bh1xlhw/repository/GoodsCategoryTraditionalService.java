package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.GoodsCategoryTraditional;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.goods.GoodsCategoryPageForm;

import java.util.List;
import java.util.Map;

/**
 * 针对表【po_goods_type_traditional(商品类型多语言表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 15:13:26
 */
public interface GoodsCategoryTraditionalService extends IService<GoodsCategoryTraditional> {

    Page<GoodsCategoryTraditional> pageInfo(GoodsCategoryPageForm pageForm);

    void logicRemoveById(Integer id);

    Map<Integer, GoodsCategoryTraditional> mapByIds(List<Integer> goodsTypeIds);

    List<GoodsCategoryTraditional> listAll();

    Map<Integer, GoodsCategoryTraditional> mapNameByIds(List<Integer> goodsTypeIds);

    GoodsCategoryTraditional getByType(String type);
}
