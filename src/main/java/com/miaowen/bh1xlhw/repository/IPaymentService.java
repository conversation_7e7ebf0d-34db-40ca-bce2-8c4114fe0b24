package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.SystemPayment;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface IPaymentService extends IService<SystemPayment> {

    void logicDelete(List<Integer> ids);

    void recoverBatchByIds(@Size(max = 500) List<Integer> ids);

}
