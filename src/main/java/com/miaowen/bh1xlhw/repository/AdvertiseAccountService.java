package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.miaowen.bh1xlhw.model.entity.AdvertiseAccount;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.query.goods.AdvertisePageForm;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 针对表【po_advertise_account(广告账户表)】的数据库操作Service
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 * @since 2025-05-12 16:22:34
 */
public interface AdvertiseAccountService extends IService<AdvertiseAccount> {


    void logicRemoveById(Integer id);

    Page<AdvertiseAccount> pageInfo(AdvertisePageForm pageForm, List<Integer> operationUserIds);

    AdvertiseAccount getExistById(Integer id);

    List<AdvertiseAccount> allList();

    List<AdvertiseAccount> listByOperationId(Integer operationId);

    List<AdvertiseAccount> listByOperationIds(Collection<Integer> operationUserSet);
}
