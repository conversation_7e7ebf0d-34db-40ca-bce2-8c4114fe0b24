package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.Country;

import java.util.List;

/**
 * @ClassName PoCurrencyService
 * @Description 针对表【po_system_cOUNTRY(后台系统货币表)】的数据库操作Service
 * <AUTHOR>
 * @Date 2025/5/7 11:28
 */
public interface ICountryService extends IService<Country> {
    List<Country> useListByIds(List<Integer> ids);

    List<Country> existAll();

}
