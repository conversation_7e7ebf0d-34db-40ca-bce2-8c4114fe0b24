package com.miaowen.bh1xlhw.repository;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.EmailOrderSuccess;


/**
 * 通知邮件记录
 *
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
@DS("log")
public interface IEmailOrderSuccessService extends IService<EmailOrderSuccess> {
    void save(String suffix, EmailOrderSuccess emailOrderSuccess);

}
