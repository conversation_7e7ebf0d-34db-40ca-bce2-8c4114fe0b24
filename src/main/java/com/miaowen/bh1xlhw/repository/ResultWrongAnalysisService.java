package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.model.entity.ResultWrongAnalysis;
import com.miaowen.bh1xlhw.model.query.goods.ResultSettingWrongDetailForm;

import java.util.List;

/**
 * @Description
 * @author：huanglong
 * @company 武汉秒闻网络科技有限公司
 * @Date：2025/5/16 10:22
 */
public interface ResultWrongAnalysisService extends IService<ResultWrongAnalysis> {

    List<ResultWrongAnalysis> getListByTag(String tag, boolean isMulti);

    List<ResultWrongAnalysis> findByExamNum(String tag, String examNum, boolean isMulti);

    void editExamAnswer(String tag, String examNum,int score, String answer,boolean isMulti);

    void deleteByExamNum(String tag, String examNum, boolean isMulti);

    public void editExamAnswerDetail(String tag, String examNum,String examAnswer,int score,String explain,String langType);
}
