package com.miaowen.bh1xlhw.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.miaowen.bh1xlhw.config.annotation.Sharding;
import com.miaowen.bh1xlhw.config.annotation.ShardingParam;
import com.miaowen.bh1xlhw.model.entity.EmailSendRecord;
import com.miaowen.bh1xlhw.model.query.email_template.EmailSendRecordForm;

import java.util.List;


/**
 * 通知邮件记录
 * @Description TODO
 * <AUTHOR>
 * @company 武汉秒闻网络科技有限公司
 */
public interface IEmailSendRecordService extends IService<EmailSendRecord> {
    boolean save(String suffix, EmailSendRecord entity);

    Page<EmailSendRecord> pageInfo(String suffix, EmailSendRecordForm pageForm, List<Integer> templateIds);

}
