<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.PoExchangeRateLogMapper">

    <select id="getLatestRateByCurrencyUnit" resultType="java.math.BigDecimal">
        select a.exchange_rate exchangeRate
        from po_exchange_rate_logs a
        where a.currency_unit = #{currencyUnit} order by a.update_time desc limit 1
    </select>


</mapper>