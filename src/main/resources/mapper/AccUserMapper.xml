<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.AccUserMapper">

    <select id="getAccUserByUserName" resultType="com.miaowen.bh1xlhw.model.entity.AccUser">
        select b.id,
               b.create_time,
               b.update_time,
               b.delete_time,
               b.username,
               b.email,
               b.password,
               b.status,
               b.real_name,
               b.phone,
               b.avatar
        from po_acc_user b
        where b.username = #{s}
    </select>
    <select id="getAccUserByEmail" resultType="com.miaowen.bh1xlhw.model.entity.AccUser">
        select b.id,
               b.create_time,
               b.update_time,
               b.delete_time,
               b.username,
               b.email,
               b.password,
               b.status,
               b.real_name,
               b.phone,
               b.avatar
        from po_acc_user b
        where b.email = #{s} and b.delete_time = 0 and b.delete_time = 0
    </select>
    <select id="getRolesByUserId" resultType="com.miaowen.bh1xlhw.model.entity.AccRole">
        select b.id,
               b.create_time,
               b.update_time,
               b.delete_time,
               b.name,
               b.description,
               b.is_super_admin,
               b.status
        from po_acc_user_role a left join po_acc_role b on a.role_id = b.id
        where a.user_id = #{userId} and b.status = '1' and a.delete_time = 0 and b.delete_time = 0
    </select>
    <select id="getAllSign" resultType="java.lang.String">
        select sign
        from po_acc_permission
        where sign <![CDATA[<>]]> '' and sign is not null and delete_time = 0
    </select>
    <select id="getPermissionSignByRoleId" resultType="java.lang.String">
        select b.sign
        from po_acc_role_permission a left join po_acc_permission b on a.permission_id = b.id
        where a.role_id = #{roleId} and a.delete_time = 0 and b.delete_time = 0
    </select>

</mapper>