CREATE TABLE `po_acc_permission`
(
    `id`          int(10) unsigned                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `title`       varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '菜单名称，按钮名称',
    `name`        varchar(30) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '名字',
    `path`        varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '路径',
    `component`   varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '组件',
    `sort`        int(11)                                 NOT NULL DEFAULT '0' COMMENT '排序值',
    `sign`        varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '权限（后台使用，做接口鉴权）',
    `icon`        varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '菜单图标',
    `pid`         int(11)                                 NOT NULL DEFAULT '0' COMMENT '父节点(对应当前表的所属父节点的ID)',
    `type`        tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '权限类型1一级菜单2二级菜单3按钮',
    `hidden`      tinyint(1)                              NOT NULL DEFAULT '1' COMMENT '页面状态0显示1隐藏',
    `create_time` timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='权限表';



CREATE TABLE `po_acc_role`
(
    `id`             int(10) unsigned                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`           varchar(30) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '角色名',
    `description`    varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '描述',
    `is_super_admin` tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '是否是超管0普通用户1超管',
    `status`         tinyint(4)                              NOT NULL DEFAULT '1' COMMENT '角色状态0停用1正常',
    `create_time`    timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`    int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='角色表';

CREATE TABLE `po_acc_role_permission`
(
    `id`            int(11)   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `role_id`       int(11)   NOT NULL COMMENT '角色id',
    `permission_id` int(11)   NOT NULL COMMENT '权限id',
    `create_time`   timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`   timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`   int(11)   NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC
    COMMENT ='角色权限关联表';


CREATE TABLE `po_acc_user`
(
    `id`            int(11)                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `username`      varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '用户名',
    `email`         varchar(30) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '邮箱',
    `password`      varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户密码',
    `password_rank` tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '密码强度',
    `status`        tinyint(1)                              NOT NULL DEFAULT '1' COMMENT '用户状态0停用1正常',
    `real_name`     varchar(30) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '真实姓名',
    `phone`         varchar(20) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '手机号',
    `avatar`        varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '头像',
    `create_time`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`   timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`   int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='后台用户表';

CREATE TABLE `po_acc_user_role`
(
    `id`          int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `user_id`     int(11)          NOT NULL COMMENT '用户id',
    `role_id`     int(11)          NOT NULL COMMENT '角色id',
    `create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)          NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='用户角色关联表';


CREATE TABLE `po_agents`
(
    `id`             int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `name`           varchar(100) COLLATE utf8mb4_unicode_ci         DEFAULT NULL COMMENT '合作公司名称',
    `settle_type`    tinyint(4)                             NOT NULL DEFAULT '1' COMMENT '结算方式1:月结,2:预付',
    `platform_id`    int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT '平台表的主键',
    `platform`       varchar(25) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '平台类型',
    `operation_type` tinyint(4)                             NOT NULL DEFAULT '1' COMMENT '运营方式1:自运营，2:代运营',
    `rebate_symbol`  tinyint(4)                             NOT NULL DEFAULT '1' COMMENT '返点正负号,1:+,0:-',
    `rebate`         decimal(10, 2)                         NOT NULL DEFAULT '0.00' COMMENT '返点',
    `create_time`    timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`    timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`    int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='代理商';


CREATE TABLE `po_system_dictionary`
(
    `id`                 int(11)                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`               text COLLATE utf8mb4_unicode_ci         NOT NULL COMMENT '名称',
    `code`               varchar(20) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '数据值',
    `description`        varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
    `dictionary_type_id` int(11)                                 NOT NULL DEFAULT '0' COMMENT '关联字典类型ID',
    `sort`               int(5)                                  NOT NULL DEFAULT '0' COMMENT '排序值',
    `status`             tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '状态 0禁用 1启用',
    `create_time`        timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`        timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`        int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `type_code_unq` (`code`, `dictionary_type_id`, `delete_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='字典表';

CREATE TABLE `po_system_dictionary_type`
(
    `id`          int(11)                                 NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(20) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '名称',
    `code`        varchar(20) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '编码',
    `description` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
    `sort`        int(5)                                  NOT NULL DEFAULT '0' COMMENT '排序值',
    `status`      tinyint(1)                              NOT NULL DEFAULT '0' COMMENT '状态 0禁用 1启用',
    `create_time` timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `code_unq` (`code`, `delete_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='字典类型表';

CREATE TABLE `po_domain`
(
    `id`          int(10) unsigned                        NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `domain`      varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '域名',
    `platform_id` int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '平台id',
    `create_time` timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='域名管理';


CREATE TABLE `po_domain_platform`
(
    `id`          int(10) unsigned NOT NULL COMMENT 'pk',
    `domain_id`   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '域名id',
    `tg_id`       int(10) unsigned NOT NULL DEFAULT '0' COMMENT '推广商品id',
    `create_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)          NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='记录域名平台使用情况';

CREATE TABLE `po_goods_type_multilingual`
(
    `id`          int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类名称',
    `type`        varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类标签',
    `create_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品类型多语言表';

CREATE TABLE `po_language_context_config`
(
    `id`            int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `relation_id`   int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT '关联id',
    `relation_type` tinyint(4)                             NOT NULL COMMENT '关联类型1,多语言商品',
    `language_code` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '语言code',
    `context`       text COLLATE utf8mb4_unicode_ci COMMENT '内容',
    `create_time`   timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`   timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`   int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='多语言配置表';

CREATE TABLE `po_operation_manager`
(
    `id`          int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `work_no`     varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工号',
    `name`        varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '主管名称',
    `create_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='运营主管';

CREATE TABLE `po_operation_user`
(
    `id`          int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `work_no`     varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '工号',
    `name`        varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '员工名称',
    `pid`         int(10) unsigned                       NOT NULL DEFAULT '0' COMMENT '领导id 对应运营主管表的用户id',
    `create_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='运营人员表';


CREATE TABLE `po_platform`
(
    `id`          int(10) unsigned                       NOT NULL AUTO_INCREMENT,
    `name`        varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '平台名称',
    `type`        varchar(25) COLLATE utf8mb4_unicode_ci          DEFAULT NULL COMMENT '平台类型Google,Facebook,TikTok',
    `source`      varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '来源',
    `create_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='平台管理';


CREATE TABLE `po_price`
(
    `id`                int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `name`              varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '价格方案名称',
    `type`              tinyint(4)                             NOT NULL DEFAULT '1' COMMENT '价格类型,1:单价,2:双价,3:3价',
    `price1`            int(11)                                NOT NULL DEFAULT '0' COMMENT '低档价格,美分单位',
    `origin_price1`     int(11)                                NOT NULL DEFAULT '0' COMMENT '低档划线价格,美分单位',
    `price2`            int(11)                                NOT NULL DEFAULT '0' COMMENT '中档价格,美分单位',
    `origin_price2`     int(11)                                NOT NULL DEFAULT '0' COMMENT '中档划线价格,美分单位',
    `price3`            int(11)                                NOT NULL DEFAULT '0' COMMENT '高档价格,美分单位',
    `origin_price3`     int(11)                                NOT NULL DEFAULT '0' COMMENT '高档划线价格,美分单位',
    `base_roi`          decimal(10, 2)                         NOT NULL DEFAULT '0.00' COMMENT 'roi基数',
    `target_roi`        decimal(10, 2)                         NOT NULL DEFAULT '0.00' COMMENT 'roi目标值',
    `red_package1`      int(11)                                NOT NULL DEFAULT '0' COMMENT '红包1',
    `red_package2`      int(11)                                NOT NULL DEFAULT '0' COMMENT '红包2',
    `red_package_back1` tinyint(4)                             NOT NULL DEFAULT '0' COMMENT '红包1是否回传',
    `red_package_back2` tinyint(4)                             NOT NULL DEFAULT '0' COMMENT '红包2是否回传',
    `email_back`        tinyint(4)                             NOT NULL DEFAULT '0' COMMENT '邮箱回传',
    `create_time`       timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`       timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`       int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='价格方案表';

CREATE TABLE `po_goods_type_traditional`
(
    `id`          int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`        varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类名称',
    `type`        varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类标签',
    `create_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time` int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='商品类型繁体表';

CREATE TABLE `po_goods_traditional`
(
    `id`                    int(10) unsigned                        NOT NULL AUTO_INCREMENT COMMENT '主键',
    `name`                  varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '中文名称',
    `goods_type_id`         int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '商品类型id',
    `paper_num`             int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '试卷题目数量',
    `style_name`            varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '风格名称',
    `web_package_name`      varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '前端包名',
    `price_id`              int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '价格方案id',
    `price_payment_channel` int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '原价支付通道,0表示默认',
    `red1_payment_channel`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '红包1支付通道,0表示默认',
    `red2_payment_channel`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '红包2支付通道,0表示默认',
    `benefit_info`          text COLLATE utf8mb4_unicode_ci COMMENT '商品权益信息',
    `create_time`           timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`           timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`           int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='繁体商品表';

CREATE TABLE `po_goods_payment`
(
    `id`           int(10) unsigned                       NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `goods_id`     int(11)                                NOT NULL DEFAULT '0' COMMENT '商品id',
    `payment_id`   int(11)                                NOT NULL DEFAULT '0' COMMENT '支付方式id',
    `payment_code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '支付方式类型',
    `create_time`  timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`  timestamp                              NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`  int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    UNIQUE KEY `goods_payment_enq` (`goods_id`, `payment_id`, `delete_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `po_goods_promotion_multilingual`
(
    `id`                    int(10) unsigned                        NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `tg_id`                 varchar(25) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '推广id',
    `application`           varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '用途',
    `goods_type_id`         int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '商品分类id',
    `goods_id`              int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '对应多语言商品id',
    `platform_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '平台id',
    `agent_id`              int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '代理商id',
    `advertise_account_id`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '广告账户id',
    `operation_manager_id`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '运营主管id',
    `operation_id`          int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '运营人员id',
    `domain_id`             int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '域名id',
    `promotion_status`      tinyint(4)                              NOT NULL DEFAULT '0' COMMENT '推广状态0否，1是',
    `price_id`              int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '价格配置id',
    `price_payment_channel` int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '原价支付通道,0表示默认',
    `red1_payment_channel`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '红包1支付通道,0表示默认',
    `red2_payment_channel`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '红包2支付通道,0表示默认',
    `advertise_config_info` text COLLATE utf8mb4_unicode_ci COMMENT '广告配置信息',
    `promotion_link`        varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推广链接',
    `create_time`           timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`           timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`           int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    UNIQUE KEY `tg_id_unq` (`tg_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='推广商品多语言';

CREATE TABLE `po_goods_promotion_traditional`
(
    `id`                    int(10) unsigned                        NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `tg_id`                 varchar(25) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '推广id',
    `application`           varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '用途',
    `goods_type_id`         int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '商品分类id',
    `goods_id`              int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '对应繁体商品id',
    `platform_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '平台id',
    `agent_id`              int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '代理商id',
    `advertise_account_id`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '广告账户id',
    `operation_manager_id`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '运营主管id',
    `operation_id`          int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '运营人员id',
    `domain_id`             int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '域名id',
    `promotion_status`      tinyint(4)                              NOT NULL DEFAULT '0' COMMENT '推广状态0否，1是',
    `price_id`              int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '价格配置id',
    `price_payment_channel` int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '原价支付通道,0表示默认',
    `red1_payment_channel`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '红包1支付通道,0表示默认',
    `red2_payment_channel`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '红包2支付通道,0表示默认',
    `advertise_config_info` text COLLATE utf8mb4_unicode_ci COMMENT '广告配置信息',
    `promotion_link`        varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '推广链接',
    `create_time`           timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`           timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`           int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`),
    UNIQUE KEY `tg_id_unq` (`tg_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='推广商品繁体';



create table po_advertise_account
(
    id                     int UNSIGNED auto_increment comment 'pk'
        primary key,
    name                   varchar(50)  default ''                not null comment '账户名称',
    platform_id            int          default 0                 not null comment '广告平台id',
    multilingual_goods_ids varchar(255) default ''                not null comment '多语言商品id列表',
    traditional_goods_ids  varchar(255) default ''                not null comment '繁体商品id列表',
    operation_id           int UNSIGNED default 0                 null comment '运营人员id',
    create_time            timestamp    default CURRENT_TIMESTAMP not null,
    update_time            timestamp    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP,
    delete_time            int          default 0                 not null
)
    comment '广告账户表';


CREATE TABLE `po_payment_channel`
(
    `id`                int(10) unsigned                        NOT NULL AUTO_INCREMENT COMMENT 'pk',
    `card_payment_id`   int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '支付方式id',
    `apple_payment_id`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '苹果支付支付方式id',
    `google_payment_id` int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '谷歌支付方式id',
    `sort`              int(11)                                 NOT NULL DEFAULT '0' COMMENT '排序',
    `default_status`    tinyint(4)                              NOT NULL COMMENT '默认状态 1默认,0非默认,所有记录只能又一个状态为1的',
    `image_url`         varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '预览图',
    `create_time`       timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`       timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`       int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='支付通道配置';


CREATE TABLE `po_order`
(
    `id`                 bigint(20) unsigned                     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `out_trade_no`       varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '内部唯一订单号',
    `out_trade_pay_no`   varchar(55) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '提供给三方的内部订单号在out_trade_no后拼一串随机数',
    `third_out_trade_no` varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '第三方订单号',
    `goods_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '商品id',
    `goods_type`         tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '商品类型多1,语言商品,2繁体商品',
    `log_id`             varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '临时用户id',
    `goods_promotion_id` int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '推广商品id',
    `platform_type`      varchar(15) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '投放平台类型',
    `agent_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '代理商id',
    `operation_user_id`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '运营人员id',
    `price_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '价格方案id',
    `source`             varchar(15) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '来源',
    `email`              varchar(254) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户邮箱',
    `amount`             int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '商品金额,单位分',
    `currency`           char(3) COLLATE utf8mb4_unicode_ci      NOT NULL DEFAULT 'USD' COMMENT '货币单位',
    `payment_amount`     int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '支付金额,单位分',
    `payment_currency`   char(3) COLLATE utf8mb4_unicode_ci      NOT NULL DEFAULT 'USD' COMMENT '支付货币类型',
    `country_code`       char(2) COLLATE utf8mb4_unicode_ci      NOT NULL DEFAULT 'US' COMMENT '国家代码',
    `payment_status`     tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '支付状态 0未支付 1已支付 2已退款',
    `payment_time`       timestamp                               NULL     DEFAULT NULL COMMENT '支付时间',
    `red_pack_use_type`  tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '红包使用类型 0原价 1红包1 2红包2',
    `red_pack_get_type`  tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '红包领取类型 0未领取 1红包1 2红包2',
    `merchant_id`        int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '收款的商户id',
    `payment_code`       varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '支付方式代码',
    `merchant_type_code` varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '商户类型code',
    `payment_type_id`    int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '支付方式id',
    `is_read_result`     tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '是否查看结果',
    `email_send_status`  tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '邮件发送状态,0失败,1成功',
    `card_number`        varchar(4) COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '' COMMENT '支付后四位卡号',
    `third_email`        varchar(254) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方订单邮件',
    `create_time`        timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`        timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`        int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_out_trade_no` (`out_trade_no`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='交易订单表';

CREATE TABLE `po_order_success`
(
    `id`                 bigint(20) unsigned                     NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `out_trade_no`       varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '内部唯一订单号',
    `out_trade_pay_no`   varchar(55) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '提供给三方的内部订单号在out_trade_no后拼一串随机数',
    `third_out_trade_no` varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '第三方订单号',
    `goods_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '商品id',
    `goods_type`         tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '商品类型多1,语言商品,2繁体商品',
    `log_id`             varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '临时用户id',
    `goods_promotion_id` int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '推广商品id',
    `platform_type`      varchar(15) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '投放平台类型',
    `agent_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '代理商id',
    `operation_user_id`  int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '运营人员id',
    `price_id`           int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '价格方案id',
    `source`             varchar(15) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '来源',
    `email`              varchar(254) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户邮箱',
    `amount`             int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '商品金额,单位分',
    `currency`           char(3) COLLATE utf8mb4_unicode_ci      NOT NULL DEFAULT 'USD' COMMENT '货币单位',
    `payment_amount`     int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '支付金额,单位分',
    `payment_currency`   char(3) COLLATE utf8mb4_unicode_ci      NOT NULL DEFAULT 'USD' COMMENT '支付货币类型',
    `country_code`       char(2) COLLATE utf8mb4_unicode_ci      NOT NULL DEFAULT 'US' COMMENT '国家代码',
    `payment_time`       timestamp                               NULL     DEFAULT NULL COMMENT '支付时间',
    `red_pack_use_type`  tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '红包使用类型 0原价 1红包1 2红包2',
    `red_pack_get_type`  tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '红包领取类型 0未领取 1红包1 2红包2',
    `merchant_id`        int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '收款的商户id',
    `payment_code`       varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '支付方式代码',
    `merchant_type_code` varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '商户类型code',
    `payment_type_id`    int(10) unsigned                        NOT NULL DEFAULT '0' COMMENT '支付方式id',
    `is_read_result`     tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '是否查看结果',
    `email_send_status`  tinyint(1) unsigned                     NOT NULL DEFAULT '0' COMMENT '邮件发送状态,0失败,1成功',
    `card_number`        varchar(4) COLLATE utf8mb4_unicode_ci   NOT NULL DEFAULT '' COMMENT '支付后四位卡号',
    `third_email`        varchar(254) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方订单邮件',
    `create_time`        timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`        timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`        int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uk_out_trade_no` (`out_trade_no`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='交易订单成功表';


CREATE TABLE `po_order_environmental`
(
    `id`                bigint(20)                             NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `log_id`            varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '临时用户id',
    -- 设备环境
    `browser_version`   VARCHAR(50)                            NOT NULL COMMENT '浏览器版本（示例：Chrome *********）',
    `user_agent`        TEXT                                   NOT NULL COMMENT '完整User-Agent',
    `device_model`      VARCHAR(50)                            NOT NULL COMMENT '设备型号（示例：Windows 10）',
    `screen_resolution` VARCHAR(15)                            NOT NULL COMMENT '屏幕分辨率（示例：2560x1440）',
    -- 网络信息
    `network_type`      VARCHAR(10)                            NOT NULL COMMENT '网络类型: 4G/5G/Wi-Fi',
    `ip_address`        VARCHAR(45)                            NOT NULL COMMENT '客户端IP（支持IPv6）',
    `ip_region`         VARCHAR(50)                            NOT NULL COMMENT 'IP归属地（示例：中国武汉）',
    -- 环境配置
    `language_code`     VARCHAR(5)                             NOT NULL COMMENT '用户语言（示例：CN）',
    `timezone`          VARCHAR(32)                            NOT NULL COMMENT '时区（示例：Asia/Shanghai）',
    `timezone_offset`   tinyint                                NOT NULL DEFAULT 8 COMMENT '时区偏移（单位：小时）',
    -- 业务信息
    `web_site`          VARCHAR(100)                           NOT NULL COMMENT '购买页面URL',
    `api_site`          VARCHAR(100)                           NOT NULL COMMENT '支付API站点URL',
    `create_time`       timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`       timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`       int(11)                                NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='交易订单环境表,交易订单的水平拆分表';


CREATE TABLE `po_order_question_result`
(
    `id`              bigint(20)                              NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `email`           varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户绑定email',
    `payment_status`  tinyint(4)                              NOT NULL DEFAULT '0' COMMENT '支付状态',
    `log_id`          varchar(50) COLLATE utf8mb4_unicode_ci  NOT NULL DEFAULT '' COMMENT '临时用户id',
    `result_question` text COLLATE utf8mb4_unicode_ci         NOT NULL COMMENT '答题列表,按顺序,分割id',
    `result_answer`   text COLLATE utf8mb4_unicode_ci         NOT NULL COMMENT '答题选项列表,按顺序,分割id',
    `completion_time` int(11)                                 NOT NULL DEFAULT 0 COMMENT '答题完成时间',
    `create_time`     timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time`     timestamp                               NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_time`     int(11)                                 NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci
  ROW_FORMAT = DYNAMIC COMMENT ='交易订单答题结果,交易订单的水平拆分表'

































