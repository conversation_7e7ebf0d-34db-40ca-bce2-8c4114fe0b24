

TRUNCATE `h1-xl-hw-v2`.po_system_country;

ALTER TABLE `h1-xl-hw-v2`.`po_system_country`
DROP COLUMN `is_deleted`,
MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `id`,
MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `create_time`;
ALTER TABLE `h1-xl-hw-v2`.`po_system_country`
    ADD UNIQUE INDEX `code_index`(`code`) USING BTREE;
INSERT INTO `h1-xl-hw-v2`.`po_system_country` (`id`, `delete_time`, `name`, `code`, `continent_code`, `sort`, `status`) VALUES (5, 0, '香港', 'HK', 'asia', 1, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_country` (`id`, `delete_time`, `name`, `code`, `continent_code`, `sort`, `status`) VALUES (6, 0, '美国', 'US', 'africa', 2, 1);
