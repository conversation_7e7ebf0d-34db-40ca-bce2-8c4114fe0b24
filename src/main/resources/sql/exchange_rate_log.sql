

ALTER TABLE `h1-xl-hw-v2`.`po_exchange_rate_logs`
DROP COLUMN `is_deleted`,
MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `id`,
MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `create_time`;

ALTER TABLE `h1-xl-hw-v2`.`po_exchange_rate_logs`
    ADD INDEX `currency_unit_rate_index`(`currency_unit`, `exchange_rate`) USING BTREE;

ALTER TABLE `h1-xl-hw-v2`.`po_exchange_rate_logs`
CHANGE COLUMN `currency_symbol` `currency_unit` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '货币单位' AFTER `delete_time`;