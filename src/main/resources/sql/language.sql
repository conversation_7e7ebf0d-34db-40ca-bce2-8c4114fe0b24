
TRUNCATE `h1-xl-hw-v2`.po_system_language;

ALTER TABLE `h1-xl-hw-v2`.`po_system_language`
DROP COLUMN `is_deleted`,
MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `create_time`,
MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `delete_time`;

INSERT INTO `h1-xl-hw-v2`.`po_system_language` (`id`,  `name`, `front`, `code`, `sort`, `is_default`, `status`) VALUES (11, '简体中文', 'Chinese', 'zh', 3, 0, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_language` (`id`, `name`, `front`, `code`, `sort`, `is_default`, `status`) VALUES (12,'英文', 'English', 'en', 2, 1, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_language` (`id`, `name`, `front`, `code`, `sort`, `is_default`, `status`) VALUES (13, '日语', 'Japanese', 'ja', 65, 0, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_language` (`id`, `name`, `front`, `code`, `sort`, `is_default`, `status`) VALUES (14, '韩语', 'Korean', 'ko', 65, 0, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_language` (`id`, `name`, `front`, `code`, `sort`, `is_default`, `status`) VALUES (15, '法语', 'French', 'fr', 65, 0, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_language` (`id`,  `name`, `front`, `code`, `sort`, `is_default`, `status`) VALUES (16,'德语', 'German', 'de', 33, 0, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_language` (`id`,  `name`, `front`, `code`, `sort`, `is_default`, `status`) VALUES (17,'繁体中文', '繁体中文', 'hk', 68, 0, 1);