ALTER TABLE `h1-xl-hw-v2`.`po_system_sentence`
MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `front_content`,
MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER `create_time`;


TRUNCATE `h1-xl-hw-v2`.po_system_sentence;
INSERT INTO `h1-xl-hw-v2`.`po_system_sentence` (`id`, `content`, `front_content`, `delete_time`, `sort`) VALUES (1, '%d天前', '%d day ago',  0, 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_sentence` (`id`, `content`, `front_content`,`delete_time`, `sort`) VALUES (2, '%d周前', '%d week ago', 0, 2);
INSERT INTO `h1-xl-hw-v2`.`po_system_sentence` (`id`, `content`, `front_content`, `delete_time`, `sort`) VALUES (3, '%d月前', '%d month ago', 0, 3);
INSERT INTO `h1-xl-hw-v2`.`po_system_sentence` (`id`, `content`, `front_content`,  `delete_time`, `sort`) VALUES (4, '%d年前', '%d years ago', 0, 4);
