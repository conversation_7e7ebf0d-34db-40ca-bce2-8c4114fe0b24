
TRUNCATE `h1-xl-hw-v2`.po_system_currency;

ALTER TABLE `h1-xl-hw-v2`.`po_system_currency`
DROP COLUMN `is_deleted`,
MODIFY COLUMN `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `id`,
MODIFY COLUMN `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP AFTER `create_time`;
ALTER TABLE `h1-xl-hw-v2`.`po_system_currency`
    ADD UNIQUE INDEX `currency_unit_index`(`currency_unit`) USING BTREE;
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`,  `delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (5,  0, '人民币', 'CN', 'CNY', '￥', 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`,  `delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (6,   0, '法国货币', 'FR', 'EUR', '€', 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`, `delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (7,  0, '英镑', 'GB', 'GBP', '£', 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`, `delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (8,  0, '日元', 'JP', 'JPY', '¥', 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`, `delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (9,  0, '美元', 'US', 'USD', '$', 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`,  `delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (10, 0, '港币', 'HK', 'HKD', '$', 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`, `delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (11,  0, '新加坡币', 'SG', 'SGD', 'S$', 1);
INSERT INTO `h1-xl-hw-v2`.`po_system_currency` (`id`,`delete_time`, `name`, `country_code`, `currency_unit`, `currency_symbol`, `status`) VALUES (12, 0, '台币', 'TW', 'TWD', 'NT$', 1);