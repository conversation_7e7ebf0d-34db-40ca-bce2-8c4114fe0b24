<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration  scan="true" scanPeriod="10 seconds">
    <contextName>B.H1-XL-HW.SERVER.V2</contextName>

    <!-- 引入默认的日志格式设置。里面包含CONSOLE的日志格式了 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />

    <!-- 自定义控制台输出，使用东八区时间 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
<!--    <include resource="org/springframework/boot/logging/logback/file-appender.xml" />-->

<!--    <logger name="org.apache.ibatis" level="DEBUG"/>-->
<!--    <logger name="java.sql.Connection" level="DEBUG"/>-->
<!--    <logger name="java.sql.Statement" level="DEBUG"/>-->
<!--    <logger name="java.sql.PreparedStatement" level="DEBUG"/>-->
    <logger name="org.po" level="DEBUG"/>

    <property name="log.path" value="runtime/logs/" />

    <!-- 设置全局时区为东八区 -->
    <property name="log.timezone" value="Asia/Shanghai" />

    <!-- 定义统一的日志格式模式，使用东八区时间 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS, Asia/Shanghai} %-5level [%thread] %logger{50} - %msg%n" />

    <!-- 文件输出 -->
    <appender name="WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 压缩文件存放路径，按年月日目录存放 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM,${log.timezone}}/%d{dd,${log.timezone}}-warn-%i.log</fileNamePattern>
            <!-- 单个文件大小限制为1MB -->
            <maxFileSize>3MB</maxFileSize>
            <!-- 保留天数 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小限制 -->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <!-- 此日志文件只记录ERROR级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 文件输出 -->
    <appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 压缩文件存放路径，按年月日目录存放 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM,${log.timezone}}/%d{dd,${log.timezone}}-info-%i.log</fileNamePattern>
            <!-- 单个文件大小限制为1MB -->
            <maxFileSize>3MB</maxFileSize>
            <!-- 保留天数 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小限制 -->
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
    </appender>

    <!-- 文件输出 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 压缩文件存放路径，按年月日目录存放 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM,${log.timezone}}/%d{dd,${log.timezone}}-error-%i.log</fileNamePattern>
            <!-- 单个文件大小限制为1MB -->
            <maxFileSize>3MB</maxFileSize>
            <!-- 保留天数 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小限制 -->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <!-- 此日志文件只记录ERROR级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 文件输出 -->
    <appender name="DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 压缩文件存放路径，按年月日目录存放 -->
            <fileNamePattern>${log.path}/%d{yyyy-MM,${log.timezone}}/%d{dd,${log.timezone}}-debug-%i.log</fileNamePattern>
            <!-- 单个文件大小限制为1MB -->
            <maxFileSize>3MB</maxFileSize>
            <!-- 保留天数 -->
            <maxHistory>30</maxHistory>
            <!-- 总大小限制 -->
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>UTF-8</charset> <!-- 此处设置字符集 -->
        </encoder>
        <!-- 此日志文件只记录ERROR级别的 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>DEBUG</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--
        root节点是必选节点，用来指定最基础的日志输出级别，只有一个level属性
        level:用来设置打印级别，大小写无关：TRACE, DEBUG, INFO, WARN, ERROR, ALL 和 OFF，默认是DEBUG
        可以包含零个或多个appender元素。
    -->
    <root level="INFO">
        <appender-ref ref="WARN" />
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="ERROR" />
        <appender-ref ref="INFO" />
        <appender-ref ref="DEBUG" />
    </root>
</configuration>