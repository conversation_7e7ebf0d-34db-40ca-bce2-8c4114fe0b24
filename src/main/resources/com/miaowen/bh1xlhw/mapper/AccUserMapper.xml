<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.AccUserMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.AccUser">
            <id property="id" column="id" />
            <result property="username" column="username" />
            <result property="email" column="email" />
            <result property="password" column="password" />
            <result property="passwordRank" column="password_rank" />
            <result property="status" column="status" />
            <result property="realName" column="real_name" />
            <result property="phone" column="phone" />
            <result property="avatar" column="avatar" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="deleteTime" column="delete_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,username,email,password,password_rank,status,
        real_name,phone,avatar,create_time,update_time,
        delete_time
    </sql>
</mapper>
