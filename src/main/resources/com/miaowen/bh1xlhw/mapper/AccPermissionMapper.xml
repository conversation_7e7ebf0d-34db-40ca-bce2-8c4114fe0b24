<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.AccPermissionMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.AccPermission">
            <id property="id" column="id" />
            <result property="title" column="title" />
            <result property="name" column="name" />
            <result property="path" column="path" />
            <result property="component" column="component" />
            <result property="sort" column="sort" />
            <result property="sign" column="sign" />
            <result property="icon" column="icon" />
            <result property="pid" column="pid" />
            <result property="type" column="type" />
            <result property="hidden" column="hidden" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="deleteTime" column="delete_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,title,name,path,component,sort,
        sign,icon,pid,type,hidden,
        create_time,update_time,delete_time
    </sql>
</mapper>
