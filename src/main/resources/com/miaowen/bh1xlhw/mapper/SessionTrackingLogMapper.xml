<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.SessionTrackingLogMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.SessionTrackingLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="log_id" property="logId" jdbcType="VARCHAR"/>
        <result column="device_fingerprint" property="deviceFingerprint" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="env_type" property="envType" jdbcType="INTEGER"/>
        <result column="tuid" property="tuid" jdbcType="VARCHAR"/>
        <result column="platform_code" property="platformCode" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="INTEGER"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="pay_status" property="payStatus" jdbcType="TINYINT"/>
        <result column="pay_amount" property="payAmount" jdbcType="VARCHAR"/>
        <result column="total_amount" property="totalAmount" jdbcType="VARCHAR"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="ip_address" property="ipAddress" jdbcType="VARCHAR"/>
        <result column="trace_id" property="traceId" jdbcType="VARCHAR"/>
        <result column="source" property="source" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , log_id, device_fingerprint, order_no, env_type, tuid, platform_code,
        product_id, product_name, pay_status, pay_amount, total_amount, ip,
        ip_address, trace_id, source, create_time, update_time, delete_time
    </sql>


</mapper>