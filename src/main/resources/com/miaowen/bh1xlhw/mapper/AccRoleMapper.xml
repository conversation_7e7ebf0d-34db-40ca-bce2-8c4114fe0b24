<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.AccRoleMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.AccRole">
            <id property="id" column="id" />
            <result property="name" column="name" />
            <result property="description" column="description" />
            <result property="isSuperAdmin" column="is_super_admin" />
            <result property="status" column="status" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="deleteTime" column="delete_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,name,description,is_super_admin,status,create_time,
        update_time,delete_time
    </sql>
</mapper>
