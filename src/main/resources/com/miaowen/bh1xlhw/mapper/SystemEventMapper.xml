<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.SystemEventMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.SystemEvent">
            <id property="id" column="id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="deleteTime" column="delete_time" />
            <result property="name" column="name" />
            <result property="code" column="code" />
            <result property="typeCode" column="type_code" />
            <result property="sort" column="sort" />
            <result property="status" column="status" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_time,update_time,delete_time,name,code,type_code,sort,status
    </sql>
</mapper>
