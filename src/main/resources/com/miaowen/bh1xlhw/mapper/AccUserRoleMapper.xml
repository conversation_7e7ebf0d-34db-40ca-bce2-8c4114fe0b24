<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.AccUserRoleMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.AccUserRole">
            <id property="id" column="id" />
            <result property="userId" column="user_id" />
            <result property="roleId" column="role_id" />
            <result property="createTime" column="create_time" />
            <result property="updateTime" column="update_time" />
            <result property="deleteTime" column="delete_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,role_id,create_time,update_time,delete_time
    </sql>
</mapper>
