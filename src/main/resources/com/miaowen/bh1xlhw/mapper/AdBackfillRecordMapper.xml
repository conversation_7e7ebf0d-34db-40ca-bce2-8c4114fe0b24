<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.AdBackfillRecordMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.AdBackfillRecord">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="log_id" property="logId" jdbcType="VARCHAR"/>
        <result column="order_id" property="orderId" jdbcType="VARCHAR"/>
        <result column="order_no" property="orderNo" jdbcType="VARCHAR"/>
        <result column="tuid" property="tuid" jdbcType="VARCHAR"/>
        <result column="trace_id" property="traceId" jdbcType="VARCHAR"/>
        <result column="tg_id" property="tgId" jdbcType="INTEGER"/>
        <result column="product_id" property="productId" jdbcType="INTEGER"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="event_type" property="eventType" jdbcType="VARCHAR"/>
        <result column="pay_status" property="payStatus" jdbcType="TINYINT"/>
        <result column="pay_amount" property="payAmount" jdbcType="VARCHAR"/>
        <result column="order_time" property="orderTime" jdbcType="TIMESTAMP"/>
        <result column="page_type" property="pageType" jdbcType="TINYINT"/>
        <result column="platform_code" property="platformCode" jdbcType="VARCHAR"/>
        <result column="front_url" property="frontUrl" jdbcType="VARCHAR"/>
        <result column="params" property="params" jdbcType="VARCHAR"/>
        <result column="send_status" property="sendStatus" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, log_id, order_id, order_no, tuid, trace_id, tg_id, product_id, product_name,
        event_type, pay_status, pay_amount, order_time, page_type, platform_code,
        front_url, params, send_status, status, create_time, update_time, delete_time
    </sql>


</mapper>