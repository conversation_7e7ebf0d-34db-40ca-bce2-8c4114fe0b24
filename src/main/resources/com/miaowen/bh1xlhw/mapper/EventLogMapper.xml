<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.EventLogMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.EventLog">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="log_id" property="logId" jdbcType="VARCHAR"/>
        <result column="event_code" property="eventCode" jdbcType="VARCHAR"/>
        <result column="event_name" property="eventName" jdbcType="VARCHAR"/>
        <result column="platform_code" property="platformCode" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="delete_time" property="deleteTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, log_id, event_code, event_name,platform_code, create_time, update_time, delete_time
    </sql>

</mapper>