<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.miaowen.bh1xlhw.mapper.ExamLogMapper">

    <resultMap id="BaseResultMap" type="com.miaowen.bh1xlhw.model.entity.ExamLog">
        <id property="id" column="id" jdbcType="INTEGER"/>
        <result property="logId" column="log_id" jdbcType="VARCHAR"/>
        <result property="deviceFingerprint" column="device_fingerprint" jdbcType="VARCHAR"/>
        <result property="examId" column="exam_id" jdbcType="VARCHAR"/>
        <result property="tuid" column="tuid" jdbcType="VARCHAR"/>
        <result property="platformCode" column="platform_code" jdbcType="VARCHAR"/>
        <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
        <result property="productId" column="product_id" jdbcType="INTEGER"/>
        <result property="productName" column="product_name" jdbcType="VARCHAR"/>
        <result property="questionsNum" column="questions_num" jdbcType="INTEGER"/>
        <result property="examNum" column="exam_num" jdbcType="INTEGER"/>
        <result property="examSchedule" column="exam_schedule" jdbcType="VARCHAR"/>
        <result property="payStatus" column="pay_status" jdbcType="TINYINT"/>
        <result property="payAmount" column="pay_amount" jdbcType="VARCHAR"/>
        <result property="totalAmount" column="total_amount" jdbcType="VARCHAR"/>
        <result property="ip" column="ip" jdbcType="VARCHAR"/>
        <result property="ipAddress" column="ip_address" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="deleteTime" column="delete_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , log_id,device_fingerprint, exam_id, tuid, platform_code, order_no, product_id, product_name,
        questions_num, exam_num, exam_schedule, pay_status, pay_amount, total_amount,
        ip, ip_address, create_time, update_time, delete_time
    </sql>


</mapper>